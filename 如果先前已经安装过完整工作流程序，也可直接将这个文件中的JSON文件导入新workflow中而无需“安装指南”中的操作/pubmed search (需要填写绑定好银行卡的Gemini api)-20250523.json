{"name": "pubmed search (需要填写绑定好银行卡的Gemini api)", "nodes": [{"parameters": {"public": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [2160, 2500], "id": "b96b47cd-9243-4f41-9b91-9ae592ca3533", "name": "When chat message received", "webhookId": "80f68507-a094-4b0a-b170-7998b1d1a9af"}, {"parameters": {"jsCode": "// 创建CSV内容\nconst issns = $input.item.json.issns;\nconst csvHeader = \"ISSN\";\nconst csvRows = issns.join('\\n');\nconst csvContent = `${csvHeader}\\n${csvRows}`;\n\n// 创建文件名\nconst outputFilename = $input.item.json.outputFilename;\nconst csvFilename = `${outputFilename}.csv`;\n\n// 保存查询语句\nconst query = $input.item.json.originalQuery;\nconst queryFilename = `${outputFilename}_core_query.txt`;\n\n// 转换CSV内容为二进制\nconst binaryData = {\n  data: Buffer.from(csvContent).toString('base64'),\n  mimeType: 'text/csv',\n  fileName: csvFilename\n};\n\nreturn {\n  json: {\n    issns,\n    csvContent,\n    csvFilename,\n    queryContent: query,\n    queryFilename,\n    issnsCount: $input.item.json.issnsCount\n  },\n  binary: {\n    data: binaryData\n  }\n};"}, "id": "cd3a2a30-d093-45e4-ad35-30b05a160431", "name": "准备CSV", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [2340, 2820]}, {"parameters": {"promptType": "define", "text": "=\"pubmed_search_instruction\": {**避免在开头出现“```pubmed\\”诸如此类的废话！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！直接给我我可以直接复制到pubmed检索框上进行检索的检索式就好而无需其他任何废话！！**\n    \"description\": \"根据用户的中文或英文口头描述（{{ $('When chat message received').item.json.chatInput }}），自动创建准确、完整、标准化、英文且所有检索单元需加括号的PubMed检索式。**不要理会我是否添加了JCR、中科院分区或者影响因子等限定词，只需要给我完整详细具体准确的不添加分区或者影响因子的pubmed检索式就好**\"\"**只输出pubmed的标准检索式，而不需要其他任何废话（**比如要避免在开头出现“```pubmed\\”诸如此类的废话**，而是直接给我检索式就行了）**\",\n    \"rules\": [\n      \"无论用户使用的是中文还是英文描述，生成的检索式必须为英文。\",\n      \"对于需要精确匹配的多词短语，应使用双引号包围，如\\\"myocardial infarction\\\"[Title/Abstract]。\",\n      \"每个独立的检索单元（关键词、作者、排除条件、日期范围、文献类型、语言、过滤器、MeSH Terms）必须使用括号包裹，例如(\\\"lung cancer\\\"[Title/Abstract])。\",\n      \"作者名需以英文拼音格式呈现，例如用户提及'张伟'，则生成为(zhang wei[Author])。\",\n      \"关键词检索默认使用[Title/Abstract]字段，除非用户明确指定其他字段。\",\n      \"使用布尔逻辑符：AND、OR、NOT连接关键词、作者或排除项，每个逻辑运算单元都必须使用括号包裹，例如((\\\"diabetes\\\"[Title/Abstract]) AND (\\\"hypertension\\\"[Title/Abstract]))。\",\n      \"使用截词符号*进行词干扩展，例如(diagnos*[Title/Abstract])可匹配diagnosis、diagnostic、diagnose等。\",\n      \"日期区间的检索使用冒号连接起止年份，例如(2020:2024[dp])。\",\n      \"根据用户提到的文献类型进行限定，例如(Review[Publication Type])、(Meta-Analysis[Publication Type])、(Randomized Controlled Trial[Publication Type])。\",\n      \"语言限定使用(english[Language])。\",\n      \"免费全文限定使用(free full text[Filter])。\",\n      \"必要时使用医学主题词(MeSH Terms)进行检索，例如(prognosis[MeSH Terms])。\",\n      \"用户明确要求排除的内容，使用NOT明确排除，并且排除内容也必须用括号，例如NOT (animal*[Title])。\",\n      \"作者名需以英文拼音格式呈现，例如用户提及'张伟'，则生成为(zhang wei[Author])。\",\n\"对于中文姓名，特别注意多字符名的处理：姓和名之间用空格分隔，但如果名由多个汉字组成（如'雪楠'），对应的拼音应当连写，不加空格。例如'庄雪楠'应转换为(zhuang xuenan[Author])，而非(zhuang xue nan[Author])。这是因为PubMed中多字符名通常作为一个整体登记，分隔可能导致搜索失败。\",\n      \"在处理相对时间描述时（例如'近半年'、'近一年'、'last 5 years'），请务必基于当前日期 ({{ $json.currentDateInfo }})进行计算，并生成正确的 (YYYY/MM/DD:YYYY/MM/DD[dp]) 或 (YYYY:YYYY[dp]) 格式的日期范围。\",\n      \"例如，如果用户说'近半年'，且当前日期是 {{ $json.currentDateInfo }}，你需要计算出大约半年前的日期作为起始日期。\"\n    ],\n    \"examples\": [\n      {\n        \"description\": \"检索作者李明2020年后的文献\",\n        \"query\": \"(li ming[Author]) AND (2020:2024[dp])\" （**注意时间的公式中间上千万不要加入\\\"这种斜杠、引号等，但是两侧是可以加引号的，不然检索不出来！！**）\n      },\n      {\n        \"description\": \"查找有关糖尿病和高血压的英文综述文献\",\n        \"query\": \"((\\\"diabetes\\\"[Title/Abstract]) AND (\\\"hypertension\\\"[Title/Abstract])) AND (review[Publication Type]) AND (english[Language])\"\n      },\n      {\n        \"description\": \"检索不包括动物实验的心肌梗死诊断相关随机对照试验\",\n        \"query\": \"((\\\"myocardial infarction\\\"[Title/Abstract]) AND (diagnos*[Title/Abstract])) AND (randomized controlled trial[Publication Type]) NOT ((animal*[Title]) OR (rat*[Title]) OR (mouse*[Title]))\"\n      },\n      {\n        \"description\": \"查找乳腺癌免疫疗法的系统综述，要求免费全文，英文，近5年\",\n        \"query\": \"(\\\"breast cancer\\\"[MeSH Terms]) AND (immunotherapy[MeSH Terms]) AND (systematic review[Publication Type]) AND (english[Language]) AND (free full text[Filter]) AND (2019:2024[dp])\"\n      },\n      {\n        \"description\": \"肺癌治疗但排除化疗或放疗的文献\",\n        \"query\": \"((\\\"lung cancer\\\"[Title/Abstract]) AND (therapy[Title/Abstract])) NOT ((chemotherapy[Title]) OR (radiotherapy[Title]))\"\n      },\n      {\n        \"description\": \"冠状动脉疾病的诊断或治疗相关文章\",\n        \"query\": \"(\\\"coronary artery disease\\\"[Title/Abstract]) AND ((diagnos*[Title/Abstract]) OR (therap*[Title/Abstract]))\"\n      },\n      {\n        \"description\": \"近五年骨质疏松治疗的英文综述类文献\",\n        \"query\": \"(osteoporosis[Title/Abstract]) AND (therapy[MeSH Terms]) AND (review[Publication Type]) AND (english[Language]) AND (2019:2024[dp])\"\n      }\n    ]\n  }\n\n**注意：只需要给我检索式即可，而不需要其他任何文字说明；而且不要理会我是否添加了JCR、中科院分区或者影响因子等限定词，只需要给我完整详细具体准确的不添加分区或者影响因子的pubmed检索式就好**\n\n**绝对禁止：**\n1.  **严禁**在生成的 PubMed 检索式中包含任何与 **JCR 分区、中科院分区、影响因子 (IF)、引用次数、H-index 或任何其他期刊评价指标** 相关的信息。这些信息在 PubMed 的标准检索语法中是**无效**的。\n2.  如果用户的请求中提到了这些指标（例如：“影响因子大于3”、“JCR Q1”、“高分期刊”、“引用超过100次”等），**必须完全忽略这些部分**，如同用户从未提及一样。**不要试图**将它们以任何形式（包括错误的字段标签如 `[Journal Impact Factor]`）加入到检索式中。\n3.  生成的检索式**只能包含** PubMed 支持的标准字段（如 `[Title/Abstract]`, `[MeSH Terms]`, `[Author]`, `[dp]`, `[Publication Type]`, `[Language]`, `[Filter]` 等）以及正确的布尔运算符和括号。\n\n**重要说明：** 此步骤生成的检索式将直接提交给 PubMed API。任何期刊指标的筛选将在后续步骤中通过**其他工具**完成，而不是在 PubMed 检索式本身中进行。因此，此检索式必须保持纯粹，只关注主题、作者、日期、文献类型等核心检索要素。\n\n\"negative_constraint_examples\": [\n  {\n    \"user_request\": \"查找影响因子大于5的糖尿病文献\",\n    \"incorrect_output_example\": \"((\\\"diabetes\\\"[Title/Abstract]) AND (\\\"5\\\"[Journal Impact Factor]:\\\"*\\\"[Journal Impact Factor]))\",\n    \"correct_output\": \"(\\\"diabetes\\\"[Title/Abstract])\",\n    \"reasoning\": \"错误！PubMed 检索式本身不能包含影响因子字段。AI 必须忽略用户请求中的 '影响因子大于5' 部分，只根据核心主题 '糖尿病' 生成检索式。\"\n  },\n  {\n    \"user_request\": \"检索3分以上的近3个月来的nAMD眼底影像学的文章\",\n    \"incorrect_output_example\": \"((\\\"nAMD\\\"[Title/Abstract] OR \\\"neovascular age-related macular degeneration\\\"[Title/Abstract]) AND (\\\"fundus imaging\\\"[Title/Abstract])) AND (YYYY/MM/DD:YYYY/MM/DD[dp]) AND (\\\"3\\\"[Journal Impact Factor]:\\\"10\\\"[Journal Impact Factor]))\",\n    \"correct_output\": \"((\\\"nAMD\\\"[Title/Abstract] OR \\\"neovascular age-related macular degeneration\\\"[Title/Abstract]) AND (\\\"fundus imaging\\\"[Title/Abstract] OR \\\"retinal imaging\\\"[Title/Abstract] OR \\\"optical coherence tomography\\\"[Title/Abstract])) AND (YYYY/MM/DD:YYYY/MM/DD[dp])\",\n    \"reasoning\": \"错误！包含了无效的影响因子字段 '[Journal Impact Factor]'。AI 必须忽略用户请求中的 '3分以上' 部分，只根据主题词 'nAMD', '眼底影像学' 和时间范围 '近3个月'（需要计算具体日期）生成有效的 PubMed 检索式。\"\n  },\n   {\n    \"user_request\": \"查找中科院1区的癌症研究\",\n    \"incorrect_output_example\": \"((\\\"cancer\\\"[Title/Abstract]) AND (\\\"1\\\"[CAS Zone]))\",\n    \"correct_output\": \"(\\\"cancer\\\"[Title/Abstract] OR \\\"neoplasms\\\"[MeSH Terms])\",\n    \"reasoning\": \"错误！PubMed 检索式不能包含中科院分区 '[CAS Zone]'。AI 必须忽略 '中科院1区'，只生成关于 '癌症研究' 的检索式。\"\n  },\n   {\n   \"user_request\": \"查找关于阿尔茨海默病治疗的 JCR Q1 区文献\",\n   \"incorrect_output_example\": \"((\\\"Alzheimer disease\\\"[MeSH Terms]) AND (therapy[MeSH Terms])) AND (\\\"Q1\\\"[JCR Zone])\",\n   \"correct_output\": \"((\\\"Alzheimer disease\\\"[MeSH Terms]) AND (therapy[MeSH Terms] OR \\\"drug therapy\\\"[MeSH Terms]))\",\n   \"reasoning\": \"错误！PubMed 检索式中不应包含 '[JCR Zone]' 或任何 JCR 分区信息。这些指标对于 PubMed 检索语法是无效的。AI 必须完全忽略用户请求中的 'JCR Q1 区' 部分，仅根据核心主题 '阿尔茨海默病治疗' 生成有效的 PubMed 检索式。\"\n }\n]", "hasOutputParser": true, "options": {}}, "id": "aa079e5d-430f-4cc6-b8b0-6475305d6c81", "name": "生成PubMed检索式", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [2560, 2500]}, {"parameters": {"url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "db", "value": "pubmed"}, {"name": "retmode", "value": "xml"}, {"name": "rettype", "value": "abstract"}, {"name": "retmax", "value": "10000"}, {"name": "WebEnv", "value": "={{ $('Esearch PMID1').item.json.esearchresult.webenv }}"}, {"name": "query_key", "value": "={{ $('Esearch PMID1').item.json.esearchresult.querykey }}"}]}, "options": {}}, "id": "ada87fa7-af08-4a34-865a-43f9a4ca444c", "name": "Efetch Abstract1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3120, 2500]}, {"parameters": {"options": {}}, "id": "dfb1c241-5cc7-4232-906d-dc7d6e29bb9f", "name": "Parse EFetch XML1", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [3320, 2500]}, {"parameters": {"fieldToSplitOut": "PubmedArticleSet.PubmedArticle", "options": {}}, "id": "776d17d1-4979-4525-926f-5ff83b3a3dce", "name": "Split Articles1", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [3520, 2500]}, {"parameters": {"jsCode": "// 合并所有文章的ISSN\nconst items = $input.all();\nconst issnList = [];\n\n// 收集所有ISSN\nfor (const item of items) {\n  if (item.json.issn && item.json.issn !== 'N/A' && item.json.issn !== null) {\n    issnList.push(item.json.issn);\n  }\n}\n\n// 去重\nconst uniqueIssnList = [...new Set(issnList)];\n\n// 获取时间戳和输出文件名（添加时分秒）\nconst now = new Date();\nconst year = now.getFullYear();\nconst month = String(now.getMonth() + 1).padStart(2, '0');\nconst day = String(now.getDate()).padStart(2, '0');\nconst hours = String(now.getHours()).padStart(2, '0');\nconst minutes = String(now.getMinutes()).padStart(2, '0');\nconst seconds = String(now.getSeconds()).padStart(2, '0');\nconst timestamp = `${year}${month}${day}_${hours}${minutes}${seconds}`;\nconst outputFilename = `retrieved_issns(${timestamp})`;\n\n// 原始查询\n// 注意: 这里修改为从生成PubMed检索式节点获取，或者创建一个默认值\nconst originalQuery = $node[\"生成PubMed检索式\"]?.json?.output || \"No query available\";\n\n// 返回结果\nreturn {\n  issns: uniqueIssnList,\n  issnsCount: uniqueIssnList.length,\n  timestamp,\n  outputFilename,\n  originalQuery\n};"}, "id": "efaf28f8-68a9-4785-a342-20b066f4cb9c", "name": "提取并去重ISSN", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [2180, 2820]}, {"parameters": {"command": "=cd /data/pubmed-search/NO_DELETE && python pubmed_if_ISSN.py \"/data/pubmed-search/{{ $json.csvFilename }}\" "}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2840, 2820], "id": "2ab76c70-8fdf-49f9-a57a-588dbe0cc37c", "name": "生成information.csv"}, {"parameters": {"operation": "write", "fileName": "=/data/pubmed-search/{{ $json.csvFilename }}", "options": {"append": false}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2500, 2820], "id": "9cf85233-e98a-4175-8ff8-8cfb63abaac8", "name": "保存retrieved_issns(YYYYMMDD_HHMMSS).csv"}, {"parameters": {"operation": "write", "fileName": "=/data/pubmed-search/{{ $('准备CSV').item.json.queryFilename }}", "options": {"append": false}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2660, 2820], "id": "a3387a9d-7abf-4a5d-9699-ad847a114eb5", "name": "保存core_query.txt"}, {"parameters": {"command": "=cd /data/pubmed-search/NO_DELETE && python filter_issn_to_pubmed_query.py \"/data/pubmed-search/{{ $('重述检索要求').item.json.informationFilename }}\" {{ $json.output }}"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3700, 2820], "id": "39944d4b-33a3-43fb-8cb2-66908c28f661", "name": "生成information.md"}, {"parameters": {"fileSelector": "=/data/pubmed-search/{{ $('生成information.md文件名').item.json.extractedMarkdownFilename }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2360, 3140], "id": "0fa29549-71be-471b-ad89-897dfe62e536", "name": "Read/Write Files from Disk"}, {"parameters": {"method": "POST", "url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi", "sendBody": true, "contentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "db", "value": "pubmed"}, {"name": "retmode", "value": "json"}, {"name": "retmax", "value": "500"}, {"name": "usehistory", "value": "y"}, {"name": "term", "value": "={{ $json.text }} NOT ((\"Erratum\"[Title]) OR (\\\"Correction\\\"[Title]) OR (\\\"Corrigendum\\\"[Title]) OR (\\\"Retraction\\\"[Title]) OR (\\\"Withdrawal\\\"[Title]) OR (Corrigendum[Title]) OR (Erratum[Title]) OR (\\\"Retraction Notice\\\"[Title]) OR (Retracted Publication[Publication Type]) OR (\\\"Expression of Concern\\\"[Title]) OR (Letter[Publication Type]) OR (Editorial[Publication Type]) OR (Comment[Publication Type]) OR (News[Publication Type]) OR (Meeting Abstract[Publication Type]) OR (\\\"Conference Abstract\\\"[Title]) OR (Obituary[Publication Type]) OR (Bibliography[Publication Type]))"}]}, "options": {}}, "id": "8b4dbbd8-2016-4c60-a4fb-a369bac92f52", "name": "Esearch PMID", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2720, 3140]}, {"parameters": {"url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "db", "value": "pubmed"}, {"name": "retmode", "value": "xml"}, {"name": "rettype", "value": "abstract"}, {"name": "retmax", "value": "500"}, {"name": "WebEnv", "value": "={{ $('Esearch PMID').item.json.esearchresult.webenv }}"}, {"name": "query_key", "value": "={{ $('Esearch PMID').item.json.esearchresult.querykey }}"}]}, "options": {}}, "id": "bad92256-2f6f-4b57-838f-44e5bcc80289", "name": "Efetch Abstract", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2880, 3140]}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// 获取上一步传入的单篇文章数据\nconst articleJson = $json;\n\n// --- 提取 PMID ---\nlet pmid = null;\ntry {\n    const pmidSource = articleJson.MedlineCitation?.PMID;\n    pmid = pmidSource?._ ?? pmidSource ?? null;\n} catch (error) {\n    console.error(`!!! Error extracting PMID for item ${$itemIndex}:`, error);\n}\n\n// --- 提取 Title ---\nlet title = null;\ntry {\n    const titleSource = articleJson.MedlineCitation?.Article?.ArticleTitle;\n    title = titleSource?._ ?? titleSource ?? null;\n    if (typeof title === 'object' && title !== null && title._) {\n        title = title._;\n    }\n    // else if (typeof title === 'object' && title !== null) { title = null; } // 或者尝试 title.toString()\n} catch (error) {\n    console.error(`!!! Error extracting Title for item ${$itemIndex}:`, error);\n}\n\n// --- 提取 Abstract ---\nlet abstractText = null;\ntry {\n  const abstractDataSource = articleJson.MedlineCitation?.Article?.Abstract?.AbstractText;\n  if (abstractDataSource) {\n    if (Array.isArray(abstractDataSource)) {\n      abstractText = abstractDataSource.map(item => {\n        let textPart = '';\n        if (typeof item === 'object' && item !== null) {\n          const label = item.Label;\n          const text = item._;\n          textPart = (label ? `[${label}] ` : '') + (text || '');\n        } else if (typeof item === 'string') {\n          textPart = item;\n        }\n        return textPart;\n      }).filter(s => s).join('\\n\\n');\n    } else if (typeof abstractDataSource === 'string') {\n      abstractText = abstractDataSource;\n    } else if (typeof abstractDataSource === 'object' && abstractDataSource !== null) {\n       const label = abstractDataSource.Label;\n       const text = abstractDataSource._;\n       abstractText = (label ? `[${label}] ` : '') + (text || '');\n    }\n  }\n} catch (error) {\n  console.error(`!!! Error extracting abstract for item ${$itemIndex}:`, error);\n}\n\n// --- 提取作者相关信息 ---\nlet firstAuthorName = null;\nlet firstAuthorAffiliation = null;\nlet correspondingAuthorName = null;\ntry {\n  const authors = articleJson.MedlineCitation?.Article?.AuthorList?.Author;\n  if (Array.isArray(authors) && authors.length > 0) {\n    const firstAuthor = authors[0];\n    if (firstAuthor) {\n      const fName = firstAuthor.ForeName || '';\n      const lName = firstAuthor.LastName || '';\n      firstAuthorName = `${fName} ${lName}`.trim() || null;\n      const firstAffInfo = Array.isArray(firstAuthor.AffiliationInfo) ? firstAuthor.AffiliationInfo[0] : firstAuthor.AffiliationInfo;\n      if (firstAffInfo?.Affiliation) {\n          firstAuthorAffiliation = firstAffInfo.Affiliation._ ?? firstAffInfo.Affiliation ?? null;\n      }\n    }\n    const lastAuthor = authors[authors.length - 1];\n    if (lastAuthor) {\n      if (lastAuthor.CollectiveName) {\n         correspondingAuthorName = lastAuthor.CollectiveName._ ?? lastAuthor.CollectiveName ?? null;\n      } else {\n         const fName = lastAuthor.ForeName || '';\n         const lName = lastAuthor.LastName || '';\n         correspondingAuthorName = `${fName} ${lName}`.trim() || null;\n      }\n    }\n  }\n} catch (error) {\n  console.error(`!!! Error extracting author info for item ${$itemIndex}:`, error);\n}\n\n// --- 提取杂志名称 ---\nlet journalName = null;\ntry {\n    journalName = articleJson.MedlineCitation?.Article?.Journal?.Title;\n} catch(error) {\n    console.error(`!!! Error extracting journal name for item ${$itemIndex}:`, error);\n}\n\n// +++ 提取 ISSN +++\nlet issn = null;\ntry {\n    const issnSource = articleJson.MedlineCitation?.Article?.Journal?.ISSN;\n    issn = issnSource?._ ?? issnSource ?? null;\n    if (typeof issn === 'object' && issn !== null) {\n        issn = null;\n    }\n} catch (error) {\n    console.error(`!!! Error extracting ISSN for item ${$itemIndex}:`, error);\n}\n\n// --- 提取并格式化发表日期 (修改后 - 优先级: 正式完整 > Epub完整 > 正式年月 > Epub年月 > 年份) ---\nlet formattedPubDate = null;\n\n// Helper function to format date parts\nfunction formatDate(year, month, day) {\n    // Validate Year\n    const cleanYear = year ? String(year).match(/^\\d{4}$/) ? String(year) : null : null;\n    if (!cleanYear) return null; // Year is mandatory\n\n    // Validate and Format Month\n    let cleanMonth = null;\n    if (month) {\n        const monthMap = {\n            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',\n            'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'\n        };\n        let monthNumber = monthMap[month] || month; // Allow numeric months too\n        if (/^\\d$/.test(monthNumber)) { // Single digit month\n            monthNumber = '0' + monthNumber;\n        }\n        if (/^(0[1-9]|1[0-2])$/.test(monthNumber)) { // Valid 01-12\n             cleanMonth = monthNumber;\n        }\n    }\n\n    // Validate and Format Day\n    let cleanDay = null;\n    if (day) {\n       let dayNumber = String(day);\n        if (/^\\d$/.test(dayNumber)) { // Single digit day\n            dayNumber = '0' + dayNumber;\n        }\n        // Basic validation (doesn't check days per month rigorously)\n        if (/^(0[1-9]|[12]\\d|3[01])$/.test(dayNumber)) { // Valid 01-31\n             cleanDay = dayNumber;\n        }\n    }\n\n    // Return formatted date based on available parts\n    if (cleanYear && cleanMonth && cleanDay) {\n        return `${cleanYear}-${cleanMonth}-${cleanDay}`; // YYYY-MM-DD\n    } else if (cleanYear && cleanMonth) {\n        return `${cleanYear}-${cleanMonth}`; // YYYY-MM\n    } else if (cleanYear) {\n        return cleanYear; // YYYY\n    } else {\n        return null;\n    }\n}\n\ntry { // Start of the main try-catch for date extraction\n    // --- Priority 1: Try Formal Publication Date (JournalIssue PubDate) ---\n    const pubDateObj = articleJson.MedlineCitation?.Article?.Journal?.JournalIssue?.PubDate;\n    let formalYear = null, formalMonth = null, formalDay = null;\n    let formalDateFormatted = null;\n\n    if (pubDateObj) {\n        formalYear = pubDateObj.Year?._ ?? pubDateObj.Year ?? null;\n        formalMonth = pubDateObj.Month?._ ?? pubDateObj.Month ?? null;\n        formalDay = pubDateObj.Day?._ ?? pubDateObj.Day ?? null;\n\n        // Try formatting full formal date first\n        formalDateFormatted = formatDate(formalYear, formalMonth, formalDay);\n        // If not full, try partial formal date\n        if (!formalDateFormatted || formalDateFormatted.length < 10) {\n             formalDateFormatted = formatDate(formalYear, formalMonth, null) || formatDate(formalYear, null, null);\n        }\n        // Assign the best formal date found so far\n        formattedPubDate = formalDateFormatted;\n    }\n\n    // --- Priority 2: Try Electronic Publication Date (ArticleDate) ---\n    // Only proceed if we didn't get a full YYYY-MM-DD formal date\n    if (!formattedPubDate || formattedPubDate.length < 10) {\n        const articleDates = articleJson.MedlineCitation?.Article?.ArticleDate;\n        let epubDateObj = null;\n\n        if (Array.isArray(articleDates)) {\n            // Find the date with DateType=\"Electronic\"\n            epubDateObj = articleDates.find(d => d?.DateType === 'Electronic');\n        } else if (typeof articleDates === 'object' && articleDates !== null && articleDates.DateType === 'Electronic') {\n            // Handle case where ArticleDate is a single object\n            epubDateObj = articleDates;\n        }\n\n        if (epubDateObj) {\n            const epubYear = epubDateObj.Year?._ ?? epubDateObj.Year ?? null;\n            const epubMonth = epubDateObj.Month?._ ?? epubDateObj.Month ?? null; // Epub Month might be numeric\n            const epubDay = epubDateObj.Day?._ ?? epubDateObj.Day ?? null;\n\n            const epubFormattedFull = formatDate(epubYear, epubMonth, epubDay);\n\n            if (epubFormattedFull && epubFormattedFull.length === 10) { // Found full YYYY-MM-DD Epub\n                formattedPubDate = epubFormattedFull; // Overwrite previous partial/null date\n            } else if (!formattedPubDate) { // Only use partial Epub if no formal date was found AT ALL\n                 const epubFormattedPartial = formatDate(epubYear, epubMonth, null) || formatDate(epubYear, null, null);\n                 if (epubFormattedPartial) {\n                     formattedPubDate = epubFormattedPartial; // Use partial epub only if nothing else exists\n                 }\n            }\n             // Implicitly: If we already had a partial formal date (like YYYY-MM), we prefer that over a partial Epub date.\n        }\n    }\n     // At this point, formattedPubDate holds the best available date according to the rules, or null\n\n} catch (error) { // End of the main try-catch for date extraction\n    console.error(`!!! Error extracting/formatting publication date for item ${$itemIndex}:`, error);\n    formattedPubDate = null; // Ensure null on error\n} // <<<<<<< Ensure this closing brace matches the try block above\n\n\n// --- 格式化最终的作者信息字段 ---\nlet authorsCombined = `First: ${firstAuthorName || 'N/A'}`;\nif (correspondingAuthorName && correspondingAuthorName !== firstAuthorName) {\n    authorsCombined += `; Corresponding: ${correspondingAuthorName}`;\n} else if (!correspondingAuthorName && firstAuthorName) {\n     authorsCombined += `; Corresponding: N/A`;\n} else if (!firstAuthorName && correspondingAuthorName) {\n     authorsCombined = `First: N/A; Corresponding: ${correspondingAuthorName}`;\n} else if (!firstAuthorName && !correspondingAuthorName) {\n     authorsCombined = `First: N/A; Corresponding: N/A`;\n}\n\n\n// --- 返回最终结果对象 ---\nreturn {\n  pmid: pmid,\n  title: title,\n  journal: journalName,\n  issn: issn,\n  authors: authorsCombined,\n  first_affiliation: firstAuthorAffiliation,\n  abstract: abstractText,\n  publication_date: formattedPubDate // Contains prioritized date (YYYY-MM-DD, YYYY-MM, YYYY) or null\n};"}, "id": "76c086eb-2f1c-4132-9231-61d347c830be", "name": "Process Article Data1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3380, 3140]}, {"parameters": {"options": {}}, "id": "1cc580c4-0b24-4b1a-82fc-60574646a696", "name": "Parse EFetch XML2", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [3040, 3140]}, {"parameters": {"fieldToSplitOut": "PubmedArticleSet.PubmedArticle", "options": {}}, "id": "61e6cef7-af55-4b49-910c-e0e935a3315e", "name": "Split Articles2", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [3220, 3140]}, {"parameters": {"operation": "write", "fileName": "=/data/pubmed-search/{{ $json.outputFilename }}", "options": {"append": false}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2360, 3440], "id": "b9835f7a-a0de-4420-a9dd-cfd4cf940aa0", "name": "保存article.csv"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-05-06", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2560, 2680], "id": "0a6f5f2d-c874-48ca-ae66-ee3943685f07", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "TiSWtXYB9RrxYtQx", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3180, 3000], "id": "968b2c76-984a-43df-a55b-5118f2bb809d", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "TiSWtXYB9RrxYtQx", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3540, 3340], "id": "4bf3371f-c412-4c64-bb65-9e6ee3955663", "name": "Google Gemini Chat Model3", "credentials": {"googlePalmApi": {"id": "TiSWtXYB9RrxYtQx", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-05-06", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3240, 3600], "id": "2c006a64-6e3f-45fb-aaf2-9d39677a4576", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "TiSWtXYB9RrxYtQx", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"jsCode": "// 获取上一步 AI Agent 输出的综述内容\nconst inputItem = $input.first();\nconst reviewContent = (inputItem && inputItem.json && typeof inputItem.json.output === 'string')\n                      ? inputItem.json.output\n                      : \"\";\n\nconsole.log(`[准备综述-二进制] 获取到的综述内容长度: ${reviewContent.length}`);\nif (reviewContent === \"\") {\n  console.warn(\"[准备综述-二进制] 警告：未能从上一步获取有效的综述内容 (json.output)。\");\n}\n\n// --- 时间戳和文件名生成 (保持不变) ---\nconst now = new Date();\nconst year = now.getFullYear();\nconst month = String(now.getMonth() + 1).padStart(2, '0');\nconst day = String(now.getDate()).padStart(2, '0');\nconst hours = String(now.getHours()).padStart(2, '0');\nconst minutes = String(now.getMinutes()).padStart(2, '0');\nconst seconds = String(now.getSeconds()).padStart(2, '0');\nconst timestamp = `${year}${month}${day}_${hours}${minutes}${seconds}`;\n\nconst filename = `retrieved_issns(${timestamp})_review.md`;\nconst filePath = `/data/pubmed-search/${filename}`;\nconsole.log(`[准备综述-二进制] 准备文件名: ${filename}, 完整路径: ${filePath}`);\n\n// --- 关键修改：将文本内容转换为二进制数据 (Base64) ---\nconst base64Data = Buffer.from(reviewContent, 'utf8').toString('base64');\n\n// 构建符合 Read/Write Files 自动处理的 binary 输出结构\nconst binaryData = {\n  data: base64Data,         // Base64 编码的字符串\n  fileName: filename,       // 文件名（虽然 Read/Write Files 节点会用 filePathToSave）\n  mimeType: 'text/markdown' // 指定 MIME 类型\n};\n\n// 返回结果，包含 json 部分（用于路径）和 binary 部分（用于内容）\nreturn {\n  json: {\n    filePathToSave: filePath // Read/Write Files 节点将使用这个路径\n    // contentToSave 字段不再需要传给下一个节点\n  },\n  binary: {\n    // 注意这里的结构是 { data: binaryDataObject }\n    data: binaryData\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2180, 3720], "id": "92ebe277-06aa-42a5-b8c6-e27fd0720e7a", "name": "准备综述文件名和内容"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-05-06", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3580, 3600], "id": "dd2fc3b1-c765-4dda-b878-14a8a2a55489", "name": "Google Gemini Chat Model4", "credentials": {"googlePalmApi": {"id": "TiSWtXYB9RrxYtQx", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "=# 角色：医学文献结构化提炼与规划师\n\n# 任务：\n请**深度分析**以下提供的多篇医学文献摘要（{{ $json.combined_summary }}）。基于对这些摘要**内容和内在逻辑**的彻底理解：\n1.  **识别核心议题和信息结构**，用于构建综述的**核心主体部分**。这些主体部分的结构和标题应**直接反映**摘要内容。\n2.  **构思一个完整的中文综述写作大纲（高级框架）**。此大纲**必须**包含以下组成部分：\n    *   **引言 (Introduction)**: 作为文章的开端。\n    *   **核心主体部分**: 根据第1步分析得出的、反映摘要内容的核心议题和逻辑结构的部分（可以有多个）。\n    *   **结论与展望 (Conclusion and Outlook)**: 作为文章的结尾（或根据内容调整为“总结”、“讨论与结论”等合适的收尾标题）。\n3.  为该大纲的**每一个部分**（包括引言、所有核心主体部分、结论与展望），根据其在摘要中的内容支撑度或其在综述结构中的功能重要性，**规划一个建议的近似字数**。\n4.  大纲的撰写主旨要尽量基于用户的检索要求的主题（{{ $json.original_query }}）（比如说检索某个人的文献，则综述要围绕这个人来撰写），但可以基于摘要结果进行大纲思路扩展\n\n# 核心原则：\n*   **主体内容驱动**: 综述的**核心主体结构**和标题应**完全源自**对摘要内容的分析和提炼。\n*   **结构完整**: 大纲需包含标准的引言和结论部分。\n*   **逻辑连贯**: 各部分（包括引言、主体、结论）顺序需体现内容的内在联系和标准的论述流程。\n*   **字数反映权重**: 建议字数需反映各部分预期承担的内容量和重要性。\n\n# **输出要求 (极其重要):**\n*   **你的输出必须严格地、仅仅包含最终构思出的大纲结构及其字数规划。**\n*   格式为**列表或简单层级结构**，清晰列出**所有部分标题**（必须包含引言和结论）。\n*   **每个标题后紧跟括号，内含建议的近似字数**。例如：\n    *   引言 (建议约 XXX 字)\n    *   [根据摘要提炼的核心主体标题A] (建议约 YYY 字)\n    *   [根据摘要提炼的核心主体标题B] (建议约 ZZZ 字)\n    *   结论与展望 (建议约 WWW 字)\n*   **绝对禁止包含任何形式的引导语、解释、理由、说明、总结或其他任何非大纲本身的内容。直接开始输出第一个大纲标题（引言）。**", "hasOutputParser": true, "options": {}}, "id": "443ef94c-029b-4bb7-a915-3c5a3203f598", "name": "综述框架agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [3240, 3440]}, {"parameters": {"promptType": "define", "text": "=# 角色：医学综述报告撰写专家\n# 任务：\n请以提供的 **综述写作大纲 ({{ $json.output }})** 作为高级结构指导，并 **充分利用** 提供的 **多篇医学文献摘要 ({{ $('整合摘要&提取检索要求').item.json.combined_summary }})** 中的信息，撰写一篇逻辑严谨、结构清晰、内容完整的 **中文医学领域综述文章**。\n\n# 撰写要求：\n1.  **遵循大纲结构与处理标题:**\n    *   文章的**主要章节结构**必须与给定的大纲标题顺序一致。\n    *   在生成 Markdown 文章的章节标题时，请**使用**大纲中提供的标题文本，但**必须省略**括号及其内部的字数建议（即，忽略 \"(建议约 XXX 字)\" 这部分）。\n    *   为这些主要的章节标题**添加顺序编号**。通常从\"引言\"开始编号为\"1.\"，后续核心主体章节依次编号\"2.\"、\"3.\"... 直到最后的\"结论与展望\"部分。请使用 Markdown 的二级标题格式（`##`）来展示这些带编号的章节标题。例如：`## 1. 引言`，`## 2. [核心主体标题A]`，`## 3. [核心主体标题B]`， ... ，`## N. 结论与展望`。（注意：文章的总标题通常使用一级标题 `#` 且不带编号）。\n2.  **参考字数规划 (内部使用):** 在撰写每个部分时，请**内部参考**大纲中提供的**建议字数**来把握内容的详略，但**不要**在最终输出的任何地方（包括标题或正文）提及这些字数。内容的逻辑完整性和表达清晰度是首要标准。\n3.  **内容基于摘要:** 所有论点、分析和结论都应基于提供的文献摘要内容，进行整合、比较和分析。\n4.  **标准综述格式:** 按照标准的医学综述文章格式进行撰写。\n5.  **段落格式要求:**\n    *   **段落完整连续:** 每个自然段内部不需要额外分点列项，需写成连续、完整的段落文字。\n    *   **段首缩进:** 综述正文中的**每一个自然段**（不包括标题、列表项、代码块等）的开头，请**务必**添加**两个全角空格**（`　　`）以实现段首缩进。\n6.  **规范引用:** 在正文中引用相关文献摘要内容时，必须在相应位置通过上标数字明确标注参考文献的序号。请 **按顺序** 为你引用的摘要分配序号（例如 [1], [2],[3]...）。\n7.  **AMA格式参考文献列表要求:**\n    *   在文章末尾，需要添加\"参考文献\"标题（作为二级标题），其下面是参考文献列表。\n    *   **严格遵循修改后的AMA格式**：\n        - 使用阿拉伯数字（不带括号）作为序号，后跟句点和空格\n        - 作者姓名格式：姓氏 名字首字母，**只列出前三位作者**，后面用 \"et al.\" 替代\n        - 文章标题不用任何引号，句首字母大写，其余小写\n        - 期刊名称使用标准缩写，并用*斜体*标识（在Markdown中用*包围）\n        - 出版信息格式：年份;卷号(期号):页码\n        - 在每个参考文献末尾添加 **PMID: XXXXXXXXX**（其中X代表文献的PubMed ID）\n        - 每个参考文献条目必须独立成行\n    *   示例格式：\n        ```\n        1. Smith J, Johnson A, Lee C, et al. Title of the article with sentence case. *J Med Res*. 2023;15(3):123-135. PMID: 12345678\n        ```\n8.  **语言与逻辑:** 确保文章语言流畅、专业、准确，逻辑关系清晰，论证有力。\n9. **增强自然连贯性与逻辑流畅:**\n   * **概念递进:** 通过相关概念的自然递进和展开来连接句子，而非依赖明显的连接词。\n   * **语义关联:** 利用相关词汇、同义复述和概念关联来维持文本连贯性，使语义流动更为自然。\n   * **上下文衔接:** 确保每个新句子明确地从前一句子的信息或概念中延伸发展。\n   * **结构变化:** 混合使用不同句式结构（长短句搭配、主动被动转换）以创造节奏变化，避免机械单调感。\n   * **论点链接:** 在阐述相关观点时，通过内容本身的关联性建立连接，而非依赖公式化的过渡标记。\n   * **隐性过渡:** 使用隐含的逻辑关系和情境引导来替代显性的过渡词，让文本流动更自然。\n   * **思路连贯展开:** 确保文章整体遵循清晰但不机械的思路发展脉络，让读者能够跟随自然的思维进程。\n   * **避免套路化过渡:** 句子之间和段落之间要自然过渡，但减少使用过于明显的过渡词（如\"此外\"、\"然而\"等），转而采用更自然、多样化的语句连接方式。\n\n# **输出要求 (极其重要):**\n1.  **最终结果必须保存为 Markdown (.md) 格式**。\n2.  **你的输出必须严格地、仅仅包含完整的 Markdown 格式综述文章本身。**\n3.  **绝对禁止在 Markdown 文章内容之前或之后添加任何形式的引导语、解释、说明、确认信息、代码块标记说明（如 \"```markdown\" 这种文字本身）或任何其他非文章正文的内容。**\n4.  **你的输出应该直接以文章的第一个字符（通常是总标题的 '#' 或第一个字）开始，并以文章的最后一个字符（通常是参考文献列表的末尾）结束。**", "hasOutputParser": true, "options": {}}, "id": "11476cfc-d9c4-41bc-91a4-680cdf489232", "name": "综述写作agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [3560, 3440]}, {"parameters": {"promptType": "define", "text": "=[!!!] 输出格式铁律：不要以**“```json\\n”开头！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！**你的回答必须从第一个字符 { 开始，到最后一个字符 } 结束，只需要直接输出“ { 开始，到最后一个字符 } ”这个内容，其他所有废话都不要（包括最开始也不需要```json\\n这种字符，而只需要从“{”开始）。不允许在此范围之外包含任何文字、解释、说明、引导语、代码标记、换行符或任何其他非 JSON 内容。任何偏离此格式的输出都将被视为完全失败。\n现在，请扮演一位专业的医学文献信息整理专家。你的任务是基于下面提供的单篇文献的详细原始信息，提取、总结，并严格按照下面指定的 JSON 结构来填充内容，然后直接输出这个 JSON 对象。\n提供的文献原始信息:\n摘要 (Abstract): “{{ $json.abstract }}”\n请提取、总结并严格按以下 JSON 结构输出 (键名和值都必须是中文):\n{\n\"期刊名称\": \"（根据\\\"{{ $json.journal }}\\\"填写英文期刊名称）\",\n\"ISSN\": \"（根据\\\"{{ $json.issn }}\\\"填写有效 ISSN）\",\n\"PMID\": \"（根据\\\"{{ $json.pmid }}\\\"填写 PMID）\",\n\"文章标题\": \"（根据\\\"{{ $json.title }}\\\"填写文章标题，保留英文原文）\",\n\"第一作者\": \"（从\\\"{{ $json.authors }}\\\"提取 First Author，保留英文）\",\n\"通讯作者\": \"（从\\\"{{ $json.authors }}\\\"提取 Corresponding Author，保留英文）\",\n\"第一作者单位\": \"（根据\\\"{{ $json.first_affiliation }}\\\"填写第一作者单位，英文需翻译成中文）\",\n\"发表时间\": \"（根据\\\"{{ $json.publication_date }}\\\"填写 YYYY-MM-DD 格式日期）\",\n\"研究目的\": \"（根据“摘要 (Abstract)”中文总结的研究目的）\",\n\"研究类型\": \"（根据“摘要 (Abstract)”中文描述的研究类型）\",\n\"研究方法\": \"（根据“摘要 (Abstract)”中文描述的研究方法）\",\n\"研究对象\": \"（根据“摘要 (Abstract)”中文描述的研究对象）\",\n\"主要研究结果\": \"（根据摘要 (Abstract)中文总结的核心结果）\",\n\"研究结论与意义\": \"（根据“摘要 (Abstract)”中文总结的结论和意义）\",\n\"研究亮点或创新点\": \"（根据“摘要 (Abstract)”中文提炼的亮点，多点用分号';'隔开）\"\n}\n关键约束条件（辅助性，核心要求见开头铁律）:\n完整结构: JSON 必须包含上述所有 14 个中文键。\n内容准确: 提取信息需准确，单位按要求翻译。\n中文键值: 键和值均为中文（除非特别说明保留英文）。\n键名精确: 中文键名需与示例完全一致。\n----------------------------------------------------\n再次警示：绝对禁止的输出形式 (Negative Examples):\n任何引导性文字: 这是..., Here is...\n任何代码块标记: ```json ... ```\n任何解释或结尾文字: {...} 希望..., The provided completion...\n任何非 JSON 格式的文本: 如第一个 item 那种带 **...** 的摘要。\n----------------------------------------------------\n[!!!] 最终检查指令：请在生成回答前最后检查一遍，确保你的输出严格遵守本提示最开头的【输出格式铁律】。直接输出纯净的 JSON 对象。\n\n示例说明：\n✅ 正确格式示例 (从第一个字符开始就是 \"{\" 并以 \"}\" 结束):\n{\n\"期刊名称\": \"Nature Medicine\",\n\"ISSN\": \"1078-8956\",\n\"PMID\": \"36150431\",\n\"文章标题\": \"SARS-CoV-2 vaccination and myocarditis in a Nordic cohort study of 23 million residents\",\n\"第一作者\": \"Rickard Ljung\",\n\"通讯作者\": \"Hanna Nohynek\",\n\"第一作者单位\": \"瑞典医药产品局\",\n\"发表时间\": \"2022-04-20\",\n\"研究目的\": \"评估新冠疫苗与心肌炎关联性\",\n\"研究类型\": \"队列研究\",\n\"研究方法\": \"多国注册研究分析\",\n\"研究对象\": \"北欧四国2300万居民\",\n\"主要研究结果\": \"接种mRNA疫苗后心肌炎风险略有增加，但绝对风险很低\",\n\"研究结论与意义\": \"接种疫苗的总体获益大于心肌炎风险\",\n\"研究亮点或创新点\": \"大样本量多国合作研究;提供疫苗安全性关键证据;为公共卫生决策提供依据\"\n}\n❌ 错误格式示例1 (包含了```json和换行):\njson{\n  \"期刊名称\": \"Nature Medicine\"\n  ...\n}\n❌ 错误格式示例2 (包含了引导语):\n以下是文献的JSON格式整理:\n{\n\"期刊名称\": \"Nature Medicine\"\n...\n}\n❌ 错误格式示例3 (包含了结尾说明):\n{\n\"期刊名称\": \"Nature Medicine\"\n...\n}\n希望这个整理对您有帮助!\n重要提示： 任何偏离格式要求的输出，即使只有一个字符的前缀(如\"json\\n\")，都将被视为完全失败。输出必须从第一个字符\"{\" 开始，到最后一个字符\"}\"结束，中间不得有任何非JSON内容。", "hasOutputParser": true, "options": {}}, "id": "a0fa0e3b-fccf-47fa-a80a-196f72a4a317", "name": "整理各文献摘要", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [3560, 3140]}, {"parameters": {"promptType": "define", "text": "=你的任务是根据用户的请求，生成用于筛选 ISSN 数据的特定命令行参数。\n\n分析用户的请求：{{ $json.extractedChatInput }}\n\n**核心筛选概念（严格限定）：**\n\n1.  **中科院分区 (CAS Division)**: 识别任何提及中科院分区的意图，包括具体分区号（如 \"一区\", \"二区\"）或模糊查询（如 \"中科院几区\"）。\n2.  **JCR 分区 (JCR Division)**: 识别任何提及 JCR 分区或 SCI 分区（用户常混用）的意图，包括具体分区号（如 \"Q1\", \"Q2\"）或模糊查询（如 \"JCR分区是多少\"）。\n3.  **影响因子 (Impact Factor / IF)**: 识别任何提及影响因子、IF 值、IF 分数等的意图，包括具体数值、范围（如 \"大于5\", \"小于10\", \"3到8之间\"）或一般性要求（如 \"高影响因子\"）。\n\n**指令:**\n\n1.  仔细分析用户请求，判断是否**明确提及了上述三个核心筛选概念中的至少一个**。请灵活理解用户的表达方式。\n2.  **【关键规则】如果用户的请求完全没有提及上述任何一个核心筛选概念（即使提到了其他如时间、领域等限定条件），则你的输出内容是“无限制条件”。**\n3.  **如果用户的请求提及了至少一个核心筛选概念**，则根据以下规则生成对应的命令行参数。**严格忽略用户请求中与这三个核心概念无关的任何其他信息或条件（如时间、领域、作者等）。**\n4.  **只生成参数本身**，不要包含任何命令前缀、文件名、解释性文字、说明或任何形式的引言/结语。确保参数之间只有一个空格。\n\n**可用的参数格式 (仅在检测到对应核心概念时使用):**\n*   中科院分区：--cas-zones \"1\" \"2\"（数字可以是 1、2、3、4）\n*   影响因子最小值：`--if-min [数值]`\n*   影响因子最大值：`--if-max [数值]`\n*   JCR 分区：--jcr-zones Q1（值可以是 Q1、Q2、Q3、Q4）\n\n示例输出：\n1. 筛选中科院 1 区文献：\n   --cas-zones \"1\"\n\n2. 筛选中科院 1 区和 2 区文献：\n   --cas-zones \"1\" \"2\"\n\n3. 筛选影响因子大于等于 5.0 的文献：\n   --if-min 5.0\n\n4. 筛选影响因子小于等于 10.0 的文献：\n   --if-max 10.0\n\n5. 筛选影响因子在 3.0 到 8.0 之间的文献：\n   --if-min 3.0 --if-max 8.0\n\n6. 筛选 JCR Q1 分区的文献：\n   --jcr-zones Q1\n\n7. 筛选 JCR Q1 和 Q2 分区的文献：\n   --jcr-zones Q1 Q2\n\n8. 筛选 JCR Q1 分区且影响因子大于 5.0 的文献：\n   --jcr-zones Q1 --if-min 5.0\n\n9. 筛选中科院 1 区且 JCR Q1 分区的文献：\n   --cas-zones \"1\" --jcr-zones Q1\n\n10. 筛选中科院 1 区或 2 区且影响因子在 3.0 到 10.0 之间的文献：\n    --cas-zones \"1\" \"2\" --if-min 3.0 --if-max 10.0\n\n**再次强调：如果未检测到任何核心筛选概念，则输出内容是“无限制条件”。如果检测到了，则只输出参数字符串，参数间用单个空格分隔，且参数格式严格遵守上述规定，且只返回参数部分，不要添加任何其他文本或解释。**", "hasOutputParser": true, "options": {}}, "id": "a1078708-08dc-4403-91cb-00ea9bcdeb93", "name": "生成分区因子限定代码式", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [3180, 2820]}, {"parameters": {"method": "POST", "url": "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi", "sendBody": true, "contentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "db", "value": "pubmed"}, {"name": "retmode", "value": "json"}, {"name": "retmax", "value": "500"}, {"name": "usehistory", "value": "y"}, {"name": "term", "value": "={{ $json.output }} NOT ((\"Erratum\"[Title]) OR (\\\"Correction\\\"[Title]) OR (\\\"Corrigendum\\\"[Title]) OR (\\\"Retraction\\\"[Title]) OR (\\\"Withdrawal\\\"[Title]) OR (Corrigendum[Title]) OR (Erratum[Title]) OR (\\\"Retraction Notice\\\"[Title]) OR (Retracted Publication[Publication Type]) OR (\\\"Expression of Concern\\\"[Title]) OR (Letter[Publication Type]) OR (Editorial[Publication Type]) OR (Comment[Publication Type]) OR (News[Publication Type]) OR (Meeting Abstract[Publication Type]) OR (\\\"Conference Abstract\\\"[Title]) OR (Obituary[Publication Type]) OR (Bibliography[Publication Type]))"}]}, "options": {}}, "id": "275276b8-9aa4-4ba6-a4a8-05d89551e937", "name": "Esearch PMID1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2920, 2500]}, {"parameters": {"command": "=cd /data/pubmed-search/NO_DELETE && python pubmed_if.py \"{{ $json.fileName }}\" "}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2520, 3440], "id": "60f359ce-7803-49be-9f3c-308de0d8b46e", "name": "添加CSV期刊信息"}, {"parameters": {"jsCode": "// 获取原始的article.csv文件名\nlet originalArticleCsvFilename = null;\nlet withJournalInfoCsvFilename = null;\n\ntry {\n  // 从 Code3 节点获取原始文件名\n  const code3Output = $node[\"生成初步csv\"].json;\n  \n  if (code3Output && code3Output.outputFilename) {\n    originalArticleCsvFilename = code3Output.outputFilename;\n    \n    // 根据命名规则生成带分区信息的文件名\n    // 将 \"_article.csv\" 替换为 \"_article_带分区信息.csv\"\n    withJournalInfoCsvFilename = originalArticleCsvFilename.replace(\"_article.csv\", \"_article_带分区信息.csv\");\n  }\n} catch (error) {\n  console.error(\"获取原始文件名时出错:\", error);\n}\n\n// 如果没有从 Code3 获取到，尝试从保存article.csv节点获取\nif (!originalArticleCsvFilename) {\n  try {\n    const saveArticleCsvInput = $node[\"保存article.csv\"].json;\n    if (saveArticleCsvInput && saveArticleCsvInput.outputFilename) {\n      originalArticleCsvFilename = saveArticleCsvInput.outputFilename;\n      withJournalInfoCsvFilename = originalArticleCsvFilename.replace(\"_article.csv\", \"_article_带分区信息.csv\");\n    }\n  } catch (error) {\n    console.error(\"从保存article.csv节点获取文件名时出错:\", error);\n  }\n}\n\n// 返回结果\nreturn {\n  originalArticleCsvFilename: originalArticleCsvFilename || \"原始文件名未找到\",\n  withJournalInfoCsvFilename: withJournalInfoCsvFilename || \"带分区信息的CSV文件名未找到\",\n  fullPath: withJournalInfoCsvFilename ? `/data/pubmed-search/${withJournalInfoCsvFilename}` : null\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2720, 3440], "id": "d776eed2-6723-4ab8-919b-bc720c59e142", "name": "提取最终CSV名"}, {"parameters": {"jsCode": "// --- CSV 配置 ---\nconst headers = [\n  \"期刊名称\", \"ISSN\", \"PMID\", \"文章标题\", \"第一作者\", \"通讯作者\",\n  \"第一作者单位\", \"发表时间\", \"研究目的\", \"研究类型\",\"研究方法\",\n  \"研究对象\", \"主要研究结果\", \"研究结论与意义\", \"研究亮点或创新点\"\n];\n\n// --- 辅助函数：转义并强制加引号 ---\nfunction escapeAndQuoteCsvValue(value) {\n  if (value === null || value === undefined) {\n    return '\"\"'; // 空值也返回带引号的空字符串 \"\"\n  }\n  // 1. 转换为字符串并清理换行符\n  let stringValue = String(value).replace(/\\r?\\n/g, ' ');\n  // 2. 转义内部的双引号 (将 \" 替换为 \"\")\n  const escapedValue = stringValue.replace(/\"/g, '\"\"');\n  // 3. 强制在两边加上双引号\n  return `\"${escapedValue}\"`;\n}\n\n// --- 主要逻辑 ---\nconst items = $input.all();\nconst csvRows = [];\nlet articlesProcessed = 0;\nlet articlesFailed = 0;\n\n// --- 修改点 1: 表头使用逗号连接，并强制加引号 ---\n// 添加表头行\ncsvRows.push(headers.map(escapeAndQuoteCsvValue).join(',')); // 使用逗号 ,\n\n// 处理每篇文章的摘要\nfor (const item of items) {\n  try {\n    if (item.json && item.json.output && typeof item.json.output === 'string') {\n      const articleData = JSON.parse(item.json.output);\n\n      // 根据表头顺序创建数据行\n      const rowData = headers.map(header => {\n        const value = articleData.hasOwnProperty(header) ? articleData[header] : undefined;\n        // --- 修改点 2: 数据单元格也强制加引号 ---\n        return escapeAndQuoteCsvValue(value); // 使用新的转义和加引号函数\n      });\n\n      // --- 修改点 3: 数据行也使用逗号连接 ---\n      csvRows.push(rowData.join(',')); // 使用逗号 ,\n      articlesProcessed++;\n    } else {\n      console.warn(`跳过项目 ${item.index}：输入结构不符合预期 (缺少 item.json.output 或不是字符串)。Input item JSON:`, item.json);\n      articlesFailed++;\n    }\n  } catch (error) {\n    console.error(`处理项目 ${item.index} 时出错:`, error);\n    console.error(`尝试解析的 JSON 字符串:`, item.json?.output);\n    articlesFailed++;\n  }\n}\n\n// 将所有行合并成一个 CSV 字符串 (行分隔符仍然是 \\n)\nconst csvString = csvRows.join('\\n');\n\n// --- (生成动态文件名和准备二进制数据的代码保持不变) ---\nconst now = new Date();\nconst year = now.getFullYear();\nconst month = String(now.getMonth() + 1).padStart(2, '0');\nconst day = String(now.getDate()).padStart(2, '0');\nconst hours = String(now.getHours()).padStart(2, '0');\nconst minutes = String(now.getMinutes()).padStart(2, '0');\nconst seconds = String(now.getSeconds()).padStart(2, '0');\nconst timestamp = `${year}${month}${day}_${hours}${minutes}${seconds}`;\nconst dynamicFilename = `retrieved_issns(${timestamp})_article.csv`;\n\nconst binaryData = {\n  data: {\n    data: Buffer.from(csvString, 'utf8').toString('base64'),\n    fileName: dynamicFilename,\n    mimeType: 'text/csv'\n  }\n};\n\n// --- 返回结果 (保持不变) ---\nreturn {\n  json: {\n    outputFilename: dynamicFilename,\n    totalArticlesInput: items.length,\n    articlesSuccessfullyProcessed: articlesProcessed,\n    articlesFailedToProcess: articlesFailed,\n    csvHeaders: headers\n  },\n  binary: binaryData\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2180, 3440], "id": "2b1ce550-9da1-4ac0-b0fa-d41fe1f893dc", "name": "生成初步csv"}, {"parameters": {"jsCode": "// --- Part 1: 获取并处理 Markdown 文件内容 ---\nconst inputItem = $input.first(); // 获取 Code 节点自身的输入 (来自 Read File)\nlet markdownContent = \"错误：无法读取 Markdown 文件内容。\";\nlet extractedMarkdownContent = \"错误：无法从 Markdown 内容中提取所需部分。\";\n\n// 检查并解码来自 Read File 节点的二进制数据\nif (inputItem.binary && inputItem.binary.data && inputItem.binary.data.data) {\n  try {\n    const base64Data = inputItem.binary.data.data;\n    markdownContent = Buffer.from(base64Data, 'base64').toString('utf8');\n\n    // 提取三个反引号之间的内容\n    const regex = /```([\\s\\S]*?)```/;\n    const matchResult = markdownContent.match(regex);\n    if (matchResult && matchResult[1]) {\n      extractedMarkdownContent = matchResult[1].trim(); // 提取并去除前后空格\n    } else {\n      console.warn(\"未能从 Markdown 内容中找到 ```...``` 包裹的部分。\");\n      extractedMarkdownContent = \"未找到被三个反引号包裹的内容。\";\n    }\n  } catch (error) {\n     console.error(\"解码或提取 Markdown 内容时出错:\", error);\n     // 如果出错，将错误信息设为提取内容，以便后续判断\n     extractedMarkdownContent = `处理 Markdown 时出错: ${error.message}`;\n  }\n} else {\n  console.warn(\"在上一个 Read/Write Files 节点的输出中未找到二进制数据。\");\n  extractedMarkdownContent = \"错误：未找到 Markdown 文件内容数据。\";\n}\n\n// --- Part 2: 获取“生成PubMed检索式”节点的输出 ---\nlet pubmedQuery = \"错误：无法获取 PubMed 检索式。\"; // 默认错误信息\n\ntry {\n  // 使用 $node 表达式访问名为 \"生成PubMed检索式\" 的节点\n  // 使用可选链 (?.) 防止节点或属性不存在时报错\n  // Agent 节点的输出通常在 json.output\n  const pubmedNodeOutput = $node['生成PubMed检索式']?.json?.output;\n\n  // 检查是否成功获取到输出\n  if (pubmedNodeOutput !== undefined && pubmedNodeOutput !== null) {\n    // 确保输出是字符串\n    pubmedQuery = String(pubmedNodeOutput);\n  } else {\n     console.warn(\"未能从'生成PubMed检索式'节点获取有效的输出 (json.output)。\");\n     // pubmedQuery 保持为错误信息\n  }\n} catch (error) {\n  console.error(\"访问'生成PubMed检索式'节点输出时出错:\", error);\n  // 如果访问节点本身出错，pubmedQuery 保持为错误信息\n  pubmedQuery = `访问 PubMed 检索式节点时出错: ${error.message}`;\n}\n\n\n// --- Part 3: 组合两部分内容 ---\nlet finalOutputText = \"\";\n\n// 检查两部分内容是否都有效（即不是错误信息）\nconst isMarkdownValid = !extractedMarkdownContent.startsWith(\"错误：\") && extractedMarkdownContent !== \"未找到被三个反引号包裹的内容。\";\nconst isQueryValid = !pubmedQuery.startsWith(\"错误：\") && !pubmedQuery.startsWith(\"访问 PubMed 检索式节点时出错:\");\n\nif (isMarkdownValid && isQueryValid) {\n  // 如果两部分都有效，用 \" AND \" 连接\n  finalOutputText = `${extractedMarkdownContent} AND ${pubmedQuery}`;\n} else if (!isMarkdownValid) {\n  // 如果 Markdown 内容无效，显示 Markdown 的错误信息\n  finalOutputText = extractedMarkdownContent;\n} else {\n  // 如果 PubMed Query 无效（但 Markdown 有效），显示 Query 的错误信息\n  finalOutputText = pubmedQuery;\n}\n\n// --- Part 4: 返回最终组合的文本 ---\n// 以 Chat Trigger 期望的格式返回\nreturn {\n  json: {\n    text: finalOutputText\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2540, 3140], "id": "24613466-a8e2-462c-bb3e-b06ca4d3df36", "name": "生成二次检索式"}, {"parameters": {"jsCode": "// --- Get Output Filename from the PREVIOUS Execute Command Node ---\nlet markdownFilename = null; // Initialize filename variable\n\ntry {\n  // Get the input for THIS node, which is the output of the previous node ('生成information.md')\n  const previousNodeOutput = $input.first(); // Use .first() to get the single item output\n\n  // Check if the previous node's output and its stdout exist\n  if (previousNodeOutput && previousNodeOutput.json && previousNodeOutput.json.stdout) {\n    const stdoutText = previousNodeOutput.json.stdout;\n\n    // --- Extraction Logic ---\n    // Use a regular expression to find the filename matching the specific pattern:\n    // retrieved_issns(YYYYMMDD_HHMMSS)_information.md\n    // \\d{8} matches exactly 8 digits (YYYYMMDD)\n    // \\d{6} matches exactly 6 digits (HHMMSS)\n    // Parentheses and dot are escaped with backslashes \\\n    const regex = /(retrieved_issns\\(\\d{8}_\\d{6}\\)_information\\.md)/g;\n    const matches = stdoutText.match(regex);\n\n    if (matches && matches.length > 0) {\n      // Assume the relevant filename is the LAST one mentioned if multiple matches occur,\n      // though usually the final output filename is mentioned near the end.\n      markdownFilename = matches[matches.length - 1];\n      console.log(`Extracted filename from stdout: ${markdownFilename}`);\n    } else {\n      console.warn(\"Could not find filename pattern 'retrieved_issns(YYYYMMDD_HHMMSS)_information.md' in previous node's stdout:\", stdoutText);\n      markdownFilename = \"Markdown filename pattern not found in stdout\";\n    }\n  } else {\n     console.warn(\"Stdout from the previous node ('生成information.md') is missing or empty.\");\n     markdownFilename = \"Stdout not available from previous node\";\n  }\n} catch (error) {\n   console.error(\"Error processing stdout from previous node ('生成information.md'):\", error);\n   markdownFilename = \"Error processing previous node stdout\";\n}\n\n// --- Return the extracted filename ---\n// Return an object containing the extracted filename\nreturn {\n  extractedMarkdownFilename: markdownFilename\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2180, 3140], "id": "006179ae-3bcb-466e-ac40-1870003ac7e3", "name": "生成information.md文件名"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// 获取上一步传入的单篇文章数据\nconst articleJson = $json;\n\n// --- 提取 ISSN ---\nlet issn = null; // 初始化 ISSN 变量\n\ntry {\n  // 尝试访问 ISSN 数据，使用可选链 (?.) 防止因路径不存在而报错\n  const issnData = articleJson.MedlineCitation?.Article?.Journal?.ISSN;\n\n  // 检查是否成功获取到 issnData\n  if (issnData) {\n    // 如果 issnData 是一个对象并且包含 '_' 属性 (常见的 XML 转 JSON 结构)\n    if (typeof issnData === 'object' && issnData !== null && typeof issnData._ === 'string') {\n      issn = issnData._; // 提取 '_' 属性的值\n    }\n    // 如果 issnData 直接就是字符串\n    else if (typeof issnData === 'string') {\n      issn = issnData; // 直接使用该字符串\n    }\n    // 注意：这里没有显式处理一个期刊可能同时有 Print 和 Electronic 两种 ISSN 的情况。\n    // 如果 XML 中 ISSN 是一个数组，这段代码可能只会获取到第一个，或者需要更复杂的逻辑。\n    // 但对于常见的单个 ISSN 或对象结构的 ISSN，这段代码是有效的。\n  }\n\n} catch (error) {\n  // 如果在提取过程中发生任何预料之外的错误，在控制台记录错误信息\n  console.error(`!!! Error extracting ISSN for item ${$itemIndex}:`, error);\n  // 发生错误时，issn 保持为 null\n}\n\n// --- 返回最终结果对象 (只包含 issn) ---\nreturn {\n  issn: issn // 只输出提取到的 ISSN 号 (如果没找到或出错则为 null)\n};"}, "id": "8964a418-486c-4983-afcd-3c38231a3612", "name": "提取1次检索文献ISSN号", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3700, 2500]}, {"parameters": {"jsCode": "// 获取上一个节点（Chat Trigger）的输入\n// 注意：根据你的 n8n 版本和连接方式，$input.item 或 $json 可能需要调整\n// 你可以在左侧 INPUT DATA 面板查看实际传入的数据结构\nconst userInput = $input.item.json.chatInput; \n\n// 获取当前日期\nconst today = new Date();\nconst year = today.getFullYear();\n// 月份是从0开始的，所以要加1，并补零\nconst month = String(today.getMonth() + 1).padStart(2, '0'); \n// 日期补零\nconst day = String(today.getDate()).padStart(2, '0');\n\n// 格式化成 YYYY-MM-DD，方便模型理解\nconst formattedToday = `${year}-${month}-${day}`;\n\n// ★ 关键：准备要传递给下一个节点（Agent）的数据\n// 我们将原始的用户输入和计算好的当前日期一起传递\nconst outputData = {\n  originalQuery: userInput, // 原始用户查询\n  currentDateInfo: formattedToday // 当前日期字符串\n};\n\n// 返回包含这些信息的新 item\n// n8n 会自动将其包装在 json 字段下传给下一个节点\nreturn {\n  json: outputData \n}; "}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2360, 2500], "id": "84347af3-910f-47e3-ae82-6bc7167aa245", "name": "计算当前日期"}, {"parameters": {"command": "=cd /data/pubmed-search/NO_DELETE && \\\npython csv_to_formatted_xlsx.py \"/data/pubmed-search/{{ $json.withJournalInfoCsvFilename }}\" \"/data/pubmed-search/{{ $json.withJournalInfoCsvFilename.replace('.csv', '.xlsx') }}\""}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2900, 3440], "id": "5442e219-22f0-4e4c-9249-02b64c157b47", "name": "CSV转xlsx"}, {"parameters": {"command": "=cd /data/pubmed-search && pandoc \"{{$json.filePathToSave}}\" \\\n      --reference-doc=/data/pubmed-search/NO_DELETE/reference.docx \\\n      -o \"{{$json.filePathToSave.replace('.md', '.docx')}}\"\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2540, 3720], "id": "6675c679-5ef4-4876-8ae9-e4dbfa828027", "name": "MD转docx"}, {"parameters": {"command": "={{ $json.deleteCommand }}"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2900, 3720], "id": "e20e8e5f-1566-40d6-ab08-0da69dbf22ca", "name": "删除中间文件"}, {"parameters": {"jsCode": "// 初始化保存文件名的数组\nconst filesToDelete = [];\nconst nodesToCheck = [\n  \"提取并去重ISSN\", \"准备CSV\", \"保存retrieved_issns(YYYYMMDD_HHMMSS).csv\", \n  \"保存core_query.txt\", \"生成information.csv\", \"生成information.md\",\n  \"生成初步csv\", \"保存article.csv\", \"准备综述文件名和内容\"\n];\n\n// 收集各节点中可能存在的文件名信息\nfor (const nodeName of nodesToCheck) {\n  try {\n    const node = $node[nodeName];\n    if (!node || !node.json) continue;\n    \n    // 检查可能包含文件名的常见字段\n    const possibleFields = [\"csvFilename\", \"outputFilename\", \"queryFilename\", \n                           \"filePathToSave\", \"withJournalInfoCsvFilename\"];\n    \n    for (const field of possibleFields) {\n      if (node.json[field]) {\n        const filename = node.json[field];\n        // 如果是完整路径，提取文件名部分\n        const basename = filename.includes('/') \n          ? filename.substring(filename.lastIndexOf('/') + 1) \n          : filename;\n        // 排除已经在列表中的文件名\n        if (!filesToDelete.includes(basename) && \n            !basename.endsWith('.docx') && \n            !basename.endsWith('.xlsx')) {\n          filesToDelete.push(basename);\n          console.log(`从节点 ${nodeName} 收集到文件: ${basename}`);\n        }\n      }\n    }\n  } catch (error) {\n    console.error(`处理节点 ${nodeName} 时出错:`, error);\n  }\n}\n\n// 添加时间戳模式匹配\n// 从生成的文件名中提取时间戳模式，用于匹配可能未直接跟踪的文件\nconst timeStampPatterns = new Set();\nfor (const filename of filesToDelete) {\n  const match = filename.match(/retrieved_issns\\((\\d{8}_\\d{6})\\)/);\n  if (match && match[1]) {\n    timeStampPatterns.add(match[1]);\n  }\n}\n\nconsole.log(\"找到的时间戳模式:\", Array.from(timeStampPatterns));\n\n// 构建删除命令\nlet deleteCommand = \"cd /data/pubmed-search && \";\n\n// 如果有特定文件名可以直接删除\nif (filesToDelete.length > 0) {\n  // 转义文件名中的特殊字符\n  const escapedFiles = filesToDelete.map(f => f.replace(/([()])/g, '\\\\$1')).join(' ');\n  deleteCommand += `rm -f ${escapedFiles} && `;\n}\n\n// 如果有时间戳模式，添加模式匹配删除\nif (timeStampPatterns.size > 0) {\n  for (const pattern of timeStampPatterns) {\n    deleteCommand += `find . -type f -name \"retrieved_issns(${pattern})*\" ! -name \"*.docx\" ! -name \"*.xlsx\" -delete && `;\n  }\n}\n\n// 删除命令末尾的 \" && \"\ndeleteCommand = deleteCommand.endsWith(\" && \") \n  ? deleteCommand.slice(0, -4) \n  : deleteCommand;\n\n// 返回文件列表和删除命令\nreturn {\n  filesToDelete,\n  timeStampPatterns: Array.from(timeStampPatterns),\n  deleteCommand,\n  // 更安全的命令，先列出要删除的文件\n  safeDeleteCommand: `cd /data/pubmed-search && echo \"要删除的文件:\" && ` + \n    timeStampPatterns.size > 0 \n      ? Array.from(timeStampPatterns).map(pattern => \n          `find . -type f -name \"retrieved_issns(${pattern})*\" ! -name \"*.docx\" ! -name \"*.xlsx\" -print`\n        ).join(' && ') \n      : 'echo \"没有找到相关文件\"'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2720, 3720], "id": "802c26cf-7762-4630-8ad2-38b65c803c2d", "name": "识别需要删除的中间文件"}, {"parameters": {"operation": "write", "fileName": "={{ $json.filePathToSave }}", "dataPropertyName": "=data", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2360, 3720], "id": "88bae30c-c31a-4fe5-8bee-b5c42f9f88d2", "name": "保存综述.MD"}, {"parameters": {"jsCode": "// --- Get Chat Input ---\n// Get the input item from the 'When chat message received' node\nconst chatInputItem = $('When chat message received').first();\n// Access the chatInput field from the JSON data of that item\nconst chatInputValue = chatInputItem.json.chatInput;\n// Explicitly convert the value to a string data type.\nconst chatInputText = String(chatInputValue);\n\n// --- Get Output Filename from Execute Command Stdout ---\nlet informationFilename = null; // Initialize filename variable\n\ntry {\n  // Get the output of the '生成information.csv' node (the Execute Command node)\n  // Use .first() as it usually outputs one item\n  // Note: We access the *input* to this Code node, which is the output of the previous node.\n  const executeCmdItem = $input.first(); // Get the input data for THIS node\n\n  // Check if the command node output and its stdout exist\n  if (executeCmdItem && executeCmdItem.json && executeCmdItem.json.stdout) {\n    const stdoutText = executeCmdItem.json.stdout;\n\n    // Use a regular expression to find the filename ending with '_information.csv'\n    // This regex looks for a path-like structure ending in '_information.csv'\n    // It captures the filename part (characters not being '/' or whitespace) before the suffix.\n    const regex = /([^/\\s]+_information\\.csv)/g;\n\n    const matchResult = stdoutText.match(regex);\n\n    // If a match is found, extract the *last* occurrence, as stdout might mention it multiple times\n    if (matchResult && matchResult.length > 0) {\n      // matchResult is an array of all matches, e.g., [\"file1_information.csv\", \"file2_information.csv\"]\n      // We usually want the last one mentioned in the stdout.\n      informationFilename = matchResult[matchResult.length - 1];\n    } else {\n       console.warn(\"Could not find information filename pattern in stdout:\", stdoutText);\n       informationFilename = \"Filename not found in stdout\"; // Set a default/error value\n    }\n  } else {\n     console.warn(\"Stdout from previous node ('生成information.csv') is missing or empty.\");\n     informationFilename = \"Stdout not available\"; // Set a default/error value\n  }\n} catch (error) {\n   console.error(\"Error processing stdout from previous node ('生成information.csv'):\", error);\n   informationFilename = \"Error processing stdout\"; // Set a default/error value\n}\n\n\n// Return the extracted values in an object\nreturn {\n  extractedChatInput: chatInputText,\n  informationFilename: informationFilename // The filename ending in _information.csv extracted from stdout\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3000, 2820], "id": "e71facdc-5cea-42fd-bd8d-ec80ade38313", "name": "重述检索要求"}, {"parameters": {"jsCode": "// === 0. 从「When chat message received」节点拿到最初的检索要求 ===\nlet originalQuery = '';\ntry {\n  const triggerItem = $('When chat message received').first();\n  originalQuery = String(triggerItem.json?.chatInput ?? '').trim();\n} catch (err) {\n  console.error('[整合摘要] 获取原始检索式失败：', err);\n}\n\n// === 1. 汇总各文献摘要（保留原有流程，稍作清理） ===\nconst items = $('整理各文献摘要').all();\nlet combinedContent = '';\nlet articleCount = 0;\nconst processingErrors = [];\n\nfor (let i = 0; i < items.length; i++) {\n  const raw = items[i]?.json?.output?.trim();\n  if (typeof raw !== 'string') {\n    processingErrors.push({ index: i + 1, error: '缺少 output 字符串' });\n    continue;\n  }\n  if (!raw.startsWith('{') || !raw.endsWith('}')) {\n    processingErrors.push({ index: i + 1, error: 'output 不是合法 JSON' });\n    continue;\n  }\n\n  // 保留整段 JSON 字符串\n  const prefix = `${articleCount + 1}. `;\n  combinedContent += (articleCount ? '\\n\\n' : '') + prefix + raw;\n  articleCount++;\n}\n\n// === 2. 生成头部信息（含检索要求） ===\nconst today = new Date().toISOString().slice(0, 10);\nlet header = `# 眼科文献摘要 (${today})\\n`;\nheader += `检索要求：${originalQuery || '（未能获取）'}\\n`;\nheader += `本次共成功整理 ${articleCount} 篇文献。\\n\\n`;\n\nconst finalContent = header + combinedContent;\n\n// === 3. 向下游返回数据 ===\nreturn [\n  {\n    json: {\n      combined_summary: finalContent,        // 供下游写文件 / 大模型使用\n      original_query: originalQuery,         // 这里就是最早聊天里的检索要求\n      totalArticlesInput: items.length,\n      articlesSuccessfullyProcessed: articleCount,\n      articlesFailedToProcess: processingErrors.length,\n      processingErrorDetails: processingErrors\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3060, 3440], "id": "c0d0ca31-30bc-4bae-9636-e456720d8ec5", "name": "整合摘要&提取检索要求"}, {"parameters": {"content": "双击进入Credential填入gemini api（质量最好！）。\napi网址：https://aistudio.google.com/app/apikey\n并双击下面4个类似图标的model\n关注“医学AI干货”公众号查看教程", "height": 100, "width": 380}, "type": "n8n-nodes-base.stickyNote", "position": [2220, 2700], "typeVersion": 1, "id": "cde9ce84-7e15-465a-80ec-7370b2d248be", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// Get the output from the previous agent node\nconst agentOutput = $input.item.json.output || \"\";\n\n// Check if the output contains any of the filter parameters\nconst containsFilterParams = [\n  \"cas-zones\",\n  \"if-min\",\n  \"if-max\",\n  \"jcr-zones\"\n].some(param => agentOutput.includes(param));\n\n// If any filter parameters are found, return the original output\n// Otherwise, return an empty string\nif (containsFilterParams) {\n  return {\n    json: {\n      output: agentOutput\n    }\n  };\n} else {\n  return {\n    json: {\n      output: \"\"\n    }\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3520, 2820], "id": "12cf9d58-a8c3-413d-b476-2f6dbfa92ea8", "name": "输出条件限定"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e00d7bfd-7c4e-4ebd-97c6-d9cab6a9585c", "leftValue": "={{ $json.text }}", "rightValue": "未找到被三个反引号包裹的内容。", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2560, 3300], "id": "bc78086a-4b03-4b76-aeb9-9c321e508a9a", "name": "If"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "计算当前日期", "type": "main", "index": 0}]]}, "准备CSV": {"main": [[{"node": "保存retrieved_issns(YYYYMMDD_HHMMSS).csv", "type": "main", "index": 0}]]}, "生成PubMed检索式": {"main": [[{"node": "Esearch PMID1", "type": "main", "index": 0}]]}, "Efetch Abstract1": {"main": [[{"node": "Parse EFetch XML1", "type": "main", "index": 0}]]}, "Parse EFetch XML1": {"main": [[{"node": "Split Articles1", "type": "main", "index": 0}]]}, "Split Articles1": {"main": [[{"node": "提取1次检索文献ISSN号", "type": "main", "index": 0}]]}, "提取并去重ISSN": {"main": [[{"node": "准备CSV", "type": "main", "index": 0}]]}, "生成information.csv": {"main": [[{"node": "重述检索要求", "type": "main", "index": 0}]]}, "保存retrieved_issns(YYYYMMDD_HHMMSS).csv": {"main": [[{"node": "保存core_query.txt", "type": "main", "index": 0}]]}, "保存core_query.txt": {"main": [[{"node": "生成information.csv", "type": "main", "index": 0}]]}, "生成information.md": {"main": [[{"node": "生成information.md文件名", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "生成二次检索式", "type": "main", "index": 0}]]}, "Esearch PMID": {"main": [[{"node": "Efetch Abstract", "type": "main", "index": 0}]]}, "Efetch Abstract": {"main": [[{"node": "Parse EFetch XML2", "type": "main", "index": 0}]]}, "Process Article Data1": {"main": [[{"node": "整理各文献摘要", "type": "main", "index": 0}]]}, "Parse EFetch XML2": {"main": [[{"node": "Split Articles2", "type": "main", "index": 0}]]}, "Split Articles2": {"main": [[{"node": "Process Article Data1", "type": "main", "index": 0}]]}, "保存article.csv": {"main": [[{"node": "添加CSV期刊信息", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "生成PubMed检索式", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model3": {"ai_languageModel": [[{"node": "整理各文献摘要", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "生成分区因子限定代码式", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "综述框架agent", "type": "ai_languageModel", "index": 0}]]}, "准备综述文件名和内容": {"main": [[{"node": "保存综述.MD", "type": "main", "index": 0}]]}, "Google Gemini Chat Model4": {"ai_languageModel": [[{"node": "综述写作agent", "type": "ai_languageModel", "index": 0}]]}, "综述框架agent": {"main": [[{"node": "综述写作agent", "type": "main", "index": 0}]]}, "综述写作agent": {"main": [[{"node": "准备综述文件名和内容", "type": "main", "index": 0}]]}, "整理各文献摘要": {"main": [[{"node": "生成初步csv", "type": "main", "index": 0}]]}, "生成分区因子限定代码式": {"main": [[{"node": "输出条件限定", "type": "main", "index": 0}]]}, "Esearch PMID1": {"main": [[{"node": "Efetch Abstract1", "type": "main", "index": 0}]]}, "添加CSV期刊信息": {"main": [[{"node": "提取最终CSV名", "type": "main", "index": 0}]]}, "提取最终CSV名": {"main": [[{"node": "CSV转xlsx", "type": "main", "index": 0}]]}, "生成初步csv": {"main": [[{"node": "保存article.csv", "type": "main", "index": 0}]]}, "生成二次检索式": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "生成information.md文件名": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "提取1次检索文献ISSN号": {"main": [[{"node": "提取并去重ISSN", "type": "main", "index": 0}]]}, "计算当前日期": {"main": [[{"node": "生成PubMed检索式", "type": "main", "index": 0}]]}, "CSV转xlsx": {"main": [[{"node": "整合摘要&提取检索要求", "type": "main", "index": 0}]]}, "MD转docx": {"main": [[{"node": "识别需要删除的中间文件", "type": "main", "index": 0}]]}, "识别需要删除的中间文件": {"main": [[{"node": "删除中间文件", "type": "main", "index": 0}]]}, "保存综述.MD": {"main": [[{"node": "MD转docx", "type": "main", "index": 0}]]}, "重述检索要求": {"main": [[{"node": "生成分区因子限定代码式", "type": "main", "index": 0}]]}, "整合摘要&提取检索要求": {"main": [[{"node": "综述框架agent", "type": "main", "index": 0}]]}, "输出条件限定": {"main": [[{"node": "生成information.md", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "识别需要删除的中间文件", "type": "main", "index": 0}], [{"node": "Esearch PMID", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f53b51e6-c305-469e-bb3a-b000d93341af", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ae65e773d4359e5412849113f28dd18f8189dbd248835d4feaa6edb920617c8b"}, "id": "fq21LbTnQRZ3cSx7", "tags": []}