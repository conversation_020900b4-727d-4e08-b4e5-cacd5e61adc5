# n8n Docker 容器迁移包 (Mac导出 - v1.105.3)

本迁移包包含升级到最新版本的 n8n v1.105.3 Docker 容器及其完整数据。

## 包含内容

### 1. Docker镜像文件
- **文件**: `n8n-custom-v1.105.3.tar` (约736MB)
- **内容**: 包含 n8n v1.105.3 + Pandoc 3.1.13 + Python 3.12 + 依赖包
- **架构**: 支持 x86_64 和 ARM64 (Apple Silicon)

### 2. 数据目录
- **目录**: `container-data/.n8n/`
- **内容**: 
  - 数据库文件 (database.sqlite - 约8GB)
  - 工作流配置
  - 用户设置
  - 凭证信息
  - 执行历史

## 版本信息

- **n8n版本**: 1.105.3 (最新版本)
- **Pandoc版本**: 3.1.13
- **Python版本**: 3.12.10
- **依赖包**: pandas 2.2.3, openpyxl 3.1.5
- **导出时间**: 2025年8月6日

## 在新机器上安装

### 1. 导入Docker镜像
```bash
docker load -i n8n-custom-v1.105.3.tar
```

### 2. 准备数据目录
```bash
# 创建数据目录
mkdir -p ~/.docker

# 复制数据
cp -R container-data/.n8n ~/.docker/
```

### 3. 启动容器
```bash
docker run -d --name n8n-custom \
  -p 5678:5678 \
  -v ~/.docker:/data \
  --restart=unless-stopped \
  n8n-custom-updated:v1.105.3
```

### 4. 验证安装
```bash
# 检查容器状态
docker ps

# 检查版本
docker exec n8n-custom n8n --version

# 查看日志
docker logs n8n-custom
```

### 5. 访问n8n
在浏览器中打开: http://localhost:5678

## 升级优势

相比之前的 v1.93.0 版本，新版本包含：

- ✅ 11个版本的功能更新和改进
- ✅ 最新的安全补丁
- ✅ 性能优化
- ✅ 新增节点和功能
- ✅ Bug修复
- ✅ 数据库结构优化

## 兼容性

- **操作系统**: Windows, macOS, Linux
- **Docker版本**: 20.10+ 推荐
- **架构**: x86_64, ARM64
- **内存要求**: 最少2GB，推荐4GB+

## 故障排除

### 如果遇到权限问题 (Linux/Mac):
```bash
sudo chown -R $(whoami) ~/.docker
sudo chmod -R 755 ~/.docker/.n8n
```

### 如果端口冲突:
```bash
# 使用其他端口
docker run -d --name n8n-custom \
  -p 5680:5678 \
  -v ~/.docker:/data \
  --restart=unless-stopped \
  n8n-custom-updated:v1.105.3
```

### 查看详细日志:
```bash
docker logs -f n8n-custom
```

## 备份建议

定期备份数据目录：
```bash
tar -czf n8n-backup-$(date +%Y%m%d).tar.gz ~/.docker
```

---

**注意**: 此镜像包含完整的生产环境数据，请妥善保管。
