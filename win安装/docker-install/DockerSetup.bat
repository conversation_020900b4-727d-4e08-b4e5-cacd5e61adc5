@echo off
title Docker Setup Script
chcp 437 > nul

echo =======================================================
echo       Docker Desktop Installation Script
echo =======================================================
echo.

echo Step 0: Checking if WSL is installed...
echo.
wsl --status > nul 2>&1
if %errorlevel% == 0 (
    echo WSL is already installed, proceeding with setup.
) else (
    echo WSL is not installed. Installing WSL now...
    echo.
    echo Please wait while WSL is being installed...
    wsl --install
    echo.
    echo WSL installation completed. You need to RESTART your computer
    echo to finish the WSL setup process.
    echo.
    echo After restarting, please run this script again to continue
    echo with Docker Desktop installation.
    echo.
    echo Press any key to restart your computer...
    pause > nul
    shutdown /r /t 5 /c "Restarting to complete WSL installation"
    exit
)
echo.

echo Step 1: Running SimpleInstall.ps1...
echo.
powershell.exe -ExecutionPolicy Bypass -File "%~dp0SimpleInstall.ps1"
echo.

echo Step 2: Updating WSL...
echo.
wsl --update
echo.

echo Step 3: Checking Windows Features...
echo.
echo Checking Windows Subsystem for Linux...
DISM /Online /Get-FeatureInfo /FeatureName:Microsoft-Windows-Subsystem-Linux | find "State : Enabled" > nul
if errorlevel 1 (
    echo   Feature not enabled, enabling now...
    echo N | DISM /Online /Enable-Feature /FeatureName:Microsoft-Windows-Subsystem-Linux /All /NoRestart
) else (
    echo   Feature already enabled.
)
echo.

echo Checking Hypervisor Platform...
DISM /Online /Get-FeatureInfo /FeatureName:HypervisorPlatform | find "State : Enabled" > nul
if errorlevel 1 (
    echo   Feature not enabled, enabling now...
    echo N | DISM /Online /Enable-Feature /FeatureName:HypervisorPlatform /All /NoRestart
) else (
    echo   Feature already enabled.
)
echo.

echo Checking Hyper-V...
DISM /Online /Get-FeatureInfo /FeatureName:Microsoft-Hyper-V | find "State : Enabled" > nul
if errorlevel 1 (
    echo   Feature not enabled, enabling now...
    echo N | DISM /Online /Enable-Feature /FeatureName:Microsoft-Hyper-V /All /NoRestart
) else (
    echo   Feature already enabled.
)
echo.

echo Checking Hyper-V Management Tools...
DISM /Online /Get-FeatureInfo /FeatureName:Microsoft-Hyper-V-Management-Clients | find "State : Enabled" > nul
if errorlevel 1 (
    echo   Feature not enabled, enabling now...
    echo N | DISM /Online /Enable-Feature /FeatureName:Microsoft-Hyper-V-Management-Clients /All /NoRestart
) else (
    echo   Feature already enabled.
)
echo.

echo Step 4: Final WSL update after feature installation...
echo.
wsl --update
echo.

echo ======================================================
echo      Installation complete!
echo      If new features were enabled, please restart your 
echo      computer to apply changes.
echo ======================================================
echo.

echo Press any key to exit...
pause > nul
