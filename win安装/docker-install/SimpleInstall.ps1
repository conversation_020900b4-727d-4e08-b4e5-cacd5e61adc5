# Docker Desktop installation script
# Must be run as administrator

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "Please run this script as Administrator!" -ForegroundColor Red
    Write-Host "Right-click on PowerShell and select 'Run as administrator', then run this script again." -ForegroundColor Red
    Pause
    exit
}

Write-Host "=======================================================" -ForegroundColor Green
Write-Host "      Docker Desktop and WSL 2 Installation" -ForegroundColor Green
Write-Host "=======================================================" -ForegroundColor Green
Write-Host ""

# Step 1: Enable Windows features
Write-Host "Enabling required Windows features..." -ForegroundColor Cyan
Write-Host "Enabling Windows Subsystem for Linux..."
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
Write-Host "Enabling Virtual Machine Platform..."
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
Write-Host "Enabling Hyper-V..."
dism.exe /online /enable-feature /featurename:Microsoft-Hyper-V /all /norestart
Write-Host "Enabling Hyper-V Management Tools..."
dism.exe /online /enable-feature /featurename:Microsoft-Hyper-V-Management-Clients /all /norestart
dism.exe /online /enable-feature /featurename:Microsoft-Hyper-V-Management-PowerShell /all /norestart
Write-Host "Windows features enabled!" -ForegroundColor Green
Write-Host ""

# Step 2: Download WSL 2 kernel update package
Write-Host "Downloading WSL 2 kernel update package..." -ForegroundColor Cyan
$wslUpdateUrl = "https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi"
$wslUpdateInstallerPath = "$env:TEMP\wsl_update_x64.msi"

try {
    Invoke-WebRequest -Uri $wslUpdateUrl -OutFile $wslUpdateInstallerPath -UseBasicParsing
    Write-Host "WSL 2 kernel update package downloaded!" -ForegroundColor Green
} catch {
    Write-Host "Error: Failed to download WSL 2 kernel update package. Please check your internet connection, or download manually from:" -ForegroundColor Red
    Write-Host "https://aka.ms/wsl2kernel" -ForegroundColor Yellow
    Pause
    exit
}
Write-Host ""

# Step 3: Install WSL 2 kernel update package
Write-Host "Installing WSL 2 kernel update package..." -ForegroundColor Cyan
Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$wslUpdateInstallerPath`" /quiet" -Wait
Write-Host "WSL 2 kernel update package installed!" -ForegroundColor Green
Write-Host ""

# Step 4: Set WSL 2 as default version
Write-Host "Setting WSL 2 as default version..." -ForegroundColor Cyan
wsl --set-default-version 2
Write-Host "WSL 2 set as default version!" -ForegroundColor Green
Write-Host ""

# Step 5: Install Docker Desktop
Write-Host "Installing Docker Desktop..." -ForegroundColor Cyan
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$dockerInstallerPath = Join-Path $scriptPath "Docker Desktop Installer.exe"

if (Test-Path $dockerInstallerPath) {
    Write-Host "Found Docker Desktop installer, starting installation..." -ForegroundColor Green
    Start-Process -FilePath $dockerInstallerPath -ArgumentList "install --quiet" -Wait
    Write-Host "Docker Desktop installation command executed!" -ForegroundColor Green
} else {
    Write-Host "Error: Docker Desktop installer not found." -ForegroundColor Red
    Write-Host "Please confirm the file is named 'Docker Desktop Installer.exe' and located in the same directory as this script" -ForegroundColor Yellow
    Pause
    exit
}

# Step 6: Completion
Write-Host ""
Write-Host "=======================================================" -ForegroundColor Green
Write-Host "Installation process completed! Please restart your computer to complete all configurations." -ForegroundColor Green
Write-Host "After restarting, Docker Desktop should start normally." -ForegroundColor Green
Write-Host "If Docker Desktop still shows errors, please check:" -ForegroundColor Yellow
Write-Host "1. BIOS virtualization is enabled" -ForegroundColor Yellow
Write-Host "2. Hyper-V feature is enabled" -ForegroundColor Yellow
Write-Host "3. WSL 2 status: Open PowerShell and run 'wsl -l -v'" -ForegroundColor Yellow
Write-Host "=======================================================" -ForegroundColor Green

Write-Host "Setup complete. Please remember to restart your computer later to complete the installation." -ForegroundColor Yellow
