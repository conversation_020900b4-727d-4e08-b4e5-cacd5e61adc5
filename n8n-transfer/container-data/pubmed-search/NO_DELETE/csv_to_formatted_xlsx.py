import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
import sys

def csv_to_formatted_xlsx(csv_file, xlsx_file):
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file)
    except FileNotFoundError:
        print(f"Error: CSV file not found at {csv_file}")
        sys.exit(1)
    except Exception as e:
        print(f"Error reading CSV file {csv_file}: {e}")
        sys.exit(1)

    if "2023最新IF" in df.columns:
        df.rename(columns={"2023最新IF": "最新IF"}, inplace=True)
        print("Renamed column '2023最新IF' to '最新IF'.")
    else:
        print("Warning: Column '2023最新IF' not found for renaming.")

    try:
        df.to_excel(xlsx_file, index=False, engine='openpyxl')
    except Exception as e:
        print(f"Error saving initial Excel file {xlsx_file}: {e}")
        sys.exit(1)
    
    try:
        wb = load_workbook(xlsx_file)
        ws = wb.active
    except Exception as e:
        print(f"Error loading workbook {xlsx_file} for formatting: {e}")
        sys.exit(1)
    
    ws.auto_filter.ref = ws.dimensions
    ws.freeze_panes = 'A2'
    
    light_green_fill = PatternFill(start_color='E8F5E9', end_color='E8F5E9', fill_type='solid')
    times_font = Font(name='Times New Roman', size=11)
    header_font = Font(name='Times New Roman', size=11, bold=True)
    header_alignment = Alignment(horizontal='left', vertical='center')
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    for row_num in range(1, ws.max_row + 1):
        for col_num in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_num, column=col_num)
            if cell.value is not None and str(cell.value).strip() != '':
                cell.border = thin_border
            
            if row_num == 1:
                cell.font = header_font
                cell.alignment = header_alignment
                cell.fill = light_green_fill
            else:
                cell.font = times_font
                cell.alignment = data_alignment
                if (row_num - 1) % 2 == 0:
                    cell.fill = light_green_fill
    
    MIN_PRACTICAL_WIDTH = 2.0 # Define a minimum practical width

    for col_idx, column_cells in enumerate(ws.columns, 1):
        max_length = 0 # Not strictly needed for col_idx == 1 if using fixed base, but good practice
        column_letter = get_column_letter(column_cells[0].column)
        
        # Calculate max_length for columns where it's used for width calculation
        if col_idx > 1: # For col_idx 1, max_length is not used in its base width
            for cell in column_cells:
                if cell.value is not None:
                    current_length = len(str(cell.value))
                    if current_length > max_length:
                        max_length = current_length
        
        # --- MODIFICATION: Adjust width for 1st column ---
        if col_idx == 1:
            base_width_col1 = 7.0 # The "current" established width for column 1
            adjusted_width = max(MIN_PRACTICAL_WIDTH, base_width_col1 * (2/3))
        # --- END OF MODIFICATION ---
        elif col_idx == 2:
            adjusted_width = min(max(5.0, max_length * 0.6 + 1), 12.0)
        elif col_idx == 9:
            base_width_col9 = min(40.0, max_length + 5)
            adjusted_width = max(MIN_PRACTICAL_WIDTH, base_width_col9 * (2/3))
        elif col_idx == 18:
            adjusted_width = min(max(8.0, max_length * 0.7 + 1), 15.0)
        elif col_idx == 11 or col_idx == 12:
            base_width = min(30.0, max_length * 0.8 + 1)
            adjusted_width = max(MIN_PRACTICAL_WIDTH, base_width / 2)
        elif col_idx == 13:
            base_width = min(30.0, max_length * 0.8 + 1)
            adjusted_width = max(MIN_PRACTICAL_WIDTH, base_width / 3)
        elif col_idx == 16:
            base_width = min(50.0, max_length + 2)
            adjusted_width = max(MIN_PRACTICAL_WIDTH, base_width / 3)
        elif col_idx in [3, 4, 5, 6, 7, 8, 14]:
            adjusted_width = min(50.0, max_length + 2)
        elif col_idx >= 10:
            adjusted_width = min(30.0, max_length * 0.8 + 1)
        else:
            adjusted_width = min(25.0, max_length * 0.8 + 1)
        
        ws.column_dimensions[column_letter].width = adjusted_width
    
    try:
        wb.save(xlsx_file)
    except Exception as e:
        print(f"Error saving final formatted Excel file {xlsx_file}: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python csv_to_formatted_xlsx.py input.csv output.xlsx")
        sys.exit(1)
    
    csv_file_path = sys.argv[1]
    xlsx_file_path = sys.argv[2]
    
    print(f"Starting conversion of {csv_file_path} to {xlsx_file_path}...")
    csv_to_formatted_xlsx(csv_file_path, xlsx_file_path)
    print(f"Successfully converted {csv_file_path} to {xlsx_file_path} with formatting.")