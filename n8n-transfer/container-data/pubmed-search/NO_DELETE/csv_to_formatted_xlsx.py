import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import PatternFill, Alignment, Font, Border, Side
from openpyxl.utils import get_column_letter
import sys

def csv_to_formatted_xlsx(csv_file, xlsx_file):
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    # 将DataFrame保存为Excel文件
    df.to_excel(xlsx_file, index=False, engine='openpyxl')
    
    # 加载工作簿进行格式化
    wb = load_workbook(xlsx_file)
    ws = wb.active
    
    # 添加自动筛选到首行
    ws.auto_filter.ref = ws.dimensions
    
    # 冻结首行
    ws.freeze_panes = 'A2'
    
    # 定义浅绿色填充
    light_green_fill = PatternFill(start_color='E8F5E9', end_color='E8F5E9', fill_type='solid')
    
    # 定义Times New Roman字体
    times_font = Font(name='Times New Roman', size=11)
    header_font = Font(name='Times New Roman', size=11, bold=True)  # 标题行可以加粗
    
    # 定义对齐方式
    header_alignment = Alignment(horizontal='left', vertical='center')  # 标题行：左对齐，垂直居中
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)  # 数据行：左对齐，垂直居中，自动换行
    
    # 定义边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 处理每一行
    for row in range(1, ws.max_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            
            # 为有内容的单元格添加边框
            if cell.value is not None and str(cell.value).strip() != '':
                cell.border = thin_border
            
            # 设置字体和对齐方式
            if row == 1:
                # 第一行（标题行）
                cell.font = header_font
                cell.alignment = header_alignment
            else:
                # 其他行
                cell.font = times_font
                cell.alignment = data_alignment
            
            # 隔行填充浅绿色（从第二行开始，因为第一行是标题）
            if row > 1 and row % 2 == 0:
                cell.fill = light_green_fill
    
    # 调整列宽
    for col_idx, column in enumerate(ws.columns, 1):
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        # 根据列号设置不同的宽度策略
        if col_idx == 1:  # 第1列更窄
            adjusted_width = min(12, max_length * 0.6)
        elif col_idx == 8:  # 第8列更宽
            adjusted_width = min(40, max_length + 5)
        elif col_idx == 17:  # 第17列较窄
            adjusted_width = min(15, max_length * 0.7)
        elif col_idx in [2, 3, 4, 5, 6, 7, 13, 15]:  # 第2-7列、第13列和第15列恢复原先较宽的设置
            adjusted_width = min(50, max_length + 2)
        elif col_idx >= 9:  # 第9列及之后的其他列
            adjusted_width = min(30, max_length * 0.8)
        else:  # 其他列
            adjusted_width = min(25, max_length * 0.8)
        
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 保存工作簿
    wb.save(xlsx_file)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python csv_to_formatted_xlsx.py input.csv output.xlsx")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    xlsx_file = sys.argv[2]
    
    csv_to_formatted_xlsx(csv_file, xlsx_file)
    print(f"Successfully converted {csv_file} to {xlsx_file} with formatting")