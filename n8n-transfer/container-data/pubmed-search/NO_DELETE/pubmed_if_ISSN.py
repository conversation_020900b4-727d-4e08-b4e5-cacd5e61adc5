#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ISSN 信息增强工具 (v3)

本脚本根据提供的单列 ISSN 的 CSV 文件，
从包含详细期刊信息的参考 CSV 文件中查找匹配的 ISSN 或 EISSN，
提取指定列（大类分区、JCR分区、影响因子）的信息，
并将这些信息附加到原始 ISSN 列表的右侧。
"""

import csv
import os
import sys

def normalize_issn(issn):
    """规范化 ISSN 字符串，移除连字符和空格。"""
    if issn:
        return issn.replace('-', '').replace(' ', '').strip()
    return ""

def add_journal_info_to_issn_list(issn_list_file, reference_file, output_file=None):
    """
    读取 ISSN 列表，从参考文件匹配信息并附加到输出文件。

    参数:
        issn_list_file (str): 包含单列 ISSN 的 CSV 文件路径 (第一列为 ISSN)。
        reference_file (str): 包含详细期刊信息的参考 CSV 文件路径
                              (第1列 ISSN, 第2列 EISSN, 第3列 大类, 第4列 JCR, 第5列 IF)。
        output_file (str, optional): 输出 CSV 文件路径。如果未指定，将基于 issn_list_file 自动生成。

    返回:
        str: 生成的包含附加信息的 CSV 文件路径。
    """
    print(f"正在处理 ISSN 列表文件: {issn_list_file}")
    print(f"使用参考文件: {reference_file}")

    # --- 步骤 1: 加载参考文件，构建 ISSN/EISSN 到所需信息的映射 ---
    # 映射: {normalized_issn_or_eissn: (大类分区, JCR分区, 影响因子)}
    reference_data_map = {}
    try:
        print(f"正在加载期刊参考信息...")
        # 尝试使用 gbk 打开，失败则回退到 utf-8
        try:
            f_ref = open(reference_file, 'r', encoding='gbk', errors='replace')
        except UnicodeDecodeError:
            print(f"警告: 使用 GBK 编码打开参考文件失败，尝试 UTF-8...")
            f_ref = open(reference_file, 'r', encoding='utf-8', errors='replace')

        with f_ref:
            reader_ref = csv.reader(f_ref)
            try:
                ref_header = next(reader_ref) # 读取并跳过参考文件的表头
                if len(ref_header) < 5:
                     print(f"警告: 参考文件 '{reference_file}' 的表头列数少于5列。")
            except StopIteration:
                print(f"错误: 参考文件 '{reference_file}' 为空或缺少表头。")
                sys.exit(1)

            processed_ref_rows = 0
            for i, row in enumerate(reader_ref, 2): # 从参考文件第二行开始
                processed_ref_rows += 1
                if len(row) < 5: # 确保至少有5列
                    # print(f"警告: 参考文件行 {i} 列数不足5列，跳过。")
                    continue

                # 提取 ISSN (Col 1, index 0) 和 EISSN (Col 2, index 1)
                issn_ref_raw = row[0].strip()
                eissn_ref_raw = row[1].strip() if len(row) > 1 else ""

                issn_ref_norm = normalize_issn(issn_ref_raw)
                eissn_ref_norm = normalize_issn(eissn_ref_raw)

                # 提取所需信息 (Cols 3, 4, 5 -> indices 2, 3, 4)
                category = row[2].strip() if len(row) > 2 else ""
                jcr_quartile = row[3].strip() if len(row) > 3 else ""
                impact_factor = row[4].strip() if len(row) > 4 else ""
                journal_info_tuple = (category, jcr_quartile, impact_factor)

                # 将 ISSN 和 EISSN 都映射到相同的信息元组
                # 注意：如果一个 ISSN/EISSN 在多行出现，后面的会覆盖前面的
                if issn_ref_norm:
                    reference_data_map[issn_ref_norm] = journal_info_tuple
                if eissn_ref_norm:
                    # 只有当 EISSN 与 Print ISSN 不同时才添加，避免覆盖
                    # 或者，如果希望 EISSN 的信息优先，可以无条件添加
                    # 这里选择无条件添加，意味着如果一个期刊同时有 Print 和 EISSN，
                    # 并且它们都在参考文件的不同行，那么最后出现的行的信息会被使用。
                    # 如果它们在同一行，则两者都会指向该行的信息。
                    reference_data_map[eissn_ref_norm] = journal_info_tuple

        print(f"成功加载并处理了 {processed_ref_rows} 行参考数据，构建了 {len(reference_data_map)} 条 ISSN/EISSN 映射。")

    except FileNotFoundError:
        print(f"错误: 参考文件 '{reference_file}' 未找到。")
        sys.exit(1)
    except Exception as e:
        print(f"加载参考文件 '{reference_file}' 时出错: {e}")
        sys.exit(1)

    # --- 步骤 2: 确定输出文件名 ---
    if output_file is None:
        issn_list_filename, issn_list_ext = os.path.splitext(issn_list_file)
        output_file = f"{issn_list_filename}_information{issn_list_ext}"
        print(f"未指定输出文件，将保存为: {output_file}")

    # --- 步骤 3: 处理 ISSN 列表文件并写入输出文件 ---
    written_rows = 0
    try:
        print(f"正在处理 ISSN 列表并写入输出文件: {output_file}")
        # 输入文件也尝试 gbk/utf-8 打开，以防万一
        try:
            f_issn = open(issn_list_file, 'r', encoding='gbk', errors='replace')
        except UnicodeDecodeError:
            f_issn = open(issn_list_file, 'r', encoding='utf-8', errors='replace')

        # 输出文件使用 utf-8-sig 以便 Excel 正确识别 BOM
        with f_issn, \
             open(output_file, 'w', encoding='utf-8-sig', newline='') as f_out:

            reader_issn = csv.reader(f_issn)
            writer_out = csv.writer(f_out)

            # 读取 ISSN 列表文件的表头
            try:
                issn_header = next(reader_issn)
                if not issn_header: # 处理空表头行
                    print(f"警告: ISSN 列表文件 '{issn_list_file}' 的表头为空。")
                    issn_header = ["ISSN"] # 提供默认表头
            except StopIteration:
                print(f"错误: ISSN 列表文件 '{issn_list_file}' 为空。")
                writer_out.writerow(["ISSN", "大类分区", "JCR分区", "影响因子"])
                print(f"处理完成! 输入文件为空。")
                print(f"已创建空的输出文件: {output_file}")
                return output_file

            # 创建并写入新的表头到输出文件
            new_header = issn_header + ["大类分区", "JCR分区", "影响因子"]
            writer_out.writerow(new_header)
            written_rows += 1

            # 处理 ISSN 列表中的每一行数据
            processed_issn_count = 0
            matched_count = 0
            for i, row in enumerate(reader_issn, 2):
                processed_issn_count += 1
                if not row or not row[0].strip(): # 跳过空行或第一列为空的行
                    output_row = list(row) if row else [''] # 保留空行结构或创建单列空行
                    output_row.extend(["", "", ""]) # 附加空信息
                    writer_out.writerow(output_row)
                    written_rows += 1
                    continue

                issn_list_raw = row[0].strip() # ISSN 在第一列
                issn_list_norm = normalize_issn(issn_list_raw)

                # 在参考数据映射中查找 (会自动处理 ISSN 或 EISSN 的匹配)
                matched_info = reference_data_map.get(issn_list_norm)

                output_row = list(row) # 复制原始行

                if matched_info:
                    matched_count += 1
                    output_row.extend(matched_info) # 附加匹配到的信息
                else:
                    output_row.extend(["", "", ""]) # 附加空字符串
                    # print(f"未找到匹配: 行 {i}, ISSN: {issn_list_raw}")

                writer_out.writerow(output_row)
                written_rows += 1

            print(f"处理完成! 共处理 {processed_issn_count} 个 ISSN 行，成功匹配 {matched_count} 条。")
            print(f"总共写入 {written_rows} 行 (包括表头) 到: {output_file}")

            return output_file

    except FileNotFoundError:
        print(f"错误: ISSN 列表文件 '{issn_list_file}' 未找到。")
        sys.exit(1)
    except Exception as e:
        print(f"处理 ISSN 列表文件或写入输出时出错: {e}")
        sys.exit(1)


def main():
    """主函数，处理命令行参数并执行信息提取"""
    import argparse

    parser = argparse.ArgumentParser(description='根据 ISSN 列表从参考文件中提取指定信息并附加到列表右侧。')
    parser.add_argument('issn_list_file', help='包含单列 ISSN 的 CSV 文件路径 (第一列为 ISSN，可含表头)。')
    parser.add_argument('--reference', '-r',
                        default='2024影响因子+2025年中科院分区-ISSN.csv', # 默认使用当前目录下的文件
                        help='参考 CSV 文件路径 (第1列 ISSN, 第2列 EISSN, 第3/4/5列为大类/JCR/IF)。')
    parser.add_argument('--output', '-o', help='输出 CSV 文件路径 (可选，默认根据 ISSN 列表文件名生成)。')

    args = parser.parse_args()

    # 检查文件是否存在
    if not os.path.isfile(args.issn_list_file):
        print(f"错误: ISSN 列表文件 '{args.issn_list_file}' 不存在。")
        sys.exit(1)

    if not os.path.isfile(args.reference):
        print(f"错误: 参考文件 '{args.reference}' 不存在。")
        sys.exit(1)

    # 执行信息提取和附加
    add_journal_info_to_issn_list(args.issn_list_file, args.reference, args.output)


if __name__ == "__main__":
    main()
