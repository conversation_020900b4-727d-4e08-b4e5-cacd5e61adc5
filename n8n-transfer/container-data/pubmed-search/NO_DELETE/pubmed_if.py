#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PubMed文献信息增强工具（改进版）

本脚本用于处理PubMed检索生成的CSV文件，为每篇文献添加期刊影响因子和分区信息。
针对特定格式的影响因子数据文件进行了优化，支持多种ISSN格式匹配。
增加了序号列。
"""

import csv
import os
import sys


def process_pubmed_csv(input_file, reference_file, output_file=None):
    """
    处理PubMed检索结果CSV文件，添加期刊的影响因子和分区信息，并在第一列添加序号
    
    参数:
        input_file (str): PubMed检索结果CSV文件路径
        reference_file (str): 期刊影响因子和分区信息CSV文件路径
        output_file (str, optional): 输出CSV文件路径，如果不指定则自动生成
    
    返回:
        str: 处理后的CSV文件路径
    """
    print(f"正在处理PubMed检索结果文件: {input_file}")
    
    # 如果未指定输出文件，自动生成输出文件名
    if output_file is None:
        filename, ext = os.path.splitext(input_file)
        output_file = f"{filename}_带分区信息{ext}" # Modified output filename
    
    # 加载期刊影响因子和分区信息文件
    print(f"正在加载期刊影响因子和分区信息: {reference_file}")
    try:
        journal_issn_dict = {}
        print_issns = set() 
        
        try:
            with open(reference_file, 'r', encoding='gbk') as f:
                reader = csv.reader(f)
                next(reader) 
                
                for i, row in enumerate(reader, 2):
                    if len(row) < 9:
                        print(f"警告: 参考文件行 {i} 格式不正确，跳过: {row}")
                        continue
                    
                    journal_name = row[0].strip() if row[0] else ""
                    print_issn = row[2].strip() if len(row) > 2 and row[2] else ""
                    e_issn = row[3].strip() if len(row) > 3 and row[3] else ""
                    
                    category = jcr_quartile = impact_2023 = impact_5year = ranking = ""
                    try:
                        category = row[6].strip() if len(row) > 6 and row[6] else ""
                        jcr_quartile = row[7].strip() if len(row) > 7 and row[7] else ""
                        impact_2023 = row[8].strip() if len(row) > 8 and row[8] else ""
                        impact_5year = row[9].strip() if len(row) > 9 and row[9] else ""
                        ranking = row[10].strip() if len(row) > 10 and row[10] else ""
                    except IndexError:
                        # This handles cases where a row might be shorter than expected after column 8
                        pass # Variables will remain empty strings
                    
                    journal_info = {
                        '期刊名称': journal_name,
                        '大类分区': category,
                        'JCR分区': jcr_quartile, 
                        '2023最新IF': impact_2023,
                        '5年IF': impact_5year,
                        '排名': ranking
                    }
                    
                    if print_issn and print_issn != "N/A":
                        print_issn_norm = print_issn.replace('-', '').replace(' ', '')
                        print_issns.add(print_issn_norm)
                        journal_issn_dict[print_issn_norm] = journal_info
                    
                    if e_issn and e_issn != "N/A":
                        e_issn_norm = e_issn.replace('-', '').replace(' ', '')
                        journal_issn_dict[e_issn_norm] = journal_info
        except UnicodeDecodeError:
             print(f"尝试使用 GBK 编码读取 {reference_file} 失败，请检查文件编码。")
             sys.exit(1)

        print(f"成功加载了 {len(journal_issn_dict)} 条期刊ISSN信息")
        if print_issns:
            print(f"示例ISSN (来自参考文件): {list(print_issns)[:5]}")
        else:
            print("警告: 参考文件中未加载到任何有效的ISSN。")


    except Exception as e:
        print(f"加载期刊信息文件时出错: {e}")
        sys.exit(1)
    
    issn_column_index = -1 # Initialize outside try block
    try:
        with open(input_file, 'r', encoding='utf-8') as infile_peek:
            reader_peek = csv.reader(infile_peek)
            header_peek = next(reader_peek)
            
            for i, col_name in enumerate(header_peek):
                if 'ISSN' in col_name:
                    issn_column_index = i
                    break
            
            if issn_column_index == -1:
                issn_column_index = 1 
                print(f"警告: 未在表头找到'ISSN'列，默认使用第2列 (索引1) 作为ISSN列: '{header_peek[issn_column_index] if len(header_peek) > 1 else '未知'}'")
            else:
                print(f"找到ISSN列: {header_peek[issn_column_index]} (索引{issn_column_index})")
    except Exception as e:
        print(f"读取CSV头部以确定ISSN列时出错: {e}. 将默认使用第2列 (索引1) 作为ISSN列。")
        issn_column_index = 1
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:

            reader = csv.reader(infile)
            writer = csv.writer(outfile)
            
            header = next(reader)
            
            # --- MODIFICATION START: Add "序号" to header ---
            new_info_headers = ['大类分区', 'JCR分区', '2023最新IF', '5年IF', '排名']
            
            # Construct the new header
            # 1. "序号"
            # 2. Original columns up to and including ISSN
            # 3. New info columns (IF, JCR, etc.)
            # 4. Original columns after ISSN
            output_header = ['序号'] + \
                            header[:issn_column_index+1] + \
                            new_info_headers + \
                            header[issn_column_index+1:]
            # --- MODIFICATION END ---
            writer.writerow(output_header)
            
            processed_rows = 0
            matched_rows = 0
            serial_number = 0 # --- MODIFICATION: Initialize serial number ---
            
            for row_index, row_data in enumerate(reader, 1): # Start row_index from 1 for logging
                serial_number += 1 # --- MODIFICATION: Increment serial number for each data row ---
                processed_rows += 1
                
                if len(row_data) <= issn_column_index:
                    print(f"警告: 输入文件行 {row_index+1} (数据行 {serial_number}) 结构异常，列数不足，跳过: {row_data}")
                    # Write original row with prepended serial and empty slots for new info to maintain structure
                    empty_info = [''] * len(new_info_headers)
                    writer.writerow([str(serial_number)] + row_data + empty_info + [''] * (len(output_header) - len(row_data) - 1 - len(empty_info)))
                    continue
                
                issn_raw = row_data[issn_column_index].strip()
                issn = issn_raw.replace('-', '').replace(' ', '')
                
                journal_info_to_add = None # Use this to store found journal data
                
                if issn: # Only search if ISSN is not empty
                    journal_info_to_add = journal_issn_dict.get(issn)

                if journal_info_to_add:
                    matched_rows += 1
                    current_journal_info_values = [
                        journal_info_to_add['大类分区'], 
                        journal_info_to_add['JCR分区'], 
                        journal_info_to_add['2023最新IF'], 
                        journal_info_to_add['5年IF'], 
                        journal_info_to_add['排名']
                    ]
                else: # No match by ISSN or ISSN was empty
                    # Attempt journal name matching only if ISSN match failed
                    journal_name_from_input = ""
                    if len(row_data) > 0: # Check if first column (often journal name) exists
                         journal_name_from_input = row_data[0].strip().lower()

                    if journal_name_from_input: # Only attempt name match if name is not empty
                        found_by_name = False
                        for ref_issn_key, ref_journal_info in journal_issn_dict.items():
                            ref_journal_name = ref_journal_info['期刊名称'].lower()
                            if journal_name_from_input and ref_journal_name and \
                               (journal_name_from_input in ref_journal_name or ref_journal_name in journal_name_from_input):
                                journal_info_to_add = ref_journal_info
                                matched_rows += 1
                                found_by_name = True
                                # print(f"通过期刊名模糊匹配: '{row_data[0]}' -> '{ref_journal_info['期刊名称']}' (ISSN: {issn_raw if issn_raw else '无'})")
                                break
                        if found_by_name:
                             current_journal_info_values = [
                                journal_info_to_add['大类分区'], 
                                journal_info_to_add['JCR分区'], 
                                journal_info_to_add['2023最新IF'], 
                                journal_info_to_add['5年IF'], 
                                journal_info_to_add['排名']
                            ]
                        else:
                            if issn: # Only print "not found" if an ISSN was actually provided
                                print(f"未找到匹配: 数据行 {serial_number}, ISSN: '{issn_raw}', 期刊: '{row_data[0] if len(row_data) > 0 else 'N/A'}'")
                            current_journal_info_values = [''] * len(new_info_headers) # Empty values
                    else: # No journal name in input to attempt matching
                        if issn: # Only print "not found" if an ISSN was actually provided
                            print(f"未找到匹配: 数据行 {serial_number}, ISSN: '{issn_raw}', 期刊: 'N/A'")
                        current_journal_info_values = [''] * len(new_info_headers) # Empty values

                # --- MODIFICATION START: Construct new row with serial number ---
                # 1. Serial number
                # 2. Original columns up to and including ISSN
                # 3. New info values (found or empty)
                # 4. Original columns after ISSN
                output_row_data = [str(serial_number)] + \
                                  row_data[:issn_column_index+1] + \
                                  current_journal_info_values + \
                                  row_data[issn_column_index+1:]
                # --- MODIFICATION END ---
                writer.writerow(output_row_data)
            
            print(f"处理完成! 共处理 {processed_rows} 行数据，成功匹配 {matched_rows} 条期刊信息")
            print(f"已将结果保存至: {output_file}")
            
            if processed_rows > 0 and matched_rows == 0:
                print("警告: 未找到任何匹配的期刊信息。请检查：")
                print("  1. 输入文件中的ISSN列和格式是否正确。")
                print("  2. 参考文件中的ISSN是否与输入文件中的ISSN匹配。")
                print("  3. 参考文件是否正确加载（编码、内容）。")
                
            return output_file
            
    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_file}' 未找到。")
        sys.exit(1)
    except Exception as e:
        print(f"处理CSV文件时发生意外错误: {e}")
        import traceback
        traceback.print_exc() # Print full traceback for debugging
        sys.exit(1)


def main():
    """主函数，处理命令行参数并执行文件处理"""
    import argparse
    
    parser = argparse.ArgumentParser(description='为PubMed检索结果CSV文件添加期刊影响因子、分区信息及序号列')
    parser.add_argument('input_file', help='PubMed检索结果CSV文件路径')
    parser.add_argument('--reference', '-r',
                        default='2024影响因子+2025年中科院分区.csv',
                        help='期刊影响因子和分区信息CSV文件路径 (默认: 2024影响因子+2025年中科院分区.csv)')
    parser.add_argument('--output', '-o', help='输出CSV文件路径 (可选, 自动生成)')
    
    args = parser.parse_args()
    
    if not os.path.isfile(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在或不是一个文件")
        sys.exit(1)
    
    if not os.path.isfile(args.reference):
        print(f"错误: 参考文件 '{args.reference}' 不存在或不是一个文件")
        sys.exit(1)
    
    process_pubmed_csv(args.input_file, args.reference, args.output)

if __name__ == "__main__":
    main()