#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PubMed文献信息增强工具（2025版本）

本脚本用于处理PubMed检索生成的CSV文件，为每篇文献添加期刊影响因子和分区信息。
支持新的2025年影响因子文件格式，并保持向后兼容性。
"""

import csv
import os
import sys


def detect_file_format(reference_file):
    """
    检测影响因子文件的格式版本
    
    参数:
        reference_file (str): 期刊影响因子和分区信息CSV文件路径
    
    返回:
        str: 'old' 或 'new'，表示文件格式版本
    """
    try:
        with open(reference_file, 'r', encoding='gbk') as f:
            reader = csv.reader(f)
            header = next(reader)
            
            # 检查是否包含新格式的特征字段
            if 'Journal' in header and 'CAS-zone' in header and '2024-IF' in header:
                return 'new'
            elif '名字' in header and '大类分区' in header and '2023最新IF' in header:
                return 'old'
            else:
                print("警告: 无法识别文件格式，尝试使用新格式")
                return 'new'
    except Exception as e:
        print(f"检测文件格式时出错: {e}")
        return 'new'


def process_pubmed_csv(input_file, reference_file, output_file=None):
    """
    处理PubMed检索结果CSV文件，添加期刊的影响因子和分区信息
    
    参数:
        input_file (str): PubMed检索结果CSV文件路径
        reference_file (str): 期刊影响因子和分区信息CSV文件路径
        output_file (str, optional): 输出CSV文件路径，如果不指定则自动生成
    
    返回:
        str: 处理后的CSV文件路径
    """
    print(f"正在处理PubMed检索结果文件: {input_file}")
    
    # 如果未指定输出文件，自动生成输出文件名
    if output_file is None:
        filename, ext = os.path.splitext(input_file)
        output_file = f"{filename}_带分区信息{ext}"
    
    # 检测文件格式
    file_format = detect_file_format(reference_file)
    print(f"检测到影响因子文件格式: {'2025新格式' if file_format == 'new' else '2024旧格式'}")
    
    # 加载期刊影响因子和分区信息文件
    print(f"正在加载期刊影响因子和分区信息: {reference_file}")
    try:
        # 创建ISSN索引以加快查找速度
        journal_issn_dict = {}
        print_issns = set()  # 用于调试
        
        # 手动读取CSV文件，尝试使用 gbk 编码
        try:
            with open(reference_file, 'r', encoding='gbk') as f:
                reader = csv.reader(f)
                # 跳过标题行
                header = next(reader)
                print(f"文件表头: {header}")
                
                for i, row in enumerate(reader, 2):  # 从第2行开始（考虑标题行）
                    if len(row) < 9:  # 确保行至少有9列
                        print(f"警告: 行 {i} 格式不正确，跳过")
                        continue
                    
                    # 根据文件格式提取数据
                    if file_format == 'new':
                        # 新格式: Journal 缩写 ISSN EISSN Catalog Publisher CAS-zone JCR-zone 2024-IF 5-year-IF JIF Rank
                        journal_name = row[0].strip() if row[0] else ""
                        print_issn = row[2].strip() if len(row) > 2 and row[2] else ""
                        e_issn = row[3].strip() if len(row) > 3 and row[3] else ""
                        
                        # 获取分区和影响因子信息
                        try:
                            cas_zone = row[6].strip() if len(row) > 6 and row[6] else ""  # CAS-zone
                            jcr_zone = row[7].strip() if len(row) > 7 and row[7] else ""  # JCR-zone
                            impact_2024 = row[8].strip() if len(row) > 8 and row[8] else ""  # 2024-IF
                            impact_5year = row[9].strip() if len(row) > 9 and row[9] else ""  # 5-year-IF
                            ranking = row[10].strip() if len(row) > 10 and row[10] else ""  # JIF Rank
                        except IndexError:
                            cas_zone = jcr_zone = impact_2024 = impact_5year = ranking = ""
                        
                        journal_info = {
                            '期刊名称': journal_name,
                            'CAS分区': cas_zone,
                            'JCR分区': jcr_zone,
                            '2024最新IF': impact_2024,
                            '5年IF': impact_5year,
                            '排名': ranking
                        }
                        
                    else:
                        # 旧格式: 名字 缩写 ISSN EISSN 目录 学科大类 大类分区 JCR分区 2023最新IF 5年IF 排名
                        journal_name = row[0].strip() if row[0] else ""
                        print_issn = row[2].strip() if len(row) > 2 and row[2] else ""
                        e_issn = row[3].strip() if len(row) > 3 and row[3] else ""
                        
                        # 获取分区和影响因子信息
                        try:
                            category = row[6].strip() if len(row) > 6 and row[6] else ""  # 大类分区
                            jcr_quartile = row[7].strip() if len(row) > 7 and row[7] else ""  # JCR分区
                            impact_2023 = row[8].strip() if len(row) > 8 and row[8] else ""  # 2023最新IF
                            impact_5year = row[9].strip() if len(row) > 9 and row[9] else ""  # 5年IF
                            ranking = row[10].strip() if len(row) > 10 and row[10] else ""  # 排名
                        except IndexError:
                            category = jcr_quartile = impact_2023 = impact_5year = ranking = ""
                        
                        journal_info = {
                            '期刊名称': journal_name,
                            'CAS分区': category,  # 旧格式的"大类分区"对应新格式的CAS分区
                            'JCR分区': jcr_quartile,
                            '2024最新IF': impact_2023,  # 保持输出列名一致
                            '5年IF': impact_5year,
                            '排名': ranking
                        }
                    
                    # 将打印版和电子版ISSN都添加到字典中（如果有效）
                    if print_issn and print_issn != "N/A":
                        # 规范化ISSN格式
                        print_issn_norm = print_issn.replace('-', '').replace(' ', '')
                        print_issns.add(print_issn_norm)  # 用于调试
                        journal_issn_dict[print_issn_norm] = journal_info
                    
                    if e_issn and e_issn != "N/A":
                        # 规范化ISSN格式
                        e_issn_norm = e_issn.replace('-', '').replace(' ', '')
                        journal_issn_dict[e_issn_norm] = journal_info
                        
        except UnicodeDecodeError:
            print(f"尝试使用 GBK 编码读取 {reference_file} 失败，尝试UTF-8编码...")
            try:
                with open(reference_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    # 跳过标题行
                    header = next(reader)
                    print(f"文件表头: {header}")
                    
                    for i, row in enumerate(reader, 2):
                        if len(row) < 9:
                            print(f"警告: 行 {i} 格式不正确，跳过")
                            continue
                        
                        # 根据文件格式提取数据（重复上面的逻辑）
                        if file_format == 'new':
                            journal_name = row[0].strip() if row[0] else ""
                            print_issn = row[2].strip() if len(row) > 2 and row[2] else ""
                            e_issn = row[3].strip() if len(row) > 3 and row[3] else ""
                            
                            try:
                                cas_zone = row[6].strip() if len(row) > 6 and row[6] else ""
                                jcr_zone = row[7].strip() if len(row) > 7 and row[7] else ""
                                impact_2024 = row[8].strip() if len(row) > 8 and row[8] else ""
                                impact_5year = row[9].strip() if len(row) > 9 and row[9] else ""
                                ranking = row[10].strip() if len(row) > 10 and row[10] else ""
                            except IndexError:
                                cas_zone = jcr_zone = impact_2024 = impact_5year = ranking = ""
                            
                            journal_info = {
                                '期刊名称': journal_name,
                                'CAS分区': cas_zone,
                                'JCR分区': jcr_zone,
                                '2024最新IF': impact_2024,
                                '5年IF': impact_5year,
                                '排名': ranking
                            }
                        else:
                            journal_name = row[0].strip() if row[0] else ""
                            print_issn = row[2].strip() if len(row) > 2 and row[2] else ""
                            e_issn = row[3].strip() if len(row) > 3 and row[3] else ""
                            
                            try:
                                category = row[6].strip() if len(row) > 6 and row[6] else ""
                                jcr_quartile = row[7].strip() if len(row) > 7 and row[7] else ""
                                impact_2023 = row[8].strip() if len(row) > 8 and row[8] else ""
                                impact_5year = row[9].strip() if len(row) > 9 and row[9] else ""
                                ranking = row[10].strip() if len(row) > 10 and row[10] else ""
                            except IndexError:
                                category = jcr_quartile = impact_2023 = impact_5year = ranking = ""
                            
                            journal_info = {
                                '期刊名称': journal_name,
                                'CAS分区': category,
                                'JCR分区': jcr_quartile,
                                '2024最新IF': impact_2023,
                                '5年IF': impact_5year,
                                '排名': ranking
                            }
                        
                        # 添加ISSN映射
                        if print_issn and print_issn != "N/A":
                            print_issn_norm = print_issn.replace('-', '').replace(' ', '')
                            print_issns.add(print_issn_norm)
                            journal_issn_dict[print_issn_norm] = journal_info
                        
                        if e_issn and e_issn != "N/A":
                            e_issn_norm = e_issn.replace('-', '').replace(' ', '')
                            journal_issn_dict[e_issn_norm] = journal_info
                            
            except UnicodeDecodeError:
                print(f"无法读取 {reference_file}，请检查文件编码（应为GBK或UTF-8）。")
                sys.exit(1)

        print(f"成功加载了 {len(journal_issn_dict)} 条期刊ISSN信息")
        # 打印一些ISSN以供调试
        print(f"示例ISSN: {list(print_issns)[:5] if print_issns else '无'}")

    except Exception as e:
        print(f"加载期刊信息文件时出错: {e}")
        sys.exit(1)
    
    # 先读取CSV文件头部以确定ISSN列的位置 (输入文件通常是UTF-8)
    try:
        with open(input_file, 'r', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            header = next(reader)
            
            # 查找ISSN列的索引
            issn_column_index = -1
            for i, col_name in enumerate(header):
                if 'ISSN' in col_name:
                    issn_column_index = i
                    break
            
            # 如果没有找到ISSN列，默认使用第2列
            if issn_column_index == -1:
                issn_column_index = 1
                print(f"警告: 未找到ISSN列，默认使用第2列 (索引1) 作为ISSN列")
            else:
                print(f"找到ISSN列: {header[issn_column_index]} (索引{issn_column_index})")
    except Exception as e:
        print(f"读取CSV头部时出错: {e}")
        issn_column_index = 1  # 默认使用第2列
    
    # 处理PubMed检索结果CSV文件 (输入文件通常是UTF-8, 输出也用UTF-8)
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:

            # 读取CSV文件
            reader = csv.reader(infile)
            writer = csv.writer(outfile)
            
            # 读取头行
            header = next(reader)
            
            # 创建新的表头，在ISSN列后添加新的列
            new_header = header[:issn_column_index+1] + \
                        ['CAS分区', 'JCR分区', '2024最新IF', '5年IF', '排名'] + \
                        header[issn_column_index+1:]
            writer.writerow(new_header)
            
            # 处理每一行数据
            processed_rows = 0
            matched_rows = 0
            
            for row in reader:
                processed_rows += 1
                
                # 防止索引错误
                if len(row) <= issn_column_index:
                    print(f"警告: 行 {processed_rows+1} 结构异常，跳过")
                    continue
                
                # 获取ISSN并处理
                issn_raw = row[issn_column_index].strip()
                # 标准化ISSN (移除连字符和空格)
                issn = issn_raw.replace('-', '').replace(' ', '')
                
                # 在字典中查找对应的期刊信息
                journal_data = journal_issn_dict.get(issn, None)
                
                if journal_data:
                    # 找到匹配数据
                    matched_rows += 1
                    new_row = row[:issn_column_index+1] + \
                             [journal_data['CAS分区'], 
                              journal_data['JCR分区'], 
                              journal_data['2024最新IF'], 
                              journal_data['5年IF'], 
                              journal_data['排名']] + \
                             row[issn_column_index+1:]
                else:
                    # 尝试查找类似的ISSN
                    found = False
                    
                    # 检查期刊名称匹配
                    if issn_column_index > 0:  # 确保有期刊名列
                        journal_name = row[0].strip() if len(row) > 0 else ""
                        
                        # 尝试通过期刊名进行模糊匹配
                        for issn_key, journal_info in journal_issn_dict.items():
                            if journal_name.lower() in journal_info['期刊名称'].lower() or journal_info['期刊名称'].lower() in journal_name.lower():
                                journal_data = journal_info
                                matched_rows += 1
                                found = True
                                print(f"通过期刊名匹配: {journal_name} -> {journal_info['期刊名称']}")
                                break
                    
                    if found:
                        new_row = row[:issn_column_index+1] + \
                                 [journal_data['CAS分区'], 
                                  journal_data['JCR分区'], 
                                  journal_data['2024最新IF'], 
                                  journal_data['5年IF'], 
                                  journal_data['排名']] + \
                                 row[issn_column_index+1:]
                    else:
                        # 打印未匹配的ISSN以便调试
                        print(f"未找到匹配: 行 {processed_rows+1}, ISSN: {issn_raw}, 期刊: {row[0] if len(row) > 0 else 'N/A'}")
                        # 未找到匹配数据，添加空值
                        new_row = row[:issn_column_index+1] + ['', '', '', '', ''] + row[issn_column_index+1:]
                
                writer.writerow(new_row)
            
            print(f"处理完成! 共处理 {processed_rows} 行数据，成功匹配 {matched_rows} 条期刊信息")
            print(f"已将结果保存至: {output_file}")
            
            if matched_rows == 0:
                print("警告: 未找到任何匹配的期刊信息，请检查ISSN格式是否一致或查看未匹配的ISSN列表")
                
            return output_file
            
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")
        sys.exit(1)


def main():
    """主函数，处理命令行参数并执行文件处理"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='为PubMed检索结果CSV文件添加期刊影响因子和分区信息')
    parser.add_argument('input_file', help='PubMed检索结果CSV文件路径')
    parser.add_argument('--reference', '-r',
                        default='2025影响因子+2025年中科院分区.csv',  # 更新为新文件名
                        help='期刊影响因子和分区信息CSV文件路径')
    parser.add_argument('--output', '-o', help='输出CSV文件路径')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.isfile(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        sys.exit(1)
    
    if not os.path.isfile(args.reference):
        print(f"错误: 参考文件 '{args.reference}' 不存在")
        sys.exit(1)
    
    # 处理文件并输出结果
    process_pubmed_csv(args.input_file, args.reference, args.output)


if __name__ == "__main__":
    main()