#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ISSN 筛选与 PubMed 查询生成工具

本脚本读取包含 ISSN 和期刊信息的 CSV 文件，
根据用户指定的筛选条件（中科院分区、JCR 分区、影响因子范围）进行筛选，
并将筛选出的 ISSN 格式化为 PubMed 查询字符串，保存到 Markdown 文件中。
"""

import csv
import os
import sys
import argparse
import math

def normalize_zone(zone):
    """规范化分区字符串，转为大写并移除空格。"""
    if zone:
        return zone.strip().upper()
    return ""

def parse_float_safe(value):
    """安全地将字符串转换为浮点数，处理空值和非数字值。"""
    if value is None or value == '':
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None # 返回 None 表示无法转换或无效值

def filter_and_format_issns(input_csv_file, cas_zones=None, jcr_zones=None, if_min=None, if_max=None):
    """
    根据条件筛选 CSV 文件，提取 ISSN 并格式化为 PubMed 查询字符串。

    参数:
        input_csv_file (str): 输入的 CSV 文件路径。
                              预期列: ISSN, 大类分区, JCR分区, 影响因子 (或其他顺序，通过索引访问)
        cas_zones (list, optional): 要筛选的中科院分区列表 (字符串)。
        jcr_zones (list, optional): 要筛选的 JCR 分区列表 (字符串)。
        if_min (float, optional): 影响因子最小值。
        if_max (float, optional): 影响因子最大值。

    返回:
        str: 格式化后的 PubMed 查询字符串，或者在没有匹配时返回空字符串。
    """
    print(f"正在处理输入文件: {input_csv_file}")
    print("筛选条件:")
    print(f"  - 中科院分区: {cas_zones if cas_zones else '无'}")
    print(f"  - JCR 分区: {jcr_zones if jcr_zones else '无'}")
    print(f"  - 影响因子范围: {if_min if if_min is not None else '-inf'} 到 {if_max if if_max is not None else '+inf'}")

    filtered_issns = []
    processed_rows = 0
    matched_rows = 0

    # 规范化 JCR 分区条件
    norm_jcr_zones = {normalize_zone(z) for z in jcr_zones} if jcr_zones else None
    # 中科院分区条件直接使用传入的数字字符串列表
    cas_zone_filter_set = set(cas_zones) if cas_zones else None

    try:
        # 尝试使用 utf-8-sig 打开 (处理 BOM), 失败则尝试 gbk
        try:
            f_in = open(input_csv_file, 'r', encoding='utf-8-sig', errors='replace')
        except UnicodeDecodeError:
            print("警告: 使用 UTF-8-SIG 打开失败，尝试 GBK...")
            f_in = open(input_csv_file, 'r', encoding='gbk', errors='replace')

        with f_in:
            reader = csv.reader(f_in)
            try:
                header = next(reader) # 读取表头
                print(f"CSV 表头: {header}")
                # 尝试找到列索引 (假设列名稳定)
                try:
                    issn_col_idx = header.index("ISSN")
                    cas_col_idx = header.index("大类分区")
                    jcr_col_idx = header.index("JCR分区")
                    if_col_idx = header.index("影响因子")
                except ValueError as e:
                    print(f"错误: 输入 CSV 文件 '{input_csv_file}' 缺少预期的列名 ({e})。请确保包含 'ISSN', '大类分区', 'JCR分区', '影响因子'。")
                    # 如果找不到列名，可以尝试使用固定索引，但这不太健壮
                    # print("警告: 无法通过列名找到所有必需列，将尝试使用默认索引 (0, 1, 2, 3)。")
                    # issn_col_idx, cas_col_idx, jcr_col_idx, if_col_idx = 0, 1, 2, 3
                    sys.exit(1) # 或者直接退出

            except StopIteration:
                print(f"错误: 输入 CSV 文件 '{input_csv_file}' 为空。")
                return "" # 返回空查询

            for i, row in enumerate(reader, 2): # 从第二行数据开始
                processed_rows += 1
                if len(row) <= max(issn_col_idx, cas_col_idx, jcr_col_idx, if_col_idx):
                    # print(f"警告: 第 {i} 行数据列数不足，跳过。行内容: {row}")
                    continue

                # 提取数据
                issn = row[issn_col_idx].strip()
                cas_zone_raw = row[cas_col_idx].strip()
                jcr_zone_raw = row[jcr_col_idx].strip()
                impact_factor_raw = row[if_col_idx].strip()

                if not issn: # 跳过没有 ISSN 的行
                    continue

                # 规范化 JCR 分区和影响因子
                # cas_zone_raw 直接使用原始数字字符串
                jcr_zone_norm = normalize_zone(jcr_zone_raw)
                impact_factor = parse_float_safe(impact_factor_raw)

                # --- 应用筛选条件 ---
                match = True # 假设默认匹配

                # 1. 中科院分区筛选 (直接比较数字字符串)
                if cas_zone_filter_set and cas_zone_raw not in cas_zone_filter_set:
                    match = False

                # 2. JCR 分区筛选 (如果还匹配)
                if match and norm_jcr_zones and jcr_zone_norm not in norm_jcr_zones:
                    match = False

                # 3. 影响因子筛选 (如果还匹配)
                if match and impact_factor is not None: # 只有当影响因子有效时才进行比较
                    if if_min is not None and impact_factor < if_min:
                        match = False
                    if match and if_max is not None and impact_factor > if_max:
                        match = False
                elif match and (if_min is not None or if_max is not None):
                    # 如果设置了 IF 范围，但当前行的 IF 无效，则不匹配
                    match = False


                # 如果所有条件都满足
                if match:
                    matched_rows += 1
                    filtered_issns.append(issn)

        print(f"处理了 {processed_rows} 行数据，匹配了 {matched_rows} 行。")

    except FileNotFoundError:
        print(f"错误: 输入 CSV 文件 '{input_csv_file}' 未找到。")
        sys.exit(1)
    except Exception as e:
        print(f"读取或处理 CSV 文件 '{input_csv_file}' 时出错: {e}")
        sys.exit(1)

    # --- 格式化 PubMed 查询 ---
    if not filtered_issns:
        print("未找到满足条件的 ISSN。")
        return "" # 返回空字符串

    # 构建查询部分 - 使用正确的 [ISSN] 标签
    query_parts = [f'("{issn}"[ISSN])' for issn in filtered_issns]

    # 用 OR 连接
    if len(query_parts) == 1:
        pubmed_query = query_parts[0]
    else:
        pubmed_query = f"({' OR '.join(query_parts)})"

    print(f"生成的 PubMed 查询片段 (共 {len(filtered_issns)} 个 ISSN):")
    # print(pubmed_query) # 打印完整查询可能太长，只打印部分或提示
    print(f"  示例: {query_parts[0]}{' OR ...' if len(query_parts) > 1 else ''}")

    return pubmed_query


def save_query_to_md(query_string, output_md_file):
    """将查询字符串保存到 Markdown 文件。"""
    try:
        with open(output_md_file, 'w', encoding='utf-8') as f_out:
            if query_string:
                f_out.write("```\n")
                f_out.write(query_string + "\n")
                f_out.write("```\n")
                print(f"PubMed 查询已成功保存到: {output_md_file}")
            else:
                f_out.write("没有找到满足筛选条件的 ISSN。\n")
                print(f"未生成查询，提示信息已写入: {output_md_file}")
    except Exception as e:
        print(f"写入 Markdown 文件 '{output_md_file}' 时出错: {e}")
        sys.exit(1)


def main():
    """主函数，处理命令行参数并执行筛选和格式化。"""
    parser = argparse.ArgumentParser(description='根据条件筛选包含 ISSN 信息的 CSV 文件，并生成 PubMed 查询字符串保存到 MD 文件。')

    parser.add_argument('input_csv', help='输入的 CSV 文件路径 (包含 ISSN, 大类分区, JCR分区, 影响因子列)。')
    parser.add_argument('--cas-zones', nargs='+', help='要筛选的中科院分区列表 (例如: "1区" "2区")。')
    parser.add_argument('--jcr-zones', nargs='+', help='要筛选的 JCR 分区列表 (例如: Q1 Q2)。')
    parser.add_argument('--if-min', type=float, help='影响因子最小值 (包含)。')
    parser.add_argument('--if-max', type=float, help='影响因子最大值 (包含)。')
    parser.add_argument('--output-md', '-o', help='输出 Markdown 文件路径 (可选，默认根据输入 CSV 文件名生成)。')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.isfile(args.input_csv):
        print(f"错误: 输入 CSV 文件 '{args.input_csv}' 不存在。")
        sys.exit(1)

    # 确定输出文件名
    output_md_file = args.output_md
    if output_md_file is None:
        input_filename, _ = os.path.splitext(args.input_csv)
        output_md_file = f"{input_filename}.md"
        print(f"未指定输出文件，将保存为: {output_md_file}")

    # 执行筛选和格式化
    pubmed_query = filter_and_format_issns(
        args.input_csv,
        cas_zones=args.cas_zones,
        jcr_zones=args.jcr_zones,
        if_min=args.if_min,
        if_max=args.if_max
    )

    # 保存到 Markdown 文件
    save_query_to_md(pubmed_query, output_md_file)

if __name__ == "__main__":
    main()
