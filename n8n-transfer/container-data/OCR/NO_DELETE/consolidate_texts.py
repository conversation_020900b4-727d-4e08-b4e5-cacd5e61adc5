import os
import glob
import re # 导入正则表达式模块

def natural_sort_key(s):
    """
    自然排序的辅助函数。
    例如: "file1.txt" -> ["file", 1, ".txt"]
           "file10.txt" -> ["file", 10, ".txt"]
    """
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split(r'([0-9]+)', s)]

def consolidate_ocr_texts():
    # 容器内单个 OCR .txt 文件生成的路径
    ocr_input_dir = "/data/OCR/"
    # 合并后的输出文件路径，也在容器内
    consolidated_output_file = os.path.join(ocr_input_dir, "consolidated_ocr_output.txt")

    txt_files_to_combine = []

    # 查找 ocr_input_dir 目录中所有的 .txt 文件
    for filepath in glob.glob(os.path.join(ocr_input_dir, "*.txt")):
        # 如果合并后的输出文件已存在，则排除它本身，防止重复读取
        if os.path.basename(filepath) == os.path.basename(consolidated_output_file):
            continue
        txt_files_to_combine.append(filepath)

    # 使用自然排序对文件路径列表进行排序
    # 我们基于文件名 (os.path.basename(filepath)) 进行排序
    txt_files_to_combine.sort(key=lambda filepath: natural_sort_key(os.path.basename(filepath)))

    print(f"找到 {len(txt_files_to_combine)} 个 .txt 文件进行合并 (已按自然顺序排序):")
    for f_path in txt_files_to_combine:
        print(f" - {f_path}")

    with open(consolidated_output_file, "w", encoding="utf-8") as outfile:
        for i, filepath in enumerate(txt_files_to_combine):
            filename = os.path.basename(filepath) # 获取文件名，例如 "image1.txt"
            try:
                with open(filepath, "r", encoding="utf-8") as infile:
                    content = infile.read()
                    
                    # 写入文件名作为开头
                    outfile.write(f"--- 文件名: {filename} ---\n")
                    outfile.write(content)
                    
                    # 在不同文件的内容之间添加清晰的分隔符
                    if i < len(txt_files_to_combine) - 1: # 不在最后一个文件后添加分隔符
                        outfile.write("\n\n--- DOCUMENT SEPARATOR ---\n\n")
                print(f"已添加文件 '{filename}' 的内容。")
            except Exception as e:
                print(f"读取文件 {filepath} 时出错: {e}")

    print(f"所有 .txt 文件已合并到: {consolidated_output_file}")

if __name__ == "__main__":
    consolidate_ocr_texts()