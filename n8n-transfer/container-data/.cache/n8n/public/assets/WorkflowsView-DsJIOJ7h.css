/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._card_pt4ir_123 {
  transition: box-shadow 0.3s ease;
  cursor: pointer;
}
._card_pt4ir_123:hover {
  box-shadow: 0 2px 8px rgba(68, 28, 23, 0.1);
}
._folder-icon_pt4ir_131 {
  width: var(--spacing-xl);
  height: var(--spacing-xl);
  flex-shrink: 0;
  color: var(--color-text-base);
  align-content: center;
  text-align: center;
}
._card-header_pt4ir_140 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: var(--spacing-xs);
  margin-bottom: var(--spacing-5xs);
}
._card-footer_pt4ir_148 {
  display: flex;
}
._info-cell_pt4ir_152 + ._info-cell_pt4ir_152::before {
  content: "|";
  margin: 0 var(--spacing-4xs);
}
._cardBadge_pt4ir_157._with-breadcrumbs_pt4ir_157 .n8n-badge {
  padding-right: 0;
}
._cardBadge_pt4ir_157._with-breadcrumbs_pt4ir_157 .n8n-breadcrumbs {
  padding-left: var(--spacing-5xs);
}
._card-actions_pt4ir_164 {
  display: flex;
  gap: var(--spacing-xs);
}
@media screen and (max-width: 991px) {
._card_pt4ir_123 {
    flex-wrap: wrap;
}
._card_pt4ir_123 .n8n-card-append {
    width: 100%;
    margin-top: var(--spacing-3xs);
    padding-left: 40px;
}
._card_pt4ir_123 ._card-actions_pt4ir_164 {
    width: 100%;
    justify-content: space-between;
}
._info-cell--created_pt4ir_182 {
    display: none;
}
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._cardLink_ycbf3_123 {
  transition: box-shadow 0.3s ease;
  cursor: pointer;
  padding: 0;
  align-items: stretch;
}
._cardLink_ycbf3_123:hover {
  box-shadow: 0 2px 8px rgba(68, 28, 23, 0.1);
}
._cardHeading_ycbf3_133 {
  font-size: var(--font-size-s);
  word-break: break-word;
  padding: var(--spacing-s) 0 0 var(--spacing-s);
}
._cardHeading_ycbf3_133 span {
  color: var(--color-text-light);
}
._cardHeadingArchived_ycbf3_142 {
  color: var(--color-text-light);
}
._cardDescription_ycbf3_146 {
  min-height: 19px;
  display: flex;
  align-items: center;
  padding: 0 0 var(--spacing-s) var(--spacing-s);
}
._cardTags_ycbf3_153 {
  display: inline-block;
  margin-top: var(--spacing-4xs);
}
._cardActions_ycbf3_158 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  padding: 0 var(--spacing-s) 0 0;
  cursor: default;
}
._cardBadge_ycbf3_168 {
  background-color: var(--color-background-xlight);
}
._cardBadge_ycbf3_168._with-breadcrumbs_ycbf3_172 .n8n-badge {
  padding-right: 0;
}
._cardBadge_ycbf3_168._with-breadcrumbs_ycbf3_172 .n8n-breadcrumbs {
  padding-left: var(--spacing-5xs);
}
._cardArchived_ycbf3_179 {
  background-color: var(--color-background-light);
  border-color: var(--color-foreground-light);
  color: var(--color-text-base);
}
@media screen and (max-width: 991px) {
._cardLink_ycbf3_123 {
    --card--padding: 0 var(--spacing-s) var(--spacing-s);
    --card--append--width: 100%;
    flex-direction: column;
}
._cardActions_ycbf3_158 {
    width: 100%;
    padding: 0 var(--spacing-s) var(--spacing-s);
    justify-content: end;
}
._cardBadge_ycbf3_168,
  ._breadcrumbs_ycbf3_197 {
    margin-right: auto;
}
}
@media screen and (max-width: 767px) {
._breadcrumbs_ycbf3_197 > div {
    flex-direction: column;
}
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._suggested-workflow-callout_1alzo_123 {
  margin-top: var(--spacing-xs);
  padding-left: var(--spacing-s);
  padding-right: var(--spacing-m);
  border-style: dashed;
}
._suggested-workflow-callout_1alzo_123 ._callout-content_1alzo_129 {
  display: flex;
  flex-direction: column;
}
._suggested-workflow-callout_1alzo_123 ._callout-trailing-content_1alzo_133 {
  display: flex;
  align-items: center;
  gap: var(--spacing-m);
}
._suggested-workflow-callout_1alzo_123 a span span {
  color: var(--color-callout-secondary-font);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._suggested-workflows_t3ed6_123 {
  margin-top: var(--spacing-xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._actionsContainer_1puyr_123 {
  display: flex;
  justify-content: center;
}
._easy-ai-workflow-callout_1puyr_128 {
  margin-top: var(--spacing-xs);
  padding-left: var(--spacing-s);
  padding-right: var(--spacing-m);
}
._easy-ai-workflow-callout_1puyr_128 ._callout-trailing-content_1puyr_133 {
  display: flex;
  align-items: center;
  gap: var(--spacing-m);
}
._emptyStateCard_1puyr_139 {
  width: 192px;
  text-align: center;
  display: inline-flex;
  height: 230px;
}
._emptyStateCard_1puyr_139 + ._emptyStateCard_1puyr_139 {
  margin-left: var(--spacing-s);
}
._emptyStateCard_1puyr_139:hover svg {
  color: var(--color-primary);
}
._emptyStateCardContent_1puyr_152 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
._emptyStateCardIcon_1puyr_159 {
  font-size: 48px;
}
._emptyStateCardIcon_1puyr_159 svg {
  transition: color 0.3s ease;
}
._add-folder-button_1puyr_166 {
  width: 30px;
  height: 30px;
}
._breadcrumbs-container_1puyr_171 {
  display: flex;
  align-items: center;
  align-self: flex-end;
}
._breadcrumbs-loading_1puyr_177 .el-skeleton__item {
  margin: 0;
  height: 40px;
  width: 400px;
}
._empty-folder-container_1puyr_183 button {
  margin-top: var(--spacing-2xs);
}
._drag-active_1puyr_187 *,
._drag-active_1puyr_187 .action-toggle {
  cursor: grabbing !important;
}
._dragging_1puyr_192 {
  transition: opacity 0.3s ease;
  opacity: 0.3;
  border-style: dashed;
  pointer-events: none;
}
._drop-active_1puyr_199 .card {
  border-color: var(--color-secondary);
  background-color: var(--color-callout-secondary-background);
}
._path-separator_1puyr_204 {
  font-size: var(--font-size-xl);
  color: var(--color-foreground-base);
  padding: var(--spacing-3xs) var(--spacing-4xs) var(--spacing-4xs);
}
._name_1puyr_210 {
  color: var(--color-text-dark);
  font-size: var(--font-size-s);
  padding: var(--spacing-3xs) var(--spacing-4xs) var(--spacing-4xs);
}
._pointer-disabled_1puyr_216 {
  pointer-events: none;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.add-folder-modal {
  width: 500px;
  padding-bottom: 0;
}
.add-folder-modal .el-message-box__message {
  font-size: var(--font-size-xl);
}
.add-folder-modal .el-message-box__btns {
  padding: 0 var(--spacing-l) var(--spacing-l);
}
.add-folder-modal .el-message-box__content {
  padding: var(--spacing-l);
}