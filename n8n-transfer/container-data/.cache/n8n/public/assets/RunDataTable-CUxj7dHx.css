/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._dataDisplay_1dwr4_123 {
  position: absolute;
  top: 0;
  left: 0;
  padding-left: var(--spacing-s);
  right: 0;
  overflow-y: auto;
  line-height: 1.5;
  word-break: normal;
  height: 100%;
  padding-bottom: var(--spacing-3xl);
}
._dataDisplay_1dwr4_123._compact_1dwr4_135 {
  padding-left: var(--spacing-2xs);
}
._table_1dwr4_139 {
  border-collapse: separate;
  text-align: left;
  width: 100%;
  font-size: var(--font-size-2xs);
  color: var(--color-text-base);
}
._table_1dwr4_139 th {
  background-color: var(--color-background-base);
  border-top: var(--border-base);
  border-bottom: var(--border-base);
  border-left: var(--border-base);
  position: sticky;
  top: 0;
  color: var(--color-text-dark);
  z-index: 1;
}
._lightHeader_1dwr4_156 ._table_1dwr4_139 th {
  background-color: var(--color-background-light);
}
._table_1dwr4_139 th._tableRightMargin_1dwr4_159 {
  background-color: transparent;
}
._table_1dwr4_139 td {
  vertical-align: top;
  padding: var(--spacing-4xs) var(--spacing-3xs);
  border-bottom: var(--border-base);
  border-left: var(--border-base);
  overflow-wrap: break-word;
  white-space: pre-wrap;
  vertical-align: top;
}
._table_1dwr4_139 td:first-child,
._table_1dwr4_139 td:nth-last-child(2) {
  position: relative;
  z-index: 0;
}
._table_1dwr4_139 td:first-child:after,
._table_1dwr4_139 td:nth-last-child(2):after {
  content: "";
  position: absolute;
  height: 100%;
  width: 2px;
  top: 0;
}
._table_1dwr4_139 td:nth-last-child(2):after {
  right: -1px;
}
._table_1dwr4_139 td:first-child:after {
  left: -1px;
}
._table_1dwr4_139 th:last-child,
._table_1dwr4_139 td:last-child {
  border-right: var(--border-base);
}
._nodeClass_1dwr4_195 {
  margin-bottom: var(--spacing-5xs);
}
._emptyCell_1dwr4_199 {
  height: 32px;
}
._header_1dwr4_203 {
  display: flex;
  align-items: center;
  padding: var(--spacing-4xs) var(--spacing-3xs);
}
._header_1dwr4_203 span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex-grow: 1;
}
._draggableHeader_1dwr4_215:hover {
  cursor: grab;
  background-color: var(--color-foreground-base);
}
._draggableHeader_1dwr4_215:hover ._dragButton_1dwr4_219 {
  opacity: 1;
}
._highlight_1dwr4_223 ._draggableHeader_1dwr4_215 {
  color: var(--color-primary);
}
._draggingHeader_1dwr4_227 {
  color: var(--color-primary);
  background-color: var(--color-primary-tint-2);
}
._activeHeader_1dwr4_232 ._dragButton_1dwr4_219 {
  opacity: 1;
}
._dragButton_1dwr4_219 {
  opacity: 0;
  margin-left: var(--spacing-2xs);
}
._dataKey_1dwr4_241 {
  color: var(--color-text-dark);
  line-height: 1.7;
  font-weight: var(--font-weight-bold);
  border-radius: var(--border-radius-base);
  padding: 0 var(--spacing-5xs) 0 var(--spacing-5xs);
  margin-right: var(--spacing-5xs);
}
._value_1dwr4_250 {
  line-height: var(--font-line-height-regular);
}
._nestedValue_1dwr4_254 {
  margin-left: var(--spacing-4xs);
}
._mappable_1dwr4_259 {
  cursor: grab;
}
._empty_1dwr4_199 {
  color: var(--color-danger);
}
._limitColWidth_1dwr4_267 {
  max-width: 300px;
}
._minColWidth_1dwr4_271 {
  min-width: 240px;
}
._hoveringKey_1dwr4_275 {
  background-color: var(--color-foreground-base);
}
._draggingKey_1dwr4_279 {
  background-color: var(--color-primary-tint-2);
}
._tableRightMargin_1dwr4_159 {
  width: var(--spacing-s);
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
}
._compact_1dwr4_135 ._tableRightMargin_1dwr4_159 {
  padding: 0;
  min-width: var(--spacing-2xs);
  max-width: var(--spacing-2xs);
}
._hoveringRow_1dwr4_295 td:first-child:after,
._hoveringRow_1dwr4_295 td:nth-last-child(2):after {
  background-color: var(--color-secondary);
}
._warningTooltip_1dwr4_300 {
  color: var(--color-warning);
}
._executionLinkCell_1dwr4_304 {
  padding: var(--spacing-3xs) !important;
}
._executionLinkRowHeader_1dwr4_308 {
  width: var(--spacing-m);
}