/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._heading_1ftgg_123 {
  margin-bottom: var(--spacing-s);
}
._switch_1ftgg_127 span {
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-light);
}
._buttons_1ftgg_133 {
  display: flex;
  justify-content: flex-start;
  padding: var(--spacing-2xl) 0 var(--spacing-2xs);
}
._buttons_1ftgg_133 button {
  margin: 0 var(--spacing-s) 0 0;
}
._group_1ftgg_142 {
  padding: var(--spacing-xl) 0 0;
}
._group_1ftgg_142 > label {
  display: inline-block;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  padding: 0 0 var(--spacing-2xs);
}
._group_1ftgg_142 small {
  display: block;
  padding: var(--spacing-2xs) 0 0;
  font-size: var(--font-size-2xs);
  color: var(--color-text-base);
}
._actionBox_1ftgg_158 {
  margin: var(--spacing-2xl) 0 0;
}
._footer_1ftgg_162 {
  color: var(--color-text-base);
  font-size: var(--font-size-2xs);
}