/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._ratingIcon_1gp02_123 {
  display: flex;
  flex-direction: row;
}
._ratingIcon_1gp02_123 ._icon_1gp02_127 {
  color: var(--color-text-light);
}
._ratingIcon_1gp02_123 ._icon_1gp02_127:not(._up_1gp02_130):not(._down_1gp02_130):hover {
  color: var(--color-primary);
}
._ratingIcon_1gp02_123 ._up_1gp02_130 {
  color: var(--color-success);
}
._ratingIcon_1gp02_123 ._down_1gp02_130 {
  color: var(--color-danger);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._highlightDataButton_snwak_123 {
  height: 30px;
  width: 30px;
}
._highlightDataButtonActive_snwak_128 {
  width: auto;
}
._highlightDataButtonOpen_snwak_132 {
  color: var(--color-primary);
  background-color: var(--color-button-secondary-hover-background);
  border-color: var(--color-button-secondary-hover-active-focus-border);
}
._badge_snwak_138 {
  border: 0;
}
._container_snwak_142 {
  z-index: 1;
  max-height: calc(100vh - 250px);
  width: 250px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--color-background-xlight);
  border: var(--border-base);
  border-radius: var(--border-radius-base);
}
._section_snwak_154 {
  padding: var(--spacing-s);
  display: flex;
  flex-direction: column;
}
._section_snwak_154:not(:last-child) {
  border-bottom: var(--border-base);
}
._metadata_snwak_163 {
  padding-top: var(--spacing-s);
}
._heading_snwak_167 {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  padding-right: var(--spacing-l);
}
._controls_snwak_174 {
  padding: var(--spacing-s) 0 var(--spacing-xs);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: var(--spacing-m);
}
._controls_snwak_174 button {
  display: flex;
  align-items: center;
}
._customDataEntry_snwak_186 {
  display: flex;
  flex-direction: column;
}
._customDataEntry_snwak_186:not(:first-of-type) {
  margin-top: var(--spacing-s);
}
._customDataEntry_snwak_186 ._key_snwak_193 {
  font-weight: var(--font-weight-bold);
}
._noResultsContainer_snwak_197 {
  width: 100%;
  margin-top: var(--spacing-s);
}
._execution-annotation-panel_snwak_202 :deep(._el-skeleton__item_snwak_202) {
  height: 60px;
  border-radius: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._tags_19zht_123 {
  display: block;
  margin-top: var(--spacing-4xs);
}
._addTagButton_19zht_128 {
  height: 24px;
  font-size: var(--font-size-2xs);
  white-space: nowrap;
  padding: var(--spacing-4xs) var(--spacing-3xs);
  background-color: var(--color-button-secondary-background);
  border: 1px solid var(--color-foreground-light);
  border-radius: var(--border-radius-base);
  font-weight: var(--font-weight-regular);
}
._addTagButton_19zht_128:hover {
  color: var(--color-primary);
  text-decoration: none;
  background-color: var(--color-button-secondary-hover-background);
  border: 1px solid var(--color-button-secondary-hover-active-focus-border);
  border-radius: var(--border-radius-base);
}
._addTagButton_19zht_128 span + span {
  margin-left: var(--spacing-4xs);
}
._addTagButtonIconOnly_19zht_149 {
  height: 20px;
  width: 20px;
}
._tagsContainer_19zht_154 {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-4xs);
  max-width: 360px;
}
._tagsContainer_19zht_154 .el-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: max-content;
  height: var(--tag-height);
  padding: var(--tag-padding);
  line-height: var(--tag-line-height);
  color: var(--tag-text-color);
  background-color: var(--tag-background-color);
  border: 1px solid var(--tag-border-color);
  border-radius: var(--tag-border-radius);
  font-size: var(--tag-font-size);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._previewContainer_1v3g2_123 {
  position: relative;
  height: 100%;
  overflow: hidden;
}
._executionDetails_1v3g2_129 {
  position: absolute;
  padding: var(--spacing-m);
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 150ms ease-in-out;
  pointer-events: none;
}
._executionDetails_1v3g2_129 > div:last-child {
  display: flex;
  align-items: center;
}
._executionDetails_1v3g2_129 * {
  pointer-events: all;
}
._executionDetailsLeft_1v3g2_147 {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-5xs);
}
._executionTitle_1v3g2_153 {
  display: flex;
  align-items: center;
  gap: var(--spacing-3xs);
}
._voteButtons_1v3g2_159 {
  margin-bottom: 2px;
}
._spinner_1v3g2_163 div div {
  width: 30px;
  height: 30px;
  border-width: 2px;
}
._running_1v3g2_169,
._spinner_1v3g2_163 {
  color: var(--color-warning);
}
._waiting_1v3g2_174 {
  color: var(--color-secondary);
}
._success_1v3g2_178 {
  color: var(--color-success);
}
._error_1v3g2_182 {
  color: var(--color-danger);
}
._newInfo_1v3g2_186,
._runningInfo_1v3g2_187 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: var(--spacing-4xl);
}
._newMessage_1v3g2_194,
._runningMessage_1v3g2_195 {
  width: 240px;
  margin-top: var(--spacing-l);
  text-align: center;
}
._debugLink_1v3g2_201 a > span {
  display: block;
  padding: var(--button-padding-vertical, var(--spacing-xs)) var(--button-padding-horizontal, var(--spacing-m));
}
._actions_1v3g2_206 {
  display: flex;
  gap: var(--spacing-2xs);
}
._highlightDataButton_1v3g2_211 {
  height: 30px;
  width: 30px;
}
._highlightDataButtonActive_1v3g2_216 {
  width: auto;
}
._highlightDataButtonOpen_1v3g2_220 {
  color: var(--color-primary);
  background-color: var(--color-button-secondary-hover-background);
  border-color: var(--color-button-secondary-hover-active-focus-border);
}
._badge_1v3g2_226 {
  border: 0;
}