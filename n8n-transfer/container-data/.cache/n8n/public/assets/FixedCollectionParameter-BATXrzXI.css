/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.fixed-collection-parameter[data-v-e6503b13] {
  padding-left: var(--spacing-s);
}
.fixed-collection-parameter .icon-button[data-v-e6503b13] {
  display: flex;
  flex-direction: column;
}
.fixed-collection-parameter .controls[data-v-e6503b13] .button {
  font-weight: var(--font-weight-normal);
  --button-font-color: var(--color-text-dark);
  --button-border-color: var(--color-foreground-base);
  --button-background-color: var(--color-background-base);
  --button-hover-font-color: var(--color-text-dark);
  --button-hover-border-color: var(--color-foreground-base);
  --button-hover-background-color: var(--color-background-base);
  --button-active-font-color: var(--color-text-dark);
  --button-active-border-color: var(--color-foreground-base);
  --button-active-background-color: var(--color-background-base);
  --button-focus-font-color: var(--color-text-dark);
  --button-focus-border-color: var(--color-foreground-base);
  --button-focus-background-color: var(--color-background-base);
}
.fixed-collection-parameter .controls[data-v-e6503b13] .button:active, .fixed-collection-parameter .controls[data-v-e6503b13] .button.active, .fixed-collection-parameter .controls[data-v-e6503b13] .button:focus {
  outline: none;
}
.fixed-collection-parameter-property[data-v-e6503b13] {
  margin: var(--spacing-xs) 0;
  margin-bottom: 0;
}
.parameter-item:hover > .parameter-item-wrapper > .icon-button[data-v-e6503b13] {
  opacity: 1;
}
.parameter-item[data-v-e6503b13] {
  position: relative;
  padding: 0 0 var(--spacing-s) var(--spacing-s);
}
.parameter-item + .parameter-item .parameter-item-wrapper .default-top-padding[data-v-e6503b13] {
  top: calc(1.2 * var(--spacing-s));
}
.parameter-item + .parameter-item .parameter-item-wrapper .extra-top-padding[data-v-e6503b13] {
  top: calc(2.2 * var(--spacing-s));
}
.parameter-item:first-of-type .parameter-item-wrapper .default-top-padding[data-v-e6503b13] {
  top: var(--spacing-3xs);
}
.parameter-item:first-of-type .parameter-item-wrapper .extra-top-padding[data-v-e6503b13] {
  top: var(--spacing-l);
}
.border-top-dashed[data-v-e6503b13] {
  border-top: 1px dashed #999;
}
.no-items-exist[data-v-e6503b13] {
  margin: var(--spacing-xs) 0;
}
.ghost[data-v-e6503b13],
.dragging[data-v-e6503b13] {
  border-radius: var(--border-radius-base);
  padding-right: var(--spacing-xs);
}
.ghost[data-v-e6503b13] {
  background-color: var(--color-background-base);
  opacity: 0.5;
}
.dragging[data-v-e6503b13] {
  background-color: var(--color-background-xlight);
  opacity: 0.7;
}
.dragging .parameter-item-wrapper[data-v-e6503b13] {
  border: none;
}