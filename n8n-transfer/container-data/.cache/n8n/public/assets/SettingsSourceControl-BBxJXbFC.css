/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._component_y1zn3_123 {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
._input_y1zn3_128 {
  margin-left: var(--spacing-3xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._group_1thng_123 {
  padding: 0 0 var(--spacing-s);
  width: 100%;
  display: block;
}
._group_1thng_123 hr {
  margin: 0 0 var(--spacing-xl);
  border: 1px solid var(--color-foreground-light);
}
._group_1thng_123 label {
  display: inline-block;
  padding: 0 0 var(--spacing-2xs);
  font-size: var(--font-size-s);
}
._group_1thng_123 small {
  display: inline-block;
  padding: var(--spacing-2xs) 0 0;
  font-size: var(--font-size-2xs);
  color: var(--color-text-light);
}
._readOnly_1thng_144 span {
  font-size: var(--font-size-s) !important;
}
._groupFlex_1thng_148 {
  display: flex;
  align-items: flex-start;
}
._groupFlex_1thng_148 > div {
  flex: 1;
}
._groupFlex_1thng_148 > div:last-child {
  margin-left: var(--spacing-2xs);
}
._groupFlex_1thng_148 input {
  width: 100%;
}
._connect_1thng_162 {
  margin: calc(var(--spacing-2xs) * -1) 0 var(--spacing-2xs);
}
._disconnectButton_1thng_166 {
  margin: 0 0 0 var(--spacing-2xs);
  height: 40px;
}
._actionBox_1thng_171 {
  margin: var(--spacing-2xl) 0 0;
}
._sshInput_1thng_175 {
  width: 100%;
  display: flex;
  align-items: center;
}
._sshInput_1thng_175 > div {
  flex: 1 1 auto;
}
._sshInput_1thng_175 > button {
  height: 42px;
}
._sshInput_1thng_175 ._copyInput_1thng_186 {
  margin: 0 var(--spacing-2xs);
}
._sshKeyTypeSelect_1thng_190 {
  min-width: 120px;
}
._copyInput_1thng_186 {
  overflow: auto;
}
._branchSelection_1thng_198 {
  display: flex;
}
._branchSelection_1thng_198 > div:first-child {
  flex: 1;
}
._branchSelection_1thng_198 > div:first-child input {
  height: 36px;
}
._branchSelection_1thng_198 button._refreshBranches_1thng_207 {
  height: 36px;
  width: 36px;
  margin-left: var(--spacing-xs);
}