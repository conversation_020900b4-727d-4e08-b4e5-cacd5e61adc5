import { L as LogsPanel } from "./LogsPanel-CutVxFbc.js";
import { d as defineComponent, p as useSettingsStore, Q as useWorkflowsStore, q as computed, e as createBlock, f as createCommentVNode, m as unref, g as openBlock } from "./index-B7FgXPOI.js";
import "./useClearExecutionButtonVisible-C3SJ4iFv.js";
import "./useCanvasOperations-B0S86JW0.js";
import "./RunData-DcTRJlPx.js";
import "./FileSaver.min-DmkzTNV9.js";
import "./useExecutionHelpers-BWNgdgwx.js";
import "./dateFormatter-yaAmYK7_.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "DemoFooter",
  setup(__props) {
    const { isNewLogsEnabled } = useSettingsStore();
    const workflowsStore = useWorkflowsStore();
    const hasExecutionData = computed(() => workflowsStore.workflowExecutionData);
    return (_ctx, _cache) => {
      return unref(isNewLogsEnabled) && hasExecutionData.value ? (openBlock(), createBlock(LogsPanel, {
        key: 0,
        "is-read-only": true
      })) : createCommentVNode("", true);
    };
  }
});
export {
  _sfc_main as default
};
