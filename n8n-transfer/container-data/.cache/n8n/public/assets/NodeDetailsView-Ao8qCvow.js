const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/RunDataTable-DEgXQifj.js","assets/index-CN4JmOoA.js","assets/index-DwKuVkBg.css","assets/useExecutionHelpers-Bet0YWvb.js","assets/dateFormatter-DP26ifk_.js","assets/RunDataTable-CX7gToH4.css","assets/RunDataJson-BRO48N-i.js","assets/import-curl-DVtpAPDa.js","assets/FileSaver.min-DbOfV2ME.js","assets/RunDataAi-DsidSvp-.js","assets/useCanvasOperations-BW3qnkOc.js","assets/RunDataAi-CUUDqZRP.css","assets/useWorkflowActivate-BZzKuzDM.js","assets/RunDataJson-Txw0lqay.css","assets/RunDataSearch-CNfhlvrK.js","assets/RunDataSearch-R6qtl0Jf.css"])))=>i.map(i=>d[i]);
import { h4 as hasKey, d as defineComponent, r as ref, q as computed, h as resolveComponent, i as createElementBlock, g as openBlock, n as normalizeClass, k as createBaseVNode, j as createVNode, bC as _sfc_main$l, w as withCtx, H as withKeys, J as withModifiers, l as createTextVNode, t as toDisplayString, m as unref, c as useI18n, e as createBlock, f as createCommentVNode, z as nextTick, _ as _export_sfc, au as useNDVStore, U as useWorkflowsStore, dH as isCommunityPackageName, cB as NPM_PACKAGE_DOCS_BASE_URL, h5 as BUILTIN_NODES_DOCS_URL, cD as COMMUNITY_NODES_INSTALLATION_DOCS_URL, aR as useExternalHooks, ai as useTelemetry, bu as NodeConnectionTypes, b as useRouter, ba as useClipboard, a7 as useWorkflowHelpers, a as useToast, h6 as OPEN_URL_PANEL_TRIGGER_NODE_TYPES, h7 as PRODUCTION_ONLY_TRIGGER_NODE_TYPES, eT as MCP_TRIGGER_NODE_TYPE, eK as FORM_TRIGGER_NODE_TYPE, bv as CHAT_TRIGGER_NODE_TYPE, I as watch, F as Fragment, D as renderList, G as onClickOutside, as as h, h8 as toRef, h9 as tryOnScopeDispose, ha as toValue, hb as isIOS, hc as noop, hd as isObject, he as isClient, bw as useNodeTypesStore, bx as useNodeHelpers, K as useDebounce, fI as nodeIssuesToString, B as normalizeStyle, fC as TitledList, br as getNodeInputs, hf as SWITCH_NODE_TYPE, fG as isEqual, hg as captureException, p as useSettingsStore, aw as usePostHog, bM as useCredentialsStore, u as useUsersStore, a4 as useProjectsStore, dB as OPEN_AI_API_CREDENTIAL_TYPE, hh as AI_CREDITS_EXPERIMENT, bP as useHistoryStore, ad as ProjectTypes, bs as getConnectionTypes, o as onMounted, hi as ndvEventBus, y as onBeforeUnmount, hj as _sfc_main$m, hk as CUSTOM_NODES_DOCS_URL, aC as withDirectives, aD as vShow, gO as _sfc_main$n, hl as NodeCredentials, bh as get, f9 as getNodeParameters, ca as deepCopy, hm as set, hn as unset, e_ as RenameNodeCommand, gN as isINodePropertyCollectionList, ho as isINodePropertiesList, hp as isINodePropertyOptionsList, hq as displayParameter, hr as Draggable, fs as useThrottleFn, L as useUIStore, fd as useSlots, hs as MAIN_NODE_PANEL_WIDTH, aL as useStorage, ht as LOCAL_STORAGE_MAIN_PANEL_RELATIVE_WIDTH, x as renderSlot, hu as requireVue, ck as getDefaultExportFromCjs, hv as sanitizeHtml, hw as sanitizeHtmlExports, bU as jsonParse, aS as N8nTooltip, aU as _sfc_main$o, aT as N8nLink, b9 as N8nText, a3 as useSourceControlStore, a1 as useRootStore, dy as useSchemaPreviewStore, W as useRoute, bI as toRef$1, bi as usePinnedData, hx as useNodeType, cG as storeToRefs, ep as HTML_NODE_TYPE, hy as TRIMMED_TASK_DATA_CONNECTIONS_KEY, hz as executionDataToJson, hA as NODE_TYPES_EXCLUDED_FROM_OUTPUT_NAME_APPEND, dU as CORE_NODES_CATEGORY, hB as SCHEMA_PREVIEW_EXPERIMENT, hC as computedAsync, hD as LOCAL_STORAGE_PIN_DATA_DISCOVERY_NDV_FLAG, hE as dataPinningEventBus, hF as LOCAL_STORAGE_PIN_DATA_DISCOVERY_CANVAS_FLAG, hG as searchInObject, hH as isObject$1, hI as MAX_DISPLAY_DATA_SIZE_SCHEMA_VIEW, dG as MAX_DISPLAY_DATA_SIZE, cb as resolveDirective, b_ as N8nCallout, hJ as DATA_PINNING_DOCS_URL, bY as Suspense, bZ as defineAsyncComponent, bG as N8nRadioButtons, bb as N8nButton, gv as _sfc_main$p, gu as N8nSelect, hK as N8nTabs, ga as _sfc_main$q, hL as JsonEditor, hM as DATA_EDITING_DOCS_URL, de as InfoTip, hN as N8nBlockUi, aq as __vitePreload, eY as isPresent, bt as getNodeOutputs, hO as getNodeHints, hP as getGenericHints, hQ as clearJsonKey, hR as TEST_PIN_DATA, fQ as isEmpty, fH as useNodeDirtiness, fD as CanvasNodeDirtiness, aW as createSlots, hS as waitingNodeTooltip, hT as truncate, gj as createStaticVNode, bS as START_NODE_TYPE, eH as MANUAL_TRIGGER_NODE_TYPE, hU as CRON_NODE_TYPE, hV as INTERVAL_NODE_TYPE, hW as uniqBy, b2 as resolveDynamicComponent, bE as N8nIcon, hX as isTriggerPanelObject, dw as getTriggerNodeServiceName, eC as WEBHOOK_NODE_TYPE, dh as CopyInput, C as createEventBus, fF as Transition, V as VIEWS, am as WORKFLOW_SETTINGS_MODAL_KEY, fu as useDeviceSupport, hY as EXECUTABLE_TRIGGER_NODE_TYPES, aA as STICKY_NODE_TYPE, hZ as BASE_NODE_SURVEY_URL, ac as EnterpriseEditionFeature, bH as useStyles, h_ as APP_MODALS_ELEMENT_ID, ak as useMessage, al as MODAL_CONFIRM } from "./index-CN4JmOoA.js";
import { i as importCurlEventBus } from "./import-curl-DVtpAPDa.js";
import { F as FileSaver_minExports } from "./FileSaver.min-DbOfV2ME.js";
import { V as ViewSubExecution, b as _sfc_main$r, R as RunDataAi } from "./RunDataAi-DsidSvp-.js";
import { b as convertToDisplayDateComponents } from "./dateFormatter-DP26ifk_.js";
import { u as useWorkflowActivate } from "./useWorkflowActivate-BZzKuzDM.js";
function responseHasSubworkflowData(response) {
  return ["executionId", "workflowId"].every(
    (x) => hasKey(response, x) && typeof response[x] === "string"
  );
}
function parseErrorResponseWorkflowMetadata(response) {
  if (!responseHasSubworkflowData(response)) return void 0;
  return {
    subExecution: {
      executionId: response.executionId,
      workflowId: response.workflowId
    },
    subExecutionsCount: 1
  };
}
function parseErrorMetadata(error) {
  if (hasKey(error, "errorResponse")) {
    return parseErrorResponseWorkflowMetadata(error.errorResponse);
  }
  return parseErrorResponseWorkflowMetadata(error);
}
const _sfc_main$k = /* @__PURE__ */ defineComponent({
  __name: "NodeTitle",
  props: {
    modelValue: { default: "" },
    nodeType: { default: void 0 },
    readOnly: { type: Boolean, default: false }
  },
  emits: ["update:model-value"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const editName = ref(false);
    const newName = ref("");
    const input = ref();
    const i18n = useI18n();
    const editable = computed(() => !props.readOnly && window === window.parent);
    async function onEdit() {
      newName.value = props.modelValue;
      editName.value = true;
      await nextTick();
      if (input.value) {
        input.value.focus();
      }
    }
    function onRename() {
      if (newName.value.trim() !== "") {
        emit("update:model-value", newName.value.trim());
      }
      editName.value = false;
    }
    return (_ctx, _cache) => {
      const _component_n8n_text = resolveComponent("n8n-text");
      const _component_n8n_input = resolveComponent("n8n-input");
      const _component_n8n_button = resolveComponent("n8n-button");
      const _component_font_awesome_icon = resolveComponent("font-awesome-icon");
      const _component_n8n_popover = resolveComponent("n8n-popover");
      return openBlock(), createElementBlock("span", {
        class: normalizeClass(_ctx.$style.container),
        "data-test-id": "node-title-container",
        onClick: onEdit
      }, [
        createBaseVNode("span", {
          class: normalizeClass(_ctx.$style.iconWrapper)
        }, [
          createVNode(_sfc_main$l, {
            "node-type": _ctx.nodeType,
            size: 18
          }, null, 8, ["node-type"])
        ], 2),
        createVNode(_component_n8n_popover, {
          placement: "right",
          width: "200",
          visible: editName.value,
          disabled: !editable.value
        }, {
          reference: withCtx(() => [
            createBaseVNode("div", {
              class: normalizeClass({ [_ctx.$style.title]: true, [_ctx.$style.hoverable]: editable.value })
            }, [
              createTextVNode(toDisplayString(_ctx.modelValue) + " ", 1),
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.editIconContainer)
              }, [
                editable.value ? (openBlock(), createBlock(_component_font_awesome_icon, {
                  key: 0,
                  class: normalizeClass(_ctx.$style.editIcon),
                  icon: "pencil-alt"
                }, null, 8, ["class"])) : createCommentVNode("", true)
              ], 2)
            ], 2)
          ]),
          default: withCtx(() => [
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.editContainer),
              onKeydown: [
                withKeys(onRename, ["enter"]),
                _cache[3] || (_cache[3] = withModifiers(() => {
                }, ["stop"])),
                _cache[4] || (_cache[4] = withKeys(($event) => editName.value = false, ["esc"]))
              ]
            }, [
              createVNode(_component_n8n_text, {
                bold: true,
                color: "text-base",
                tag: "div"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("ndv.title.renameNode")), 1)
                ]),
                _: 1
              }),
              createVNode(_component_n8n_input, {
                ref_key: "input",
                ref: input,
                modelValue: newName.value,
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => newName.value = $event),
                size: "small",
                "data-test-id": "node-rename-input"
              }, null, 8, ["modelValue"]),
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.editButtons)
              }, [
                createVNode(_component_n8n_button, {
                  type: "secondary",
                  size: "small",
                  label: unref(i18n).baseText("ndv.title.cancel"),
                  onClick: _cache[1] || (_cache[1] = ($event) => editName.value = false),
                  onKeydown: _cache[2] || (_cache[2] = withKeys(withModifiers(() => {
                  }, ["stop"]), ["enter"]))
                }, null, 8, ["label"]),
                createVNode(_component_n8n_button, {
                  type: "primary",
                  size: "small",
                  label: unref(i18n).baseText("ndv.title.rename"),
                  onClick: onRename
                }, null, 8, ["label"])
              ], 2)
            ], 34)
          ]),
          _: 1
        }, 8, ["visible", "disabled"])
      ], 2);
    };
  }
});
const container$3 = "_container_86rol_123";
const title$3 = "_title_86rol_133";
const hoverable = "_hoverable_86rol_141";
const editIcon = "_editIcon_86rol_144";
const iconWrapper = "_iconWrapper_86rol_148";
const editIconContainer = "_editIconContainer_86rol_161";
const editButtons = "_editButtons_86rol_167";
const editContainer = "_editContainer_86rol_175";
const style0$c = {
  container: container$3,
  title: title$3,
  hoverable,
  editIcon,
  iconWrapper,
  editIconContainer,
  editButtons,
  editContainer
};
const cssModules$d = {
  "$style": style0$c
};
const NodeTitle = /* @__PURE__ */ _export_sfc(_sfc_main$k, [["__cssModules", cssModules$d]]);
const _sfc_main$j = /* @__PURE__ */ defineComponent({
  __name: "NodeSettingsTabs",
  props: {
    modelValue: { default: "params" },
    nodeType: { default: void 0 },
    pushRef: { default: "" }
  },
  emits: ["update:model-value"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const externalHooks = useExternalHooks();
    const ndvStore = useNDVStore();
    const workflowsStore = useWorkflowsStore();
    const i18n = useI18n();
    const telemetry = useTelemetry();
    const activeNode = computed(() => ndvStore.activeNode);
    const isCommunityNode = computed(() => {
      const nodeType = props.nodeType;
      if (nodeType) {
        return isCommunityPackageName(nodeType.name);
      }
      return false;
    });
    const packageName = computed(() => props.nodeType?.name.split(".")[0] ?? "");
    const documentationUrl = computed(() => {
      const nodeType = props.nodeType;
      if (!nodeType) {
        return "";
      }
      if (nodeType.documentationUrl && nodeType.documentationUrl.startsWith("http")) {
        return nodeType.documentationUrl;
      }
      const utmParams = new URLSearchParams({
        utm_source: "n8n_app",
        utm_medium: "node_settings_modal-credential_link",
        utm_campaign: nodeType.name
      });
      const primaryDocUrl = nodeType.codex?.resources?.primaryDocumentation?.[0]?.url;
      if (primaryDocUrl) {
        return `${primaryDocUrl}?${utmParams.toString()}`;
      }
      if (isCommunityNode.value) {
        return `${NPM_PACKAGE_DOCS_BASE_URL}${packageName.value}`;
      }
      return `${BUILTIN_NODES_DOCS_URL}?${utmParams.toString()}`;
    });
    const options = computed(() => {
      const options2 = [
        {
          label: i18n.baseText("nodeSettings.parameters"),
          value: "params"
        },
        {
          label: i18n.baseText("nodeSettings.settings"),
          value: "settings"
        }
      ];
      if (isCommunityNode.value) {
        options2.push({
          icon: "cube",
          value: "communityNode",
          align: "right",
          tooltip: i18n.baseText("generic.communityNode.tooltip", {
            interpolate: {
              docUrl: COMMUNITY_NODES_INSTALLATION_DOCS_URL,
              packageName: packageName.value
            }
          })
        });
      }
      if (documentationUrl.value) {
        options2.push({
          label: i18n.baseText("nodeSettings.docs"),
          value: "docs",
          href: documentationUrl.value,
          align: "right"
        });
      }
      return options2;
    });
    function onTabSelect(tab) {
      if (tab === "docs" && props.nodeType) {
        void externalHooks.run("dataDisplay.onDocumentationUrlClick", {
          nodeType: props.nodeType,
          documentationUrl: documentationUrl.value
        });
        telemetry.track("User clicked ndv link", {
          node_type: activeNode.value?.type,
          workflow_id: workflowsStore.workflowId,
          push_ref: props.pushRef,
          pane: NodeConnectionTypes.Main,
          type: "docs"
        });
      }
      if (tab === "settings" && props.nodeType) {
        telemetry.track("User viewed node settings", {
          node_type: props.nodeType.name,
          workflow_id: workflowsStore.workflowId
        });
      }
      if (tab === "settings" || tab === "params") {
        emit("update:model-value", tab);
      }
    }
    function onTooltipClick(tab, event) {
      if (tab === "communityNode" && event.target.localName === "a") {
        telemetry.track("user clicked cnr docs link", { source: "node details view" });
      }
    }
    return (_ctx, _cache) => {
      const _component_N8nTabs = resolveComponent("N8nTabs");
      return openBlock(), createBlock(_component_N8nTabs, {
        options: options.value,
        "model-value": _ctx.modelValue,
        "onUpdate:modelValue": onTabSelect,
        onTooltipClick
      }, null, 8, ["options", "model-value"]);
    };
  }
});
const _hoisted_1$d = {
  key: 0,
  class: "webhooks"
};
const _hoisted_2$7 = ["title"];
const _hoisted_3$6 = {
  key: 0,
  class: "node-webhooks"
};
const _hoisted_4$6 = {
  key: 0,
  class: "url-selection"
};
const _hoisted_5$6 = {
  key: 0,
  class: "webhook-wrapper"
};
const _hoisted_6$5 = { class: "http-field" };
const _hoisted_7$4 = { class: "http-method" };
const _hoisted_8$4 = { class: "url-field" };
const _hoisted_9$3 = ["onClick"];
const _hoisted_10$2 = {
  key: 1,
  class: "webhook-wrapper"
};
const _hoisted_11$1 = { class: "url-field-full-width" };
const _hoisted_12 = ["onClick"];
const _sfc_main$i = /* @__PURE__ */ defineComponent({
  __name: "NodeWebhooks",
  props: {
    node: {},
    nodeTypeDescription: {}
  },
  setup(__props) {
    const props = __props;
    const router = useRouter();
    const clipboard = useClipboard();
    const workflowHelpers = useWorkflowHelpers({ router });
    const toast = useToast();
    const i18n = useI18n();
    const telemetry = useTelemetry();
    const isMinimized = ref(
      props.nodeTypeDescription && !OPEN_URL_PANEL_TRIGGER_NODE_TYPES.includes(props.nodeTypeDescription.name)
    );
    const showUrlFor = ref("test");
    const isProductionOnly = computed(() => {
      return props.nodeTypeDescription && PRODUCTION_ONLY_TRIGGER_NODE_TYPES.includes(props.nodeTypeDescription.name);
    });
    const urlOptions = computed(() => [
      ...isProductionOnly.value ? [] : [{ label: baseText.value.testUrl, value: "test" }],
      {
        label: baseText.value.productionUrl,
        value: "production"
      }
    ]);
    const visibleWebhookUrls = computed(() => {
      return webhooksNode.value.filter((webhook) => {
        if (typeof webhook.ndvHideUrl === "string") {
          return !workflowHelpers.getWebhookExpressionValue(webhook, "ndvHideUrl");
        }
        return !webhook.ndvHideUrl;
      });
    });
    const webhooksNode = computed(() => {
      if (props.nodeTypeDescription?.webhooks === void 0) {
        return [];
      }
      return props.nodeTypeDescription.webhooks.filter(
        (webhookData) => webhookData.restartWebhook !== true
      );
    });
    const baseText = computed(() => {
      const nodeType = props.nodeTypeDescription?.name;
      switch (nodeType) {
        case CHAT_TRIGGER_NODE_TYPE:
          return {
            toggleTitle: i18n.baseText("nodeWebhooks.webhookUrls.chatTrigger"),
            clickToDisplay: i18n.baseText("nodeWebhooks.clickToDisplayWebhookUrls.formTrigger"),
            clickToHide: i18n.baseText("nodeWebhooks.clickToHideWebhookUrls.chatTrigger"),
            clickToCopy: i18n.baseText("nodeWebhooks.clickToCopyWebhookUrls.chatTrigger"),
            testUrl: i18n.baseText("nodeWebhooks.testUrl"),
            productionUrl: i18n.baseText("nodeWebhooks.productionUrl"),
            copyTitle: i18n.baseText("nodeWebhooks.showMessage.title.chatTrigger"),
            copyMessage: i18n.baseText("nodeWebhooks.showMessage.message.chatTrigger")
          };
        case FORM_TRIGGER_NODE_TYPE:
          return {
            toggleTitle: i18n.baseText("nodeWebhooks.webhookUrls.formTrigger"),
            clickToDisplay: i18n.baseText("nodeWebhooks.clickToDisplayWebhookUrls.formTrigger"),
            clickToHide: i18n.baseText("nodeWebhooks.clickToHideWebhookUrls.formTrigger"),
            clickToCopy: i18n.baseText("nodeWebhooks.clickToCopyWebhookUrls.formTrigger"),
            testUrl: i18n.baseText("nodeWebhooks.testUrl"),
            productionUrl: i18n.baseText("nodeWebhooks.productionUrl"),
            copyTitle: i18n.baseText("nodeWebhooks.showMessage.title.formTrigger"),
            copyMessage: i18n.baseText("nodeWebhooks.showMessage.message.formTrigger")
          };
        case MCP_TRIGGER_NODE_TYPE:
          return {
            toggleTitle: i18n.baseText("nodeWebhooks.webhookUrls.mcpTrigger"),
            clickToDisplay: i18n.baseText("nodeWebhooks.clickToDisplayWebhookUrls.mcpTrigger"),
            clickToHide: i18n.baseText("nodeWebhooks.clickToHideWebhookUrls.mcpTrigger"),
            clickToCopy: i18n.baseText("nodeWebhooks.clickToCopyWebhookUrls.mcpTrigger"),
            testUrl: i18n.baseText("nodeWebhooks.testUrl"),
            productionUrl: i18n.baseText("nodeWebhooks.productionUrl"),
            copyTitle: i18n.baseText("nodeWebhooks.showMessage.title.mcpTrigger"),
            copyMessage: void 0
          };
        default:
          return {
            toggleTitle: i18n.baseText("nodeWebhooks.webhookUrls"),
            clickToDisplay: i18n.baseText("nodeWebhooks.clickToDisplayWebhookUrls"),
            clickToHide: i18n.baseText("nodeWebhooks.clickToHideWebhookUrls"),
            clickToCopy: i18n.baseText("nodeWebhooks.clickToCopyWebhookUrls"),
            testUrl: i18n.baseText("nodeWebhooks.testUrl"),
            productionUrl: i18n.baseText("nodeWebhooks.productionUrl"),
            copyTitle: i18n.baseText("nodeWebhooks.showMessage.title"),
            copyMessage: void 0
          };
      }
    });
    function copyWebhookUrl(webhookData) {
      const webhookUrl = getWebhookUrlDisplay(webhookData);
      void clipboard.copy(webhookUrl);
      toast.showMessage({
        title: baseText.value.copyTitle,
        message: baseText.value.copyMessage,
        type: "success"
      });
      telemetry.track("User copied webhook URL", {
        pane: "parameters",
        type: `${showUrlFor.value} url`
      });
    }
    function getWebhookUrlDisplay(webhookData) {
      if (props.node) {
        return workflowHelpers.getWebhookUrl(
          webhookData,
          props.node,
          isProductionOnly.value ? "production" : showUrlFor.value
        );
      }
      return "";
    }
    function isWebhookMethodVisible(webhook) {
      try {
        const method = workflowHelpers.getWebhookExpressionValue(webhook, "httpMethod", false);
        if (Array.isArray(method) && method.length !== 1) {
          return false;
        }
      } catch (error) {
      }
      if (typeof webhook.ndvHideMethod === "string") {
        return !workflowHelpers.getWebhookExpressionValue(webhook, "ndvHideMethod");
      }
      return !webhook.ndvHideMethod;
    }
    function getWebhookHttpMethod(webhook) {
      const method = workflowHelpers.getWebhookExpressionValue(webhook, "httpMethod", false);
      if (Array.isArray(method)) {
        return method[0];
      }
      return method;
    }
    watch(
      () => props.node,
      () => {
        isMinimized.value = props.nodeTypeDescription && !OPEN_URL_PANEL_TRIGGER_NODE_TYPES.includes(props.nodeTypeDescription.name);
      }
    );
    return (_ctx, _cache) => {
      const _component_font_awesome_icon = resolveComponent("font-awesome-icon");
      const _component_n8n_radio_buttons = resolveComponent("n8n-radio-buttons");
      const _component_el_col = resolveComponent("el-col");
      const _component_el_row = resolveComponent("el-row");
      const _component_n8n_tooltip = resolveComponent("n8n-tooltip");
      const _component_el_collapse_transition = resolveComponent("el-collapse-transition");
      return webhooksNode.value.length && visibleWebhookUrls.value.length > 0 ? (openBlock(), createElementBlock("div", _hoisted_1$d, [
        createBaseVNode("div", {
          class: normalizeClass(["clickable headline", { expanded: !isMinimized.value }]),
          title: isMinimized.value ? baseText.value.clickToDisplay : baseText.value.clickToHide,
          onClick: _cache[0] || (_cache[0] = ($event) => isMinimized.value = !isMinimized.value)
        }, [
          createVNode(_component_font_awesome_icon, {
            icon: "angle-right",
            class: "minimize-button minimize-icon"
          }),
          createTextVNode(" " + toDisplayString(baseText.value.toggleTitle), 1)
        ], 10, _hoisted_2$7),
        createVNode(_component_el_collapse_transition, null, {
          default: withCtx(() => [
            !isMinimized.value ? (openBlock(), createElementBlock("div", _hoisted_3$6, [
              !isProductionOnly.value ? (openBlock(), createElementBlock("div", _hoisted_4$6, [
                createVNode(_component_el_row, null, {
                  default: withCtx(() => [
                    createVNode(_component_el_col, { span: 24 }, {
                      default: withCtx(() => [
                        createVNode(_component_n8n_radio_buttons, {
                          modelValue: showUrlFor.value,
                          "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => showUrlFor.value = $event),
                          options: urlOptions.value
                        }, null, 8, ["modelValue", "options"])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                })
              ])) : createCommentVNode("", true),
              (openBlock(true), createElementBlock(Fragment, null, renderList(visibleWebhookUrls.value, (webhook, index) => {
                return openBlock(), createBlock(_component_n8n_tooltip, {
                  key: index,
                  class: "item",
                  content: baseText.value.clickToCopy,
                  placement: "left"
                }, {
                  default: withCtx(() => [
                    isWebhookMethodVisible(webhook) ? (openBlock(), createElementBlock("div", _hoisted_5$6, [
                      createBaseVNode("div", _hoisted_6$5, [
                        createBaseVNode("div", _hoisted_7$4, [
                          createTextVNode(toDisplayString(getWebhookHttpMethod(webhook)), 1),
                          _cache[2] || (_cache[2] = createBaseVNode("br", null, null, -1))
                        ])
                      ]),
                      createBaseVNode("div", _hoisted_8$4, [
                        createBaseVNode("div", {
                          class: "webhook-url left-ellipsis clickable",
                          onClick: ($event) => copyWebhookUrl(webhook)
                        }, [
                          createTextVNode(toDisplayString(getWebhookUrlDisplay(webhook)), 1),
                          _cache[3] || (_cache[3] = createBaseVNode("br", null, null, -1))
                        ], 8, _hoisted_9$3)
                      ])
                    ])) : (openBlock(), createElementBlock("div", _hoisted_10$2, [
                      createBaseVNode("div", _hoisted_11$1, [
                        createBaseVNode("div", {
                          class: "webhook-url left-ellipsis clickable",
                          onClick: ($event) => copyWebhookUrl(webhook)
                        }, [
                          createTextVNode(toDisplayString(getWebhookUrlDisplay(webhook)), 1),
                          _cache[4] || (_cache[4] = createBaseVNode("br", null, null, -1))
                        ], 8, _hoisted_12)
                      ])
                    ]))
                  ]),
                  _: 2
                }, 1032, ["content"]);
              }), 128))
            ])) : createCommentVNode("", true)
          ]),
          _: 1
        })
      ])) : createCommentVNode("", true);
    };
  }
});
const NodeWebhooks = /* @__PURE__ */ _export_sfc(_sfc_main$i, [["__scopeId", "data-v-56323fca"]]);
const OnClickOutside = /* @__PURE__ */ defineComponent({
  name: "OnClickOutside",
  props: ["as", "options"],
  emits: ["trigger"],
  setup(props, { slots, emit }) {
    const target = ref();
    onClickOutside(target, (e) => {
      emit("trigger", e);
    }, props.options);
    return () => {
      if (slots.default)
        return h(props.as || "div", { ref: target }, slots.default());
    };
  }
});
function unrefElement(elRef) {
  var _a;
  const plain = toValue(elRef);
  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;
}
const defaultWindow = isClient ? window : void 0;
function useEventListener(...args) {
  let target;
  let events;
  let listeners;
  let options;
  if (typeof args[0] === "string" || Array.isArray(args[0])) {
    [events, listeners, options] = args;
    target = defaultWindow;
  } else {
    [target, events, listeners, options] = args;
  }
  if (!target)
    return noop;
  if (!Array.isArray(events))
    events = [events];
  if (!Array.isArray(listeners))
    listeners = [listeners];
  const cleanups = [];
  const cleanup = () => {
    cleanups.forEach((fn) => fn());
    cleanups.length = 0;
  };
  const register = (el, event, listener, options2) => {
    el.addEventListener(event, listener, options2);
    return () => el.removeEventListener(event, listener, options2);
  };
  const stopWatch = watch(
    () => [unrefElement(target), toValue(options)],
    ([el, options2]) => {
      cleanup();
      if (!el)
        return;
      const optionsClone = isObject(options2) ? { ...options2 } : options2;
      cleanups.push(
        ...events.flatMap((event) => {
          return listeners.map((listener) => register(el, event, listener, optionsClone));
        })
      );
    },
    { immediate: true, flush: "post" }
  );
  const stop = () => {
    stopWatch();
    cleanup();
  };
  tryOnScopeDispose(stop);
  return stop;
}
function resolveElement(el) {
  if (typeof Window !== "undefined" && el instanceof Window)
    return el.document.documentElement;
  if (typeof Document !== "undefined" && el instanceof Document)
    return el.documentElement;
  return el;
}
function checkOverflowScroll(ele) {
  const style = window.getComputedStyle(ele);
  if (style.overflowX === "scroll" || style.overflowY === "scroll" || style.overflowX === "auto" && ele.clientWidth < ele.scrollWidth || style.overflowY === "auto" && ele.clientHeight < ele.scrollHeight) {
    return true;
  } else {
    const parent = ele.parentNode;
    if (!parent || parent.tagName === "BODY")
      return false;
    return checkOverflowScroll(parent);
  }
}
function preventDefault(rawEvent) {
  const e = rawEvent || window.event;
  const _target = e.target;
  if (checkOverflowScroll(_target))
    return false;
  if (e.touches.length > 1)
    return true;
  if (e.preventDefault)
    e.preventDefault();
  return false;
}
const elInitialOverflow = /* @__PURE__ */ new WeakMap();
function useScrollLock(element, initialState = false) {
  const isLocked = ref(initialState);
  let stopTouchMoveListener = null;
  let initialOverflow = "";
  watch(toRef(element), (el) => {
    const target = resolveElement(toValue(el));
    if (target) {
      const ele = target;
      if (!elInitialOverflow.get(ele))
        elInitialOverflow.set(ele, ele.style.overflow);
      if (ele.style.overflow !== "hidden")
        initialOverflow = ele.style.overflow;
      if (ele.style.overflow === "hidden")
        return isLocked.value = true;
      if (isLocked.value)
        return ele.style.overflow = "hidden";
    }
  }, {
    immediate: true
  });
  const lock = () => {
    const el = resolveElement(toValue(element));
    if (!el || isLocked.value)
      return;
    if (isIOS) {
      stopTouchMoveListener = useEventListener(
        el,
        "touchmove",
        (e) => {
          preventDefault(e);
        },
        { passive: false }
      );
    }
    el.style.overflow = "hidden";
    isLocked.value = true;
  };
  const unlock = () => {
    const el = resolveElement(toValue(element));
    if (!el || !isLocked.value)
      return;
    isIOS && (stopTouchMoveListener == null ? void 0 : stopTouchMoveListener());
    el.style.overflow = initialOverflow;
    elInitialOverflow.delete(el);
    isLocked.value = false;
  };
  tryOnScopeDispose(unlock);
  return computed({
    get() {
      return isLocked.value;
    },
    set(v) {
      if (v)
        lock();
      else unlock();
    }
  });
}
function onScrollLock() {
  let isMounted = false;
  const state = ref(false);
  return (el, binding) => {
    state.value = binding.value;
    if (isMounted)
      return;
    isMounted = true;
    const isLocked = useScrollLock(el, binding.value);
    watch(state, (v) => isLocked.value = v);
  };
}
onScrollLock();
const _hoisted_1$c = ["data-test-id"];
const _hoisted_2$6 = ["textContent"];
const _hoisted_3$5 = ["onClick"];
const _hoisted_4$5 = ["onClick"];
const _hoisted_5$5 = ["data-node-name"];
const _hoisted_6$4 = ["onClick"];
const _sfc_main$h = /* @__PURE__ */ defineComponent({
  __name: "NDVSubConnections",
  props: {
    rootNode: {}
  },
  emits: ["switchSelectedNode", "openConnectionNodeCreator"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const workflowsStore = useWorkflowsStore();
    const nodeTypesStore = useNodeTypesStore();
    const nodeHelpers = useNodeHelpers();
    const i18n = useI18n();
    const { debounce } = useDebounce();
    const emit = __emit;
    const possibleConnections = ref([]);
    const expandedGroups = ref([]);
    const shouldShowNodeInputIssues = ref(false);
    const nodeType = computed(
      () => nodeTypesStore.getNodeType(props.rootNode.type, props.rootNode.typeVersion)
    );
    const nodeData = computed(() => workflowsStore.getNodeByName(props.rootNode.name));
    const workflow = computed(() => workflowsStore.getCurrentWorkflow());
    const nodeInputIssues = computed(() => {
      const issues = nodeHelpers.getNodeIssues(nodeType.value, props.rootNode, workflow.value, [
        "typeUnknown",
        "parameters",
        "credentials",
        "execution"
      ]);
      return issues?.input ?? {};
    });
    const connectedNodes2 = computed(() => {
      return possibleConnections.value.reduce(
        (acc, connection) => {
          const nodes = getINodesFromNames(
            workflow.value.getParentNodes(props.rootNode.name, connection.type)
          );
          return { ...acc, [connection.type]: nodes };
        },
        {}
      );
    });
    function getConnectionConfig(connectionType2) {
      return possibleConnections.value.find((c) => c.type === connectionType2);
    }
    function isMultiConnection(connectionType2) {
      const connectionConfig = getConnectionConfig(connectionType2);
      return connectionConfig?.maxConnections !== 1;
    }
    function shouldShowConnectionTooltip(connectionType2) {
      return isMultiConnection(connectionType2) && !expandedGroups.value.includes(connectionType2);
    }
    function expandConnectionGroup(connectionType2, isExpanded) {
      if (!isMultiConnection(connectionType2)) {
        return;
      }
      if (isExpanded) {
        expandedGroups.value = [...expandedGroups.value, connectionType2];
      } else {
        expandedGroups.value = expandedGroups.value.filter((g) => g !== connectionType2);
      }
    }
    function getINodesFromNames(names) {
      return names.map((name) => {
        const node2 = workflowsStore.getNodeByName(name);
        if (node2) {
          const matchedNodeType = nodeTypesStore.getNodeType(node2.type);
          if (matchedNodeType) {
            const issues = nodeHelpers.getNodeIssues(matchedNodeType, node2, workflow.value);
            const stringifiedIssues = issues ? nodeIssuesToString(issues, node2) : "";
            return { node: node2, nodeType: matchedNodeType, issues: stringifiedIssues };
          }
        }
        return null;
      }).filter((n) => n !== null);
    }
    function hasInputIssues(connectionType2) {
      return shouldShowNodeInputIssues.value && (nodeInputIssues.value[connectionType2] ?? []).length > 0;
    }
    function isNodeInputConfiguration(connectionConfig) {
      if (typeof connectionConfig === "string") return false;
      return "type" in connectionConfig;
    }
    function getPossibleSubInputConnections() {
      if (!nodeType.value || !props.rootNode) return [];
      const inputs = getNodeInputs(workflow.value, props.rootNode, nodeType.value);
      const nonMainInputs = inputs.filter((input) => {
        if (!isNodeInputConfiguration(input)) return false;
        return input.type !== "main";
      });
      return nonMainInputs;
    }
    function onNodeClick(nodeName, connectionType2) {
      if (isMultiConnection(connectionType2) && !expandedGroups.value.includes(connectionType2)) {
        expandConnectionGroup(connectionType2, true);
        return;
      }
      emit("switchSelectedNode", nodeName);
    }
    function onPlusClick(connectionType2) {
      const connectionNodes = connectedNodes2.value[connectionType2];
      if (isMultiConnection(connectionType2) && !expandedGroups.value.includes(connectionType2) && connectionNodes.length >= 1) {
        expandConnectionGroup(connectionType2, true);
        return;
      }
      emit("openConnectionNodeCreator", props.rootNode.name, connectionType2);
    }
    function showNodeInputsIssues() {
      shouldShowNodeInputIssues.value = false;
      setTimeout(() => {
        shouldShowNodeInputIssues.value = true;
      }, 0);
    }
    watch(
      nodeData,
      debounce(
        () => setTimeout(() => {
          expandedGroups.value = [];
          possibleConnections.value = getPossibleSubInputConnections();
        }, 0),
        { debounceTime: 1e3 }
      ),
      { immediate: true }
    );
    __expose({
      showNodeInputsIssues
    });
    return (_ctx, _cache) => {
      const _component_n8n_icon_button = resolveComponent("n8n-icon-button");
      const _component_n8n_tooltip = resolveComponent("n8n-tooltip");
      return possibleConnections.value.length ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: normalizeClass(_ctx.$style.container)
      }, [
        createBaseVNode("div", {
          class: normalizeClass(_ctx.$style.connections),
          style: normalizeStyle(`--possible-connections: ${possibleConnections.value.length}`)
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(possibleConnections.value, (connection) => {
            return openBlock(), createElementBlock("div", {
              key: connection.type,
              "data-test-id": `subnode-connection-group-${connection.type}`
            }, [
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.connectionType)
              }, [
                createBaseVNode("span", {
                  class: normalizeClass({
                    [_ctx.$style.connectionLabel]: true,
                    [_ctx.$style.hasIssues]: hasInputIssues(connection.type)
                  }),
                  textContent: toDisplayString(`${connection.displayName}${connection.required ? " *" : ""}`)
                }, null, 10, _hoisted_2$6),
                createVNode(unref(OnClickOutside), {
                  onTrigger: ($event) => expandConnectionGroup(connection.type, false)
                }, {
                  default: withCtx(() => [
                    createBaseVNode("div", {
                      ref_for: true,
                      ref: "connectedNodesWrapper",
                      class: normalizeClass({
                        [_ctx.$style.connectedNodesWrapper]: true,
                        [_ctx.$style.connectedNodesWrapperExpanded]: expandedGroups.value.includes(connection.type)
                      }),
                      style: normalizeStyle(`--nodes-length: ${connectedNodes2.value[connection.type].length}`),
                      onClick: ($event) => expandConnectionGroup(connection.type, true)
                    }, [
                      (connectedNodes2.value[connection.type].length >= 1 ? connection.maxConnections !== 1 : true) ? (openBlock(), createElementBlock("div", {
                        key: 0,
                        class: normalizeClass({
                          [_ctx.$style.plusButton]: true,
                          [_ctx.$style.hasIssues]: hasInputIssues(connection.type)
                        }),
                        onClick: ($event) => onPlusClick(connection.type)
                      }, [
                        createVNode(_component_n8n_tooltip, {
                          placement: "top",
                          teleported: true,
                          offset: 10,
                          "show-after": 300,
                          disabled: shouldShowConnectionTooltip(connection.type) && connectedNodes2.value[connection.type].length >= 1
                        }, {
                          content: withCtx(() => [
                            createTextVNode(" Add " + toDisplayString(connection.displayName) + " ", 1),
                            hasInputIssues(connection.type) ? (openBlock(), createBlock(TitledList, {
                              key: 0,
                              title: `${unref(i18n).baseText("node.issues")}:`,
                              items: nodeInputIssues.value[connection.type]
                            }, null, 8, ["title", "items"])) : createCommentVNode("", true)
                          ]),
                          default: withCtx(() => [
                            createVNode(_component_n8n_icon_button, {
                              size: "medium",
                              icon: "plus",
                              type: "tertiary",
                              "data-test-id": `add-subnode-${connection.type}`
                            }, null, 8, ["data-test-id"])
                          ]),
                          _: 2
                        }, 1032, ["disabled"])
                      ], 10, _hoisted_4$5)) : createCommentVNode("", true),
                      connectedNodes2.value[connection.type].length > 0 ? (openBlock(), createElementBlock("div", {
                        key: 1,
                        class: normalizeClass({
                          [_ctx.$style.connectedNodes]: true,
                          [_ctx.$style.connectedNodesMultiple]: connectedNodes2.value[connection.type].length > 1
                        })
                      }, [
                        (openBlock(true), createElementBlock(Fragment, null, renderList(connectedNodes2.value[connection.type], (node2, index) => {
                          return openBlock(), createElementBlock("div", {
                            key: node2.node.name,
                            class: normalizeClass({ [_ctx.$style.nodeWrapper]: true, [_ctx.$style.hasIssues]: node2.issues }),
                            "data-test-id": "floating-subnode",
                            "data-node-name": node2.node.name,
                            style: normalizeStyle(`--node-index: ${index}`)
                          }, [
                            (openBlock(), createBlock(_component_n8n_tooltip, {
                              key: node2.node.name,
                              placement: "top",
                              teleported: true,
                              offset: 10,
                              "show-after": 300,
                              disabled: shouldShowConnectionTooltip(connection.type)
                            }, {
                              content: withCtx(() => [
                                createTextVNode(toDisplayString(node2.node.name) + " ", 1),
                                node2.issues ? (openBlock(), createBlock(TitledList, {
                                  key: 0,
                                  title: `${unref(i18n).baseText("node.issues")}:`,
                                  items: node2.issues
                                }, null, 8, ["title", "items"])) : createCommentVNode("", true)
                              ]),
                              default: withCtx(() => [
                                createBaseVNode("div", {
                                  class: normalizeClass(_ctx.$style.connectedNode),
                                  onClick: ($event) => onNodeClick(node2.node.name, connection.type)
                                }, [
                                  createVNode(_sfc_main$l, {
                                    "node-type": node2.nodeType,
                                    "node-name": node2.node.name,
                                    "tooltip-position": "top",
                                    size: 20,
                                    circle: ""
                                  }, null, 8, ["node-type", "node-name"])
                                ], 10, _hoisted_6$4)
                              ]),
                              _: 2
                            }, 1032, ["disabled"]))
                          ], 14, _hoisted_5$5);
                        }), 128))
                      ], 2)) : createCommentVNode("", true)
                    ], 14, _hoisted_3$5)
                  ]),
                  _: 2
                }, 1032, ["onTrigger"])
              ], 2)
            ], 8, _hoisted_1$c);
          }), 128))
        ], 6)
      ], 2)) : createCommentVNode("", true);
    };
  }
});
const container$2 = "_container_ac466_140";
const connections = "_connections_ac466_148";
const connectionType = "_connectionType_ac466_160";
const connectionLabel = "_connectionLabel_ac466_167";
const hasIssues = "_hasIssues_ac466_173";
const connectedNodesWrapper = "_connectedNodesWrapper_ac466_177";
const plusButton = "_plusButton_ac466_185";
const connectedNodesWrapperExpanded = "_connectedNodesWrapperExpanded_ac466_203";
const connectedNodesMultiple = "_connectedNodesMultiple_ac466_209";
const connectedNode$1 = "_connectedNode_ac466_177";
const connectedNodes = "_connectedNodes_ac466_177";
const nodeWrapper = "_nodeWrapper_ac466_247";
const style0$b = {
  container: container$2,
  connections,
  connectionType,
  connectionLabel,
  hasIssues,
  connectedNodesWrapper,
  plusButton,
  "horizontal-shake": "_horizontal-shake_ac466_1",
  connectedNodesWrapperExpanded,
  connectedNodesMultiple,
  connectedNode: connectedNode$1,
  connectedNodes,
  nodeWrapper
};
const cssModules$c = {
  "$style": style0$b
};
const NDVSubConnections = /* @__PURE__ */ _export_sfc(_sfc_main$h, [["__cssModules", cssModules$c]]);
function updateDynamicConnections(node2, workflowConnections, parameterData) {
  const connections2 = { ...workflowConnections };
  try {
    if (parameterData.name.includes("conditions") || !connections2[node2.name]?.main) return null;
    if (node2.type === SWITCH_NODE_TYPE && parameterData.name === "parameters.numberOutputs") {
      const curentNumberOutputs = node2.parameters?.numberOutputs;
      const newNumberOutputs = parameterData.value;
      if (newNumberOutputs < curentNumberOutputs) {
        connections2[node2.name].main = connections2[node2.name].main.slice(0, newNumberOutputs);
        return connections2;
      }
    }
    if (node2.type === SWITCH_NODE_TYPE && parameterData.name === "parameters.options.fallbackOutput") {
      const curentFallbackOutput = node2.parameters?.options?.fallbackOutput;
      if (curentFallbackOutput === "extra") {
        if (!parameterData.value || parameterData.value !== "extra") {
          connections2[node2.name].main = connections2[node2.name].main.slice(0, -1);
          return connections2;
        }
      }
    }
    if (node2.type === SWITCH_NODE_TYPE && parameterData.name.includes("parameters.rules.values")) {
      const { fallbackOutput } = node2.parameters?.options;
      if (parameterData.value === void 0) {
        let extractIndex = function(path) {
          const match = path.match(/parameters\.rules\.values\[(\d+)\]$/);
          return match ? parseInt(match[1], 10) : null;
        };
        const index = extractIndex(parameterData.name);
        if (index !== null) {
          connections2[node2.name].main.splice(index, 1);
          return connections2;
        }
        if (parameterData.name === "parameters.rules.values") {
          if (fallbackOutput === "extra") {
            connections2[node2.name].main = [
              connections2[node2.name].main[connections2[node2.name].main.length - 1]
            ];
          } else {
            connections2[node2.name].main = [];
          }
          return connections2;
        }
      } else if (parameterData.name === "parameters.rules.values") {
        const curentRulesvalues = node2.parameters?.rules?.values;
        let lastConnection = void 0;
        if (fallbackOutput === "extra" && connections2[node2.name].main.length === curentRulesvalues.length + 1) {
          lastConnection = connections2[node2.name].main.pop();
        }
        const currentRulesLength = node2.parameters?.rules?.values?.length;
        const newRulesLength = parameterData.value?.length;
        if (newRulesLength - currentRulesLength === 1) {
          connections2[node2.name].main = [...connections2[node2.name].main, []];
          if (lastConnection) {
            connections2[node2.name].main.push(lastConnection);
          }
          return connections2;
        } else {
          const newRulesvalues = parameterData.value;
          const updatedConnectionsIndex = [];
          for (const newRule of newRulesvalues) {
            const index = curentRulesvalues.findIndex((rule) => isEqual(rule, newRule));
            if (index !== -1) {
              updatedConnectionsIndex.push(index);
            }
          }
          const reorderedConnections = [];
          for (const index of updatedConnectionsIndex) {
            reorderedConnections.push(connections2[node2.name].main[index] ?? []);
          }
          if (lastConnection) {
            reorderedConnections.push(lastConnection);
          }
          connections2[node2.name].main = reorderedConnections;
          return connections2;
        }
      }
    }
  } catch (error) {
    captureException(error);
  }
  return null;
}
const _hoisted_1$b = { class: "mt-xs" };
const LANGCHAIN_NODES_PREFIX = "@n8n/n8n-nodes-langchain.";
const N8N_NODES_PREFIX = "@n8n/n8n-nodes.";
const _sfc_main$g = /* @__PURE__ */ defineComponent({
  __name: "FreeAiCreditsCallout",
  setup(__props) {
    const NODES_WITH_OPEN_AI_API_CREDENTIAL = [
      `${LANGCHAIN_NODES_PREFIX}openAi`,
      `${LANGCHAIN_NODES_PREFIX}embeddingsOpenAi`,
      `${LANGCHAIN_NODES_PREFIX}lmChatOpenAi`,
      `${N8N_NODES_PREFIX}openAi`
    ];
    const showSuccessCallout = ref(false);
    const claimingCredits = ref(false);
    const settingsStore = useSettingsStore();
    const posthogStore = usePostHog();
    const credentialsStore = useCredentialsStore();
    const usersStore = useUsersStore();
    const ndvStore = useNDVStore();
    const projectsStore = useProjectsStore();
    const telemetry = useTelemetry();
    const i18n = useI18n();
    const toast = useToast();
    const userHasOpenAiCredentialAlready = computed(
      () => !!credentialsStore.allCredentials.filter(
        (credential) => credential.type === OPEN_AI_API_CREDENTIAL_TYPE
      ).length
    );
    const userHasClaimedAiCreditsAlready = computed(
      () => !!usersStore.currentUser?.settings?.userClaimedAiCredits
    );
    const activeNodeHasOpenAiApiCredential = computed(
      () => ndvStore.activeNode?.type && NODES_WITH_OPEN_AI_API_CREDENTIAL.includes(ndvStore.activeNode.type)
    );
    const userCanClaimOpenAiCredits = computed(() => {
      return settingsStore.isAiCreditsEnabled && activeNodeHasOpenAiApiCredential.value && posthogStore.getVariant(AI_CREDITS_EXPERIMENT.name) === AI_CREDITS_EXPERIMENT.variant && !userHasOpenAiCredentialAlready.value && !userHasClaimedAiCreditsAlready.value;
    });
    const onClaimCreditsClicked = async () => {
      claimingCredits.value = true;
      try {
        await credentialsStore.claimFreeAiCredits(projectsStore.currentProject?.id);
        if (usersStore?.currentUser?.settings) {
          usersStore.currentUser.settings.userClaimedAiCredits = true;
        }
        telemetry.track("User claimed OpenAI credits");
        showSuccessCallout.value = true;
      } catch (e) {
        toast.showError(
          e,
          i18n.baseText("freeAi.credits.showError.claim.title"),
          i18n.baseText("freeAi.credits.showError.claim.message")
        );
      } finally {
        claimingCredits.value = false;
      }
    };
    return (_ctx, _cache) => {
      const _component_n8n_button = resolveComponent("n8n-button");
      const _component_n8n_callout = resolveComponent("n8n-callout");
      const _component_n8n_text = resolveComponent("n8n-text");
      return openBlock(), createElementBlock("div", _hoisted_1$b, [
        userCanClaimOpenAiCredits.value && !showSuccessCallout.value ? (openBlock(), createBlock(_component_n8n_callout, {
          key: 0,
          theme: "secondary",
          icon: "exclamation-circle"
        }, {
          trailingContent: withCtx(() => [
            createVNode(_component_n8n_button, {
              type: "tertiary",
              size: "small",
              label: unref(i18n).baseText("freeAi.credits.callout.claim.button.label"),
              loading: claimingCredits.value,
              onClick: onClaimCreditsClicked
            }, null, 8, ["label", "loading"])
          ]),
          default: withCtx(() => [
            createTextVNode(toDisplayString(unref(i18n).baseText("freeAi.credits.callout.claim.title", {
              interpolate: { credits: unref(settingsStore).aiCreditsQuota }
            })) + " ", 1)
          ]),
          _: 1
        })) : showSuccessCallout.value ? (openBlock(), createBlock(_component_n8n_callout, {
          key: 1,
          theme: "success",
          icon: "check-circle"
        }, {
          default: withCtx(() => [
            createVNode(_component_n8n_text, { size: "small" }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("freeAi.credits.callout.success.title.part1", {
                  interpolate: { credits: unref(settingsStore).aiCreditsQuota }
                })), 1)
              ]),
              _: 1
            }),
            _cache[0] || (_cache[0] = createTextVNode("  ")),
            createVNode(_component_n8n_text, {
              size: "small",
              bold: "true"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("freeAi.credits.callout.success.title.part2")), 1)
              ]),
              _: 1
            })
          ]),
          _: 1
        })) : createCommentVNode("", true)
      ]);
    };
  }
});
const _hoisted_1$a = { class: "header-side-menu" };
const _hoisted_2$5 = { key: 1 };
const _hoisted_3$4 = {
  key: 0,
  class: "node-is-not-valid"
};
const _hoisted_4$4 = { class: "missingNodeTitleContainer mt-s mb-xs" };
const _hoisted_5$4 = { class: "mb-l" };
const _hoisted_6$3 = ["href"];
const _hoisted_7$3 = ["href", "textContent"];
const _hoisted_8$3 = {
  key: 1,
  class: "node-parameters-wrapper",
  "data-test-id": "node-parameters"
};
const _hoisted_9$2 = {
  key: 1,
  class: "no-parameters"
};
const _hoisted_10$1 = {
  key: 2,
  class: "parameter-item parameter-notice",
  "data-test-id": "node-parameters-http-notice"
};
const _hoisted_11 = {
  class: "node-version",
  "data-test-id": "node-version"
};
const _sfc_main$f = /* @__PURE__ */ defineComponent({
  __name: "NodeSettings",
  props: {
    eventBus: {},
    dragging: { type: Boolean },
    pushRef: {},
    nodeType: {},
    readOnly: { type: Boolean, default: false },
    foreignCredentials: { default: () => [] },
    blockUI: { type: Boolean, default: false },
    executable: { type: Boolean, default: true },
    inputSize: { default: 0 }
  },
  emits: ["stopExecution", "redrawRequired", "valueChanged", "switchSelectedNode", "openConnectionNodeCreator", "activate", "execute"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const nodeTypesStore = useNodeTypesStore();
    const ndvStore = useNDVStore();
    const workflowsStore = useWorkflowsStore();
    const credentialsStore = useCredentialsStore();
    const historyStore = useHistoryStore();
    const telemetry = useTelemetry();
    const nodeHelpers = useNodeHelpers();
    const externalHooks = useExternalHooks();
    const i18n = useI18n();
    const nodeValid = ref(true);
    const openPanel = ref("params");
    const nodeValues = ref({
      color: "#ff0000",
      alwaysOutputData: false,
      executeOnce: false,
      notesInFlow: false,
      onError: "stopWorkflow",
      retryOnFail: false,
      maxTries: 3,
      waitBetweenTries: 1e3,
      notes: "",
      parameters: {}
    });
    const nodeValuesInitialized = ref(false);
    const hiddenIssuesInputs = ref([]);
    const nodeSettings = ref([]);
    const subConnections = ref(null);
    const currentWorkflowInstance = computed(() => workflowsStore.getCurrentWorkflow());
    const currentWorkflow = computed(
      () => workflowsStore.getWorkflowById(currentWorkflowInstance.value.id)
    );
    const hasForeignCredential = computed(() => props.foreignCredentials.length > 0);
    const isHomeProjectTeam = computed(
      () => currentWorkflow.value?.homeProject?.type === ProjectTypes.Team
    );
    const isReadOnly = computed(
      () => props.readOnly || hasForeignCredential.value && !isHomeProjectTeam.value
    );
    const node2 = computed(() => ndvStore.activeNode);
    const isTriggerNode = computed(() => !!node2.value && nodeTypesStore.isTriggerNode(node2.value.type));
    const isExecutable = computed(() => {
      if (props.nodeType && node2.value) {
        const workflowNode = currentWorkflowInstance.value.getNode(node2.value.name);
        const inputs = getNodeInputs(
          currentWorkflowInstance.value,
          workflowNode,
          props.nodeType
        );
        const inputNames = getConnectionTypes(inputs);
        if (!inputNames.includes(NodeConnectionTypes.Main) && !isTriggerNode.value) {
          return false;
        }
      }
      return props.executable || props.foreignCredentials.length > 0;
    });
    const nodeTypeVersions = computed(() => {
      if (!node2.value) return [];
      return nodeTypesStore.getNodeVersions(node2.value.type);
    });
    const latestVersion = computed(() => Math.max(...nodeTypeVersions.value));
    const isLatestNodeVersion = computed(
      () => !node2.value?.typeVersion || latestVersion.value === node2.value.typeVersion
    );
    const executeButtonTooltip = computed(() => {
      if (node2.value && isLatestNodeVersion.value && props.inputSize > 1 && !nodeHelpers.isSingleExecution(node2.value.type, node2.value.parameters)) {
        return i18n.baseText("nodeSettings.executeButtonTooltip.times", {
          interpolate: { inputSize: props.inputSize }
        });
      }
      return "";
    });
    const nodeVersionTag = computed(() => {
      if (!props.nodeType || props.nodeType.hidden) {
        return i18n.baseText("nodeSettings.deprecated");
      }
      if (isLatestNodeVersion.value) {
        return i18n.baseText("nodeSettings.latest");
      }
      return i18n.baseText("nodeSettings.latestVersion", {
        interpolate: { version: latestVersion.value.toString() }
      });
    });
    const parameters = computed(() => {
      if (props.nodeType === null) {
        return [];
      }
      return props.nodeType?.properties ?? [];
    });
    const parametersSetting = computed(() => parameters.value.filter((item) => item.isNodeSetting));
    const parametersNoneSetting = computed(
      () => parameters.value.filter((item) => !item.isNodeSetting)
    );
    const outputPanelEditMode = computed(() => ndvStore.outputPanelEditMode);
    const isCommunityNode = computed(() => !!node2.value && isCommunityPackageName(node2.value.type));
    const usedCredentials = computed(
      () => Object.values(workflowsStore.usedCredentials).filter(
        (credential) => Object.values(node2.value?.credentials || []).find(
          (nodeCredential) => nodeCredential.id === credential.id
        )
      )
    );
    const credentialOwnerName = computed(() => {
      const credential = usedCredentials.value ? Object.values(usedCredentials.value).find(
        (credential2) => credential2.id === props.foreignCredentials[0]
      ) : void 0;
      return credentialsStore.getCredentialOwnerName(credential);
    });
    const setValue = (name, value) => {
      const nameParts = name.split(".");
      let lastNamePart = nameParts.pop();
      let isArray = false;
      if (lastNamePart !== void 0 && lastNamePart.includes("[")) {
        const lastNameParts = lastNamePart.match(/(.*)\[(\d+)\]$/);
        if (lastNameParts) {
          nameParts.push(lastNameParts[1]);
          lastNamePart = lastNameParts[2];
          isArray = true;
        }
      }
      if (nameParts.length === 0) {
        if (value === null) {
          if (lastNamePart) {
            const { [lastNamePart]: removedNodeValue, ...remainingNodeValues } = nodeValues.value;
            nodeValues.value = remainingNodeValues;
          }
        } else {
          nodeValues.value = {
            ...nodeValues.value,
            [lastNamePart]: value
          };
        }
      } else {
        if (value === null) {
          let tempValue = get(nodeValues.value, nameParts.join("."));
          if (lastNamePart && !Array.isArray(tempValue)) {
            const { [lastNamePart]: removedNodeValue, ...remainingNodeValues } = tempValue;
            tempValue = remainingNodeValues;
          }
          if (isArray && Array.isArray(tempValue) && tempValue.length === 0) {
            lastNamePart = nameParts.pop();
            tempValue = get(nodeValues.value, nameParts.join("."));
            if (lastNamePart) {
              const { [lastNamePart]: removedArrayNodeValue, ...remainingArrayNodeValues } = tempValue;
              tempValue = remainingArrayNodeValues;
            }
          }
        } else {
          if (typeof value === "object") {
            set(
              get(nodeValues.value, nameParts.join(".")),
              lastNamePart,
              deepCopy(value)
            );
          } else {
            set(
              get(nodeValues.value, nameParts.join(".")),
              lastNamePart,
              value
            );
          }
        }
      }
      nodeValues.value = { ...nodeValues.value };
    };
    const removeMismatchedOptionValues = (nodeType, nodeParameterValues, updatedParameter) => {
      nodeType.properties.forEach((prop) => {
        const displayOptions = prop.displayOptions;
        if (!nodeParameterValues?.hasOwnProperty(prop.name) || !displayOptions || !prop.options) {
          return;
        }
        const showCondition = displayOptions.show?.[updatedParameter.name];
        const hideCondition = displayOptions.hide?.[updatedParameter.name];
        if (showCondition === void 0 && hideCondition === void 0) {
          return;
        }
        let hasValidOptions = true;
        if (isINodePropertyCollectionList(prop.options) || isINodePropertiesList(prop.options)) {
          hasValidOptions = Object.keys(nodeParameterValues).every(
            (key) => (prop.options ?? []).find((option) => option.name === key) !== void 0
          );
        } else if (isINodePropertyOptionsList(prop.options)) {
          hasValidOptions = !!prop.options.find(
            (option) => option.value === nodeParameterValues[prop.name]
          );
        }
        if (!hasValidOptions && displayParameter(nodeParameterValues, prop, node2.value, nodeType)) {
          unset(nodeParameterValues, prop.name);
        }
      });
    };
    const valueChanged = (parameterData) => {
      let newValue;
      if (parameterData.hasOwnProperty("value")) {
        newValue = parameterData.value;
      } else {
        newValue = get(nodeValues.value, parameterData.name);
      }
      const nodeNameBefore = parameterData.node || node2.value?.name;
      if (!nodeNameBefore) {
        return;
      }
      const _node = workflowsStore.getNodeByName(nodeNameBefore);
      if (_node === null) {
        return;
      }
      if (parameterData.name === "onError") {
        emit("redrawRequired");
      }
      if (parameterData.name === "name") {
        const sendData = {
          value: newValue,
          oldValue: nodeNameBefore,
          name: parameterData.name
        };
        emit("valueChanged", sendData);
      } else if (parameterData.name === "parameters") {
        const nodeType = nodeTypesStore.getNodeType(_node.type, _node.typeVersion);
        if (!nodeType) {
          return;
        }
        let nodeParameters = getNodeParameters(
          nodeType.properties,
          _node.parameters,
          false,
          false,
          _node,
          nodeType
        );
        const oldNodeParameters = Object.assign({}, nodeParameters);
        nodeParameters = deepCopy(nodeParameters);
        if (parameterData.value && typeof parameterData.value === "object") {
          for (const parameterName of Object.keys(parameterData.value)) {
            newValue = parameterData.value[parameterName];
            const parameterPath = parameterName.split(".").slice(1).join(".");
            const parameterPathArray = parameterPath.match(/(.*)\[(\d+)\]$/);
            if (parameterData[parameterName] === void 0 && parameterPathArray !== null) {
              const path = parameterPathArray[1];
              const index = parameterPathArray[2];
              const data = get(nodeParameters, path);
              if (Array.isArray(data)) {
                data.splice(parseInt(index, 10), 1);
                set(nodeParameters, path, data);
              }
            } else {
              if (newValue === void 0) {
                unset(nodeParameters, parameterPath);
              } else {
                set(nodeParameters, parameterPath, newValue);
              }
            }
            void externalHooks.run("nodeSettings.valueChanged", {
              parameterPath,
              newValue,
              parameters: parameters.value,
              oldNodeParameters
            });
          }
        }
        nodeParameters = getNodeParameters(
          nodeType.properties,
          nodeParameters,
          true,
          false,
          _node,
          nodeType
        );
        for (const key of Object.keys(nodeParameters)) {
          if (nodeParameters && nodeParameters[key] !== null && nodeParameters[key] !== void 0) {
            setValue(`parameters.${key}`, nodeParameters[key]);
          }
        }
        if (nodeParameters) {
          const updateInformation = {
            name: _node.name,
            value: nodeParameters
          };
          workflowsStore.setNodeParameters(updateInformation);
          nodeHelpers.updateNodeParameterIssuesByName(_node.name);
          nodeHelpers.updateNodeCredentialIssuesByName(_node.name);
        }
      } else if (parameterData.name.startsWith("parameters.")) {
        const nodeType = nodeTypesStore.getNodeType(_node.type, _node.typeVersion);
        if (!nodeType) {
          return;
        }
        let nodeParameters = getNodeParameters(
          nodeType.properties,
          _node.parameters,
          false,
          false,
          _node,
          nodeType
        );
        const oldNodeParameters = Object.assign({}, nodeParameters);
        nodeParameters = deepCopy(nodeParameters);
        const parameterPath = parameterData.name.split(".").slice(1).join(".");
        const parameterPathArray = parameterPath.match(/(.*)\[(\d+)\]$/);
        if (parameterData.value === void 0 && parameterPathArray !== null) {
          const path = parameterPathArray[1];
          const index = parameterPathArray[2];
          const data = get(nodeParameters, path);
          if (Array.isArray(data)) {
            data.splice(parseInt(index, 10), 1);
            set(nodeParameters, path, data);
          }
        } else {
          if (newValue === void 0) {
            unset(nodeParameters, parameterPath);
          } else {
            set(nodeParameters, parameterPath, newValue);
          }
          removeMismatchedOptionValues(nodeType, nodeParameters, {
            name: parameterPath
          });
        }
        nodeParameters = getNodeParameters(
          nodeType.properties,
          nodeParameters,
          true,
          false,
          _node,
          nodeType
        );
        for (const key of Object.keys(nodeParameters)) {
          if (nodeParameters && nodeParameters[key] !== null && nodeParameters[key] !== void 0) {
            setValue(`parameters.${key}`, nodeParameters[key]);
          }
        }
        const updateInformation = {
          name: _node.name,
          value: nodeParameters
        };
        const connections2 = workflowsStore.allConnections;
        const updatedConnections = updateDynamicConnections(_node, connections2, parameterData);
        if (updatedConnections) {
          workflowsStore.setConnections(updatedConnections, true);
        }
        workflowsStore.setNodeParameters(updateInformation);
        void externalHooks.run("nodeSettings.valueChanged", {
          parameterPath,
          newValue,
          parameters: parameters.value,
          oldNodeParameters
        });
        nodeHelpers.updateNodeParameterIssuesByName(_node.name);
        nodeHelpers.updateNodeCredentialIssuesByName(_node.name);
        telemetry.trackNodeParametersValuesChange(nodeType.name, parameterData);
      } else {
        nodeValues.value = {
          ...nodeValues.value,
          [parameterData.name]: newValue
        };
        const updateInformation = {
          name: _node.name,
          key: parameterData.name,
          value: newValue
        };
        workflowsStore.setNodeValue(updateInformation);
      }
    };
    const setHttpNodeParameters = (parameters2) => {
      try {
        valueChanged({
          node: node2.value?.name,
          name: "parameters",
          value: parameters2
        });
      } catch {
      }
    };
    const onSwitchSelectedNode = (node22) => {
      emit("switchSelectedNode", node22);
    };
    const onOpenConnectionNodeCreator = (nodeName, connectionType2) => {
      emit("openConnectionNodeCreator", nodeName, connectionType2);
    };
    const populateHiddenIssuesSet = () => {
      if (!node2.value || !workflowsStore.isNodePristine(node2.value.name)) return;
      hiddenIssuesInputs.value.push("credentials");
      parametersNoneSetting.value.forEach((parameter) => {
        hiddenIssuesInputs.value.push(parameter.name);
      });
      workflowsStore.setNodePristine(node2.value.name, false);
    };
    const populateSettings = () => {
      if (isExecutable.value && !isTriggerNode.value) {
        nodeSettings.value.push(
          ...[
            {
              displayName: i18n.baseText("nodeSettings.alwaysOutputData.displayName"),
              name: "alwaysOutputData",
              type: "boolean",
              default: false,
              noDataExpression: true,
              description: i18n.baseText("nodeSettings.alwaysOutputData.description")
            },
            {
              displayName: i18n.baseText("nodeSettings.executeOnce.displayName"),
              name: "executeOnce",
              type: "boolean",
              default: false,
              noDataExpression: true,
              description: i18n.baseText("nodeSettings.executeOnce.description")
            },
            {
              displayName: i18n.baseText("nodeSettings.retryOnFail.displayName"),
              name: "retryOnFail",
              type: "boolean",
              default: false,
              noDataExpression: true,
              description: i18n.baseText("nodeSettings.retryOnFail.description")
            },
            {
              displayName: i18n.baseText("nodeSettings.maxTries.displayName"),
              name: "maxTries",
              type: "number",
              typeOptions: {
                minValue: 2,
                maxValue: 5
              },
              default: 3,
              displayOptions: {
                show: {
                  retryOnFail: [true]
                }
              },
              noDataExpression: true,
              description: i18n.baseText("nodeSettings.maxTries.description")
            },
            {
              displayName: i18n.baseText("nodeSettings.waitBetweenTries.displayName"),
              name: "waitBetweenTries",
              type: "number",
              typeOptions: {
                minValue: 0,
                maxValue: 5e3
              },
              default: 1e3,
              displayOptions: {
                show: {
                  retryOnFail: [true]
                }
              },
              noDataExpression: true,
              description: i18n.baseText("nodeSettings.waitBetweenTries.description")
            },
            {
              displayName: i18n.baseText("nodeSettings.onError.displayName"),
              name: "onError",
              type: "options",
              options: [
                {
                  name: i18n.baseText("nodeSettings.onError.options.stopWorkflow.displayName"),
                  value: "stopWorkflow",
                  description: i18n.baseText("nodeSettings.onError.options.stopWorkflow.description")
                },
                {
                  name: i18n.baseText("nodeSettings.onError.options.continueRegularOutput.displayName"),
                  value: "continueRegularOutput",
                  description: i18n.baseText(
                    "nodeSettings.onError.options.continueRegularOutput.description"
                  )
                },
                {
                  name: i18n.baseText("nodeSettings.onError.options.continueErrorOutput.displayName"),
                  value: "continueErrorOutput",
                  description: i18n.baseText(
                    "nodeSettings.onError.options.continueErrorOutput.description"
                  )
                }
              ],
              default: "stopWorkflow",
              description: i18n.baseText("nodeSettings.onError.description"),
              noDataExpression: true
            }
          ]
        );
      }
      nodeSettings.value.push(
        ...[
          {
            displayName: i18n.baseText("nodeSettings.notes.displayName"),
            name: "notes",
            type: "string",
            typeOptions: {
              rows: 5
            },
            default: "",
            noDataExpression: true,
            description: i18n.baseText("nodeSettings.notes.description")
          },
          {
            displayName: i18n.baseText("nodeSettings.notesInFlow.displayName"),
            name: "notesInFlow",
            type: "boolean",
            default: false,
            noDataExpression: true,
            description: i18n.baseText("nodeSettings.notesInFlow.description")
          }
        ]
      );
    };
    const onParameterBlur = (parameterName) => {
      hiddenIssuesInputs.value = hiddenIssuesInputs.value.filter((name) => name !== parameterName);
    };
    const onWorkflowActivate = () => {
      hiddenIssuesInputs.value = [];
      emit("activate");
    };
    const onNodeExecute = () => {
      hiddenIssuesInputs.value = [];
      subConnections.value?.showNodeInputsIssues();
      emit("execute");
    };
    const credentialSelected = (updateInformation) => {
      workflowsStore.updateNodeProperties(updateInformation);
      const node22 = workflowsStore.getNodeByName(updateInformation.name);
      if (node22) {
        nodeHelpers.updateNodeCredentialIssues(node22);
      }
      void externalHooks.run("nodeSettings.credentialSelected", { updateInformation });
    };
    const nameChanged = (name) => {
      if (node2.value) {
        historyStore.pushCommandToUndo(new RenameNodeCommand(node2.value.name, name, Date.now()));
      }
      valueChanged({
        value: name,
        name: "name"
      });
    };
    const setNodeValues = () => {
      if (!node2.value) {
        nodeValuesInitialized.value = true;
        return;
      }
      if (props.nodeType !== null) {
        nodeValid.value = true;
        const foundNodeSettings = [];
        if (node2.value.color) {
          foundNodeSettings.push("color");
          nodeValues.value = {
            ...nodeValues.value,
            color: node2.value.color
          };
        }
        if (node2.value.notes) {
          foundNodeSettings.push("notes");
          nodeValues.value = {
            ...nodeValues.value,
            notes: node2.value.notes
          };
        }
        if (node2.value.alwaysOutputData) {
          foundNodeSettings.push("alwaysOutputData");
          nodeValues.value = {
            ...nodeValues.value,
            alwaysOutputData: node2.value.alwaysOutputData
          };
        }
        if (node2.value.executeOnce) {
          foundNodeSettings.push("executeOnce");
          nodeValues.value = {
            ...nodeValues.value,
            executeOnce: node2.value.executeOnce
          };
        }
        if (node2.value.continueOnFail) {
          foundNodeSettings.push("onError");
          nodeValues.value = {
            ...nodeValues.value,
            onError: "continueRegularOutput"
          };
        }
        if (node2.value.onError) {
          foundNodeSettings.push("onError");
          nodeValues.value = {
            ...nodeValues.value,
            onError: node2.value.onError
          };
        }
        if (node2.value.notesInFlow) {
          foundNodeSettings.push("notesInFlow");
          nodeValues.value = {
            ...nodeValues.value,
            notesInFlow: node2.value.notesInFlow
          };
        }
        if (node2.value.retryOnFail) {
          foundNodeSettings.push("retryOnFail");
          nodeValues.value = {
            ...nodeValues.value,
            retryOnFail: node2.value.retryOnFail
          };
        }
        if (node2.value.maxTries) {
          foundNodeSettings.push("maxTries");
          nodeValues.value = {
            ...nodeValues.value,
            maxTries: node2.value.maxTries
          };
        }
        if (node2.value.waitBetweenTries) {
          foundNodeSettings.push("waitBetweenTries");
          nodeValues.value = {
            ...nodeValues.value,
            waitBetweenTries: node2.value.waitBetweenTries
          };
        }
        for (const nodeSetting of nodeSettings.value) {
          if (!foundNodeSettings.includes(nodeSetting.name)) {
            nodeValues.value = {
              ...nodeValues.value,
              [nodeSetting.name]: nodeSetting.default
            };
          }
        }
        nodeValues.value = {
          ...nodeValues.value,
          parameters: deepCopy(node2.value.parameters)
        };
      } else {
        nodeValid.value = false;
      }
      nodeValuesInitialized.value = true;
    };
    const onMissingNodeTextClick = (event) => {
      if (event.target.localName === "a") {
        telemetry.track("user clicked cnr browse button", {
          source: "cnr missing node modal"
        });
      }
    };
    const onMissingNodeLearnMoreLinkClick = () => {
      telemetry.track("user clicked cnr docs link", {
        source: "missing node modal source",
        package_name: node2.value?.type.split(".")[0],
        node_type: node2.value?.type
      });
    };
    const onStopExecution = () => {
      emit("stopExecution");
    };
    const openSettings = () => {
      openPanel.value = "settings";
    };
    const onTabSelect = (tab) => {
      openPanel.value = tab;
    };
    watch(node2, () => {
      setNodeValues();
    });
    onMounted(() => {
      populateHiddenIssuesSet();
      populateSettings();
      setNodeValues();
      props.eventBus?.on("openSettings", openSettings);
      nodeHelpers.updateNodeParameterIssues(node2.value, props.nodeType);
      importCurlEventBus.on("setHttpNodeParameters", setHttpNodeParameters);
      ndvEventBus.on("updateParameterValue", valueChanged);
    });
    onBeforeUnmount(() => {
      props.eventBus?.off("openSettings", openSettings);
      importCurlEventBus.off("setHttpNodeParameters", setHttpNodeParameters);
      ndvEventBus.off("updateParameterValue", valueChanged);
    });
    return (_ctx, _cache) => {
      const _component_font_awesome_icon = resolveComponent("font-awesome-icon");
      const _component_n8n_text = resolveComponent("n8n-text");
      const _component_i18n_t = resolveComponent("i18n-t");
      const _component_n8n_link = resolveComponent("n8n-link");
      const _component_n8n_notice = resolveComponent("n8n-notice");
      const _component_n8n_block_ui = resolveComponent("n8n-block-ui");
      return openBlock(), createElementBlock("div", {
        class: normalizeClass({
          "node-settings": true,
          dragging: _ctx.dragging
        }),
        onKeydown: _cache[0] || (_cache[0] = withModifiers(() => {
        }, ["stop"]))
      }, [
        createBaseVNode("div", {
          class: normalizeClass(_ctx.$style.header)
        }, [
          createBaseVNode("div", _hoisted_1$a, [
            node2.value ? (openBlock(), createBlock(NodeTitle, {
              key: 0,
              class: "node-name",
              "model-value": node2.value.name,
              "node-type": _ctx.nodeType,
              "read-only": isReadOnly.value,
              "onUpdate:modelValue": nameChanged
            }, null, 8, ["model-value", "node-type", "read-only"])) : createCommentVNode("", true),
            isExecutable.value ? (openBlock(), createElementBlock("div", _hoisted_2$5, [
              !_ctx.blockUI && node2.value && nodeValid.value ? (openBlock(), createBlock(_sfc_main$m, {
                key: 0,
                "data-test-id": "node-execute-button",
                "node-name": node2.value.name,
                disabled: outputPanelEditMode.value.enabled && !isTriggerNode.value,
                tooltip: executeButtonTooltip.value,
                size: "small",
                "telemetry-source": "parameters",
                onExecute: onNodeExecute,
                onStopExecution,
                onValueChanged: valueChanged
              }, null, 8, ["node-name", "disabled", "tooltip"])) : createCommentVNode("", true)
            ])) : createCommentVNode("", true)
          ]),
          node2.value && nodeValid.value ? (openBlock(), createBlock(_sfc_main$j, {
            key: 0,
            "model-value": openPanel.value,
            "node-type": _ctx.nodeType,
            "push-ref": _ctx.pushRef,
            "onUpdate:modelValue": onTabSelect
          }, null, 8, ["model-value", "node-type", "push-ref"])) : createCommentVNode("", true)
        ], 2),
        node2.value && !nodeValid.value ? (openBlock(), createElementBlock("div", _hoisted_3$4, [
          createBaseVNode("p", {
            class: normalizeClass(_ctx.$style.warningIcon)
          }, [
            createVNode(_component_font_awesome_icon, { icon: "exclamation-triangle" })
          ], 2),
          createBaseVNode("div", _hoisted_4$4, [
            createVNode(_component_n8n_text, {
              size: "large",
              color: "text-dark",
              bold: ""
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("nodeSettings.communityNodeUnknown.title")), 1)
              ]),
              _: 1
            })
          ]),
          isCommunityNode.value ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: normalizeClass(_ctx.$style.descriptionContainer)
          }, [
            createBaseVNode("div", _hoisted_5$4, [
              createVNode(_component_i18n_t, {
                keypath: "nodeSettings.communityNodeUnknown.description",
                tag: "span",
                onClick: onMissingNodeTextClick
              }, {
                action: withCtx(() => [
                  createBaseVNode("a", {
                    href: `https://www.npmjs.com/package/${node2.value.type.split(".")[0]}`,
                    target: "_blank"
                  }, toDisplayString(node2.value.type.split(".")[0]), 9, _hoisted_6$3)
                ]),
                _: 1
              })
            ]),
            createVNode(_component_n8n_link, {
              to: unref(COMMUNITY_NODES_INSTALLATION_DOCS_URL),
              onClick: onMissingNodeLearnMoreLinkClick
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("nodeSettings.communityNodeUnknown.installLink.text")), 1)
              ]),
              _: 1
            }, 8, ["to"])
          ], 2)) : (openBlock(), createBlock(_component_i18n_t, {
            key: 1,
            keypath: "nodeSettings.nodeTypeUnknown.description",
            tag: "span"
          }, {
            action: withCtx(() => [
              createBaseVNode("a", {
                href: unref(CUSTOM_NODES_DOCS_URL),
                target: "_blank",
                textContent: toDisplayString(unref(i18n).baseText("nodeSettings.nodeTypeUnknown.description.customNode"))
              }, null, 8, _hoisted_7$3)
            ]),
            _: 1
          }))
        ])) : createCommentVNode("", true),
        node2.value && nodeValid.value ? (openBlock(), createElementBlock("div", _hoisted_8$3, [
          hasForeignCredential.value && !isHomeProjectTeam.value ? (openBlock(), createBlock(_component_n8n_notice, {
            key: 0,
            content: unref(i18n).baseText("nodeSettings.hasForeignCredential", {
              interpolate: { owner: credentialOwnerName.value }
            })
          }, null, 8, ["content"])) : createCommentVNode("", true),
          createVNode(_sfc_main$g),
          withDirectives(createBaseVNode("div", null, [
            createVNode(NodeWebhooks, {
              node: node2.value,
              "node-type-description": _ctx.nodeType
            }, null, 8, ["node", "node-type-description"]),
            nodeValuesInitialized.value ? (openBlock(), createBlock(_sfc_main$n, {
              key: 0,
              parameters: parametersNoneSetting.value,
              "hide-delete": true,
              "node-values": nodeValues.value,
              "is-read-only": isReadOnly.value,
              "hidden-issues-inputs": hiddenIssuesInputs.value,
              path: "parameters",
              onValueChanged: valueChanged,
              onActivate: onWorkflowActivate,
              onParameterBlur
            }, {
              default: withCtx(() => [
                createVNode(NodeCredentials, {
                  node: node2.value,
                  readonly: isReadOnly.value,
                  "show-all": true,
                  "hide-issues": hiddenIssuesInputs.value.includes("credentials"),
                  onCredentialSelected: credentialSelected,
                  onValueChanged: valueChanged,
                  onBlur: onParameterBlur
                }, null, 8, ["node", "readonly", "hide-issues"])
              ]),
              _: 1
            }, 8, ["parameters", "node-values", "is-read-only", "hidden-issues-inputs"])) : createCommentVNode("", true),
            parametersNoneSetting.value.length === 0 ? (openBlock(), createElementBlock("div", _hoisted_9$2, [
              createVNode(_component_n8n_text, null, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("nodeSettings.thisNodeDoesNotHaveAnyParameters")), 1)
                ]),
                _: 1
              })
            ])) : createCommentVNode("", true),
            unref(nodeHelpers).isCustomApiCallSelected(nodeValues.value) ? (openBlock(), createElementBlock("div", _hoisted_10$1, [
              createVNode(_component_n8n_notice, {
                content: unref(i18n).baseText("nodeSettings.useTheHttpRequestNode", {
                  interpolate: { nodeTypeDisplayName: _ctx.nodeType?.displayName ?? "" }
                })
              }, null, 8, ["content"])
            ])) : createCommentVNode("", true)
          ], 512), [
            [vShow, openPanel.value === "params"]
          ]),
          withDirectives(createBaseVNode("div", null, [
            createVNode(_sfc_main$n, {
              parameters: parametersSetting.value,
              "node-values": nodeValues.value,
              "is-read-only": isReadOnly.value,
              "hide-delete": true,
              "hidden-issues-inputs": hiddenIssuesInputs.value,
              path: "parameters",
              onValueChanged: valueChanged,
              onParameterBlur
            }, null, 8, ["parameters", "node-values", "is-read-only", "hidden-issues-inputs"]),
            createVNode(_sfc_main$n, {
              parameters: nodeSettings.value,
              "hide-delete": true,
              "node-values": nodeValues.value,
              "is-read-only": isReadOnly.value,
              "hidden-issues-inputs": hiddenIssuesInputs.value,
              path: "",
              onValueChanged: valueChanged,
              onParameterBlur
            }, null, 8, ["parameters", "node-values", "is-read-only", "hidden-issues-inputs"]),
            createBaseVNode("div", _hoisted_11, [
              createTextVNode(toDisplayString(unref(i18n).baseText("nodeSettings.nodeVersion", {
                interpolate: {
                  node: _ctx.nodeType?.displayName,
                  version: (node2.value.typeVersion ?? latestVersion.value).toString()
                }
              })) + " ", 1),
              createBaseVNode("span", null, "(" + toDisplayString(nodeVersionTag.value) + ")", 1)
            ])
          ], 512), [
            [vShow, openPanel.value === "settings"]
          ])
        ])) : createCommentVNode("", true),
        node2.value ? (openBlock(), createBlock(NDVSubConnections, {
          key: 2,
          ref_key: "subConnections",
          ref: subConnections,
          "root-node": node2.value,
          onSwitchSelectedNode,
          onOpenConnectionNodeCreator
        }, null, 8, ["root-node"])) : createCommentVNode("", true),
        createVNode(_component_n8n_block_ui, { show: _ctx.blockUI }, null, 8, ["show"])
      ], 34);
    };
  }
});
const header$2 = "_header_13al3_123";
const warningIcon = "_warningIcon_13al3_127";
const descriptionContainer = "_descriptionContainer_13al3_132";
const style0$a = {
  header: header$2,
  warningIcon,
  descriptionContainer
};
const cssModules$b = {
  "$style": style0$a
};
const NodeSettings = /* @__PURE__ */ _export_sfc(_sfc_main$f, [["__cssModules", cssModules$b], ["__scopeId", "data-v-18fbbe4a"]]);
const _sfc_main$e = /* @__PURE__ */ defineComponent({
  __name: "PanelDragButton",
  props: {
    canMoveRight: { type: Boolean },
    canMoveLeft: { type: Boolean }
  },
  emits: ["drag", "dragstart", "dragend"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const onDrag = (e) => {
      emit("drag", e);
    };
    const onDragEnd = () => {
      emit("dragend");
    };
    const onDragStart = () => {
      emit("dragstart");
    };
    return (_ctx, _cache) => {
      const _component_font_awesome_icon = resolveComponent("font-awesome-icon");
      return openBlock(), createBlock(Draggable, {
        type: "panel-resize",
        class: normalizeClass(_ctx.$style.dragContainer),
        onDrag,
        onDragstart: onDragStart,
        onDragend: onDragEnd
      }, {
        default: withCtx(({ isDragging }) => [
          createBaseVNode("div", {
            class: normalizeClass({ [_ctx.$style.dragButton]: true }),
            "data-test-id": "panel-drag-button"
          }, [
            _ctx.canMoveLeft ? (openBlock(), createElementBlock("span", {
              key: 0,
              class: normalizeClass({ [_ctx.$style.leftArrow]: true, [_ctx.$style.visible]: isDragging })
            }, [
              createVNode(_component_font_awesome_icon, { icon: "arrow-left" })
            ], 2)) : createCommentVNode("", true),
            _ctx.canMoveRight ? (openBlock(), createElementBlock("span", {
              key: 1,
              class: normalizeClass({ [_ctx.$style.rightArrow]: true, [_ctx.$style.visible]: isDragging })
            }, [
              createVNode(_component_font_awesome_icon, { icon: "arrow-right" })
            ], 2)) : createCommentVNode("", true),
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.grid)
            }, _cache[0] || (_cache[0] = [
              createBaseVNode("div", null, [
                createBaseVNode("div"),
                createBaseVNode("div"),
                createBaseVNode("div"),
                createBaseVNode("div"),
                createBaseVNode("div")
              ], -1),
              createBaseVNode("div", null, [
                createBaseVNode("div"),
                createBaseVNode("div"),
                createBaseVNode("div"),
                createBaseVNode("div"),
                createBaseVNode("div")
              ], -1)
            ]), 2)
          ], 2)
        ]),
        _: 1
      }, 8, ["class"]);
    };
  }
});
const dragContainer = "_dragContainer_16elv_123";
const dragButton = "_dragButton_16elv_127";
const leftArrow = "_leftArrow_16elv_141 _arrow_16elv_150";
const rightArrow = "_rightArrow_16elv_142 _arrow_16elv_150";
const visible$1 = "_visible_16elv_146";
const arrow = "_arrow_16elv_150";
const grid = "_grid_16elv_168";
const style0$9 = {
  dragContainer,
  dragButton,
  leftArrow,
  rightArrow,
  visible: visible$1,
  arrow,
  grid
};
const cssModules$a = {
  "$style": style0$9
};
const PanelDragButton = /* @__PURE__ */ _export_sfc(_sfc_main$e, [["__cssModules", cssModules$a]]);
const _hoisted_1$9 = ["data-node-name", "data-node-placement", "onClick"];
const _sfc_main$d = /* @__PURE__ */ defineComponent({
  __name: "NDVFloatingNodes",
  props: {
    rootNode: {}
  },
  emits: ["switchSelectedNode"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const workflowsStore = useWorkflowsStore();
    const nodeTypesStore = useNodeTypesStore();
    const workflow = workflowsStore.getCurrentWorkflow();
    const emit = __emit;
    function moveNodeDirection(direction) {
      const matchedDirectionNode = connectedNodes2.value[direction][0];
      if (matchedDirectionNode) {
        emit("switchSelectedNode", matchedDirectionNode.node.name);
      }
    }
    function onKeyDown(e) {
      if (e.shiftKey && e.altKey && (e.ctrlKey || e.metaKey)) {
        const mapper = {
          ArrowUp: "outputSub",
          ArrowRight: "outputMain",
          ArrowLeft: "inputMain"
          /* left */
        };
        const matchingDirection = mapper[e.key] || null;
        if (matchingDirection) {
          moveNodeDirection(matchingDirection);
        }
      }
    }
    function getINodesFromNames(names) {
      return names.map((name) => {
        const node2 = workflowsStore.getNodeByName(name);
        if (node2) {
          const nodeType = nodeTypesStore.getNodeType(node2.type);
          if (nodeType) {
            return { node: node2, nodeType };
          }
        }
        return null;
      }).filter((n) => n !== null);
    }
    const connectedNodes2 = computed(() => {
      const rootName = props.rootNode.name;
      return {
        [
          "outputSub"
          /* top */
        ]: getINodesFromNames(
          workflow.getChildNodes(rootName, "ALL_NON_MAIN")
        ),
        [
          "outputMain"
          /* right */
        ]: getINodesFromNames(
          workflow.getChildNodes(rootName, NodeConnectionTypes.Main, 1)
        ).reverse(),
        [
          "inputMain"
          /* left */
        ]: getINodesFromNames(
          workflow.getParentNodes(rootName, NodeConnectionTypes.Main, 1)
        ).reverse()
      };
    });
    const connectionGroups = [
      "outputSub",
      "outputMain",
      "inputMain"
      /* left */
    ];
    const tooltipPositionMapper = {
      [
        "outputSub"
        /* top */
      ]: "bottom",
      [
        "outputMain"
        /* right */
      ]: "left",
      [
        "inputMain"
        /* left */
      ]: "right"
    };
    onMounted(() => {
      document.addEventListener("keydown", onKeyDown, true);
    });
    onBeforeUnmount(() => {
      document.removeEventListener("keydown", onKeyDown, true);
    });
    __expose({
      moveNodeDirection
    });
    return (_ctx, _cache) => {
      const _component_n8n_tooltip = resolveComponent("n8n-tooltip");
      return openBlock(), createElementBlock("aside", {
        class: normalizeClass(_ctx.$style.floatingNodes)
      }, [
        (openBlock(), createElementBlock(Fragment, null, renderList(connectionGroups, (connectionGroup) => {
          return createBaseVNode("ul", {
            key: connectionGroup,
            class: normalizeClass([_ctx.$style.nodesList, _ctx.$style[connectionGroup]])
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(connectedNodes2.value[connectionGroup], ({ node: node2, nodeType }) => {
              return openBlock(), createElementBlock(Fragment, null, [
                node2 && nodeType ? (openBlock(), createBlock(_component_n8n_tooltip, {
                  key: node2.name,
                  placement: tooltipPositionMapper[connectionGroup],
                  teleported: false,
                  offset: 60
                }, {
                  content: withCtx(() => [
                    createTextVNode(toDisplayString(node2.name), 1)
                  ]),
                  default: withCtx(() => [
                    createBaseVNode("li", {
                      class: normalizeClass(_ctx.$style.connectedNode),
                      "data-test-id": "floating-node",
                      "data-node-name": node2.name,
                      "data-node-placement": connectionGroup,
                      onClick: ($event) => emit("switchSelectedNode", node2.name)
                    }, [
                      createVNode(_sfc_main$l, {
                        "node-type": nodeType,
                        "node-name": node2.name,
                        "tooltip-position": tooltipPositionMapper[connectionGroup],
                        size: 35,
                        circle: ""
                      }, null, 8, ["node-type", "node-name", "tooltip-position"])
                    ], 10, _hoisted_1$9)
                  ]),
                  _: 2
                }, 1032, ["placement"])) : createCommentVNode("", true)
              ], 64);
            }), 256))
          ], 2);
        }), 64))
      ], 2);
    };
  }
});
const floatingNodes = "_floatingNodes_1tkq5_123";
const nodesList = "_nodesList_1tkq5_137";
const inputSub = "_inputSub_1tkq5_151";
const outputSub = "_outputSub_1tkq5_151";
const outputMain = "_outputMain_1tkq5_162";
const inputMain = "_inputMain_1tkq5_162";
const connectedNode = "_connectedNode_1tkq5_185";
const style0$8 = {
  floatingNodes,
  nodesList,
  inputSub,
  outputSub,
  outputMain,
  inputMain,
  connectedNode
};
const cssModules$9 = {
  "$style": style0$8
};
const NDVFloatingNodes = /* @__PURE__ */ _export_sfc(_sfc_main$d, [["__cssModules", cssModules$9]]);
const SIDE_MARGIN = 24;
const SIDE_PANELS_MARGIN = 80;
const MIN_PANEL_WIDTH = 310;
const PANEL_WIDTH = 350;
const PANEL_WIDTH_LARGE = 420;
const _sfc_main$c = /* @__PURE__ */ defineComponent({
  __name: "NDVDraggablePanels",
  props: {
    isDraggable: { type: Boolean },
    hideInputAndOutput: { type: Boolean },
    nodeType: {}
  },
  emits: ["init", "dragstart", "dragend", "switchSelectedNode", "close"],
  setup(__props, { emit: __emit }) {
    const MIN_WINDOW_WIDTH = 2 * (SIDE_MARGIN + SIDE_PANELS_MARGIN) + MIN_PANEL_WIDTH;
    const initialMainPanelWidth = {
      regular: MAIN_NODE_PANEL_WIDTH,
      dragless: MAIN_NODE_PANEL_WIDTH,
      unknown: MAIN_NODE_PANEL_WIDTH,
      inputless: MAIN_NODE_PANEL_WIDTH,
      wide: MAIN_NODE_PANEL_WIDTH * 2
    };
    const throttledOnResize = useThrottleFn(onResize, 100);
    const ndvStore = useNDVStore();
    const uiStore = useUIStore();
    const props = __props;
    const isDragging = ref(false);
    const initialized = ref(false);
    const containerWidth = ref(uiStore.appGridDimensions.width);
    const emit = __emit;
    const slots = useSlots();
    onMounted(() => {
      if (mainPanelDimensions.value.relativeLeft === 1 && mainPanelDimensions.value.relativeRight === 1) {
        setMainPanelWidth();
        setPositions(getInitialLeftPosition(mainPanelDimensions.value.relativeWidth));
        restorePositionData();
      }
      emit("init", { position: mainPanelDimensions.value.relativeLeft });
      setTimeout(() => {
        initialized.value = true;
      }, 0);
      ndvEventBus.on("setPositionByName", setPositionByName);
    });
    onBeforeUnmount(() => {
      ndvEventBus.off("setPositionByName", setPositionByName);
    });
    watch(
      () => uiStore.appGridDimensions,
      async (dimensions) => {
        const ndv = document.getElementById("ndv");
        if (ndv) {
          await nextTick();
          const { width: ndvWidth } = ndv.getBoundingClientRect();
          containerWidth.value = ndvWidth;
        } else {
          containerWidth.value = dimensions.width;
        }
        const minRelativeWidth = pxToRelativeWidth(MIN_PANEL_WIDTH);
        const isBelowMinWidthMainPanel = mainPanelDimensions.value.relativeWidth < minRelativeWidth;
        if (isBelowMinWidthMainPanel) {
          setMainPanelWidth(minRelativeWidth);
        }
        const isBelowMinLeft = minimumLeftPosition.value > mainPanelDimensions.value.relativeLeft;
        const isMaxRight = maximumRightPosition.value > mainPanelDimensions.value.relativeRight;
        if (dimensions.width > MIN_WINDOW_WIDTH && isBelowMinLeft && isMaxRight) {
          setMainPanelWidth(minRelativeWidth);
          setPositions(getInitialLeftPosition(mainPanelDimensions.value.relativeWidth));
        }
        setPositions(mainPanelDimensions.value.relativeLeft);
      }
    );
    const currentNodePaneType = computed(() => {
      if (!hasInputSlot.value) return "inputless";
      if (!props.isDraggable) return "dragless";
      if (props.nodeType === null) return "unknown";
      return props.nodeType.parameterPane ?? "regular";
    });
    const mainPanelDimensions = computed(() => {
      return ndvStore.mainPanelDimensions[currentNodePaneType.value];
    });
    const calculatedPositions = computed(
      () => {
        const hasInput = slots.input !== void 0;
        const outputPanelRelativeLeft = mainPanelDimensions.value.relativeLeft + mainPanelDimensions.value.relativeWidth;
        const inputPanelRelativeRight = hasInput ? 1 - outputPanelRelativeLeft + mainPanelDimensions.value.relativeWidth : 1 - pxToRelativeWidth(SIDE_MARGIN);
        return {
          inputPanelRelativeRight,
          outputPanelRelativeLeft
        };
      }
    );
    const outputPanelRelativeTranslate = computed(() => {
      const panelMinLeft = 1 - pxToRelativeWidth(MIN_PANEL_WIDTH + SIDE_MARGIN);
      const currentRelativeLeftDelta = calculatedPositions.value.outputPanelRelativeLeft - panelMinLeft;
      return currentRelativeLeftDelta > 0 ? currentRelativeLeftDelta : 0;
    });
    const supportedResizeDirections = computed(() => {
      const supportedDirections = ["right"];
      if (props.isDraggable) supportedDirections.push("left");
      return supportedDirections;
    });
    const hasInputSlot = computed(() => {
      return slots.input !== void 0;
    });
    const inputPanelMargin = computed(() => pxToRelativeWidth(SIDE_PANELS_MARGIN));
    const minimumLeftPosition = computed(() => {
      if (containerWidth.value < MIN_WINDOW_WIDTH) return pxToRelativeWidth(1);
      if (!hasInputSlot.value) return pxToRelativeWidth(SIDE_MARGIN);
      return pxToRelativeWidth(SIDE_MARGIN + 20) + inputPanelMargin.value;
    });
    const maximumRightPosition = computed(() => {
      if (containerWidth.value < MIN_WINDOW_WIDTH) return pxToRelativeWidth(1);
      return pxToRelativeWidth(SIDE_MARGIN + 20) + inputPanelMargin.value;
    });
    const canMoveLeft = computed(() => {
      return mainPanelDimensions.value.relativeLeft > minimumLeftPosition.value;
    });
    const canMoveRight = computed(() => {
      return mainPanelDimensions.value.relativeRight > maximumRightPosition.value;
    });
    const mainPanelStyles = computed(() => {
      return {
        left: `${relativeWidthToPx(mainPanelDimensions.value.relativeLeft)}px`,
        right: `${relativeWidthToPx(mainPanelDimensions.value.relativeRight)}px`
      };
    });
    const inputPanelStyles = computed(() => {
      return {
        right: `${relativeWidthToPx(calculatedPositions.value.inputPanelRelativeRight)}px`
      };
    });
    const outputPanelStyles = computed(() => {
      return {
        left: `${relativeWidthToPx(calculatedPositions.value.outputPanelRelativeLeft)}px`,
        transform: `translateX(-${relativeWidthToPx(outputPanelRelativeTranslate.value)}px)`
      };
    });
    const hasDoubleWidth = computed(() => {
      return props.nodeType?.parameterPane === "wide";
    });
    const fixedPanelWidth = computed(() => {
      const multiplier = hasDoubleWidth.value ? 2 : 1;
      if (containerWidth.value > 1700) {
        return PANEL_WIDTH_LARGE * multiplier;
      }
      return PANEL_WIDTH * multiplier;
    });
    const onSwitchSelectedNode = (node2) => emit("switchSelectedNode", node2);
    function getInitialLeftPosition(width) {
      if (currentNodePaneType.value === "dragless")
        return pxToRelativeWidth(SIDE_MARGIN + 1 + fixedPanelWidth.value);
      return hasInputSlot.value ? 0.5 - width / 2 : minimumLeftPosition.value;
    }
    function setMainPanelWidth(relativeWidth) {
      const mainPanelRelativeWidth = relativeWidth || pxToRelativeWidth(initialMainPanelWidth[currentNodePaneType.value]);
      ndvStore.setMainPanelDimensions({
        panelType: currentNodePaneType.value,
        dimensions: {
          relativeWidth: mainPanelRelativeWidth
        }
      });
    }
    function setPositions(relativeLeft) {
      const mainPanelRelativeLeft = relativeLeft || 1 - calculatedPositions.value.inputPanelRelativeRight;
      const mainPanelRelativeRight = 1 - mainPanelRelativeLeft - mainPanelDimensions.value.relativeWidth;
      const isMaxRight = maximumRightPosition.value > mainPanelRelativeRight;
      const isMinLeft = minimumLeftPosition.value > mainPanelRelativeLeft;
      const isInputless = currentNodePaneType.value === "inputless";
      if (isMinLeft) {
        ndvStore.setMainPanelDimensions({
          panelType: currentNodePaneType.value,
          dimensions: {
            relativeLeft: minimumLeftPosition.value,
            relativeRight: 1 - mainPanelDimensions.value.relativeWidth - minimumLeftPosition.value
          }
        });
        return;
      }
      if (isMaxRight) {
        ndvStore.setMainPanelDimensions({
          panelType: currentNodePaneType.value,
          dimensions: {
            relativeLeft: 1 - mainPanelDimensions.value.relativeWidth - maximumRightPosition.value,
            relativeRight: maximumRightPosition.value
          }
        });
        return;
      }
      ndvStore.setMainPanelDimensions({
        panelType: currentNodePaneType.value,
        dimensions: {
          relativeLeft: isInputless ? minimumLeftPosition.value : mainPanelRelativeLeft,
          relativeRight: mainPanelRelativeRight
        }
      });
    }
    function setPositionByName(position) {
      const positionByName = {
        minLeft: minimumLeftPosition.value,
        maxRight: maximumRightPosition.value,
        initial: getInitialLeftPosition(mainPanelDimensions.value.relativeWidth)
      };
      setPositions(positionByName[position]);
    }
    function pxToRelativeWidth(px) {
      return px / containerWidth.value;
    }
    function relativeWidthToPx(relativeWidth) {
      return relativeWidth * containerWidth.value;
    }
    function onResizeEnd() {
      storePositionData();
    }
    function onResizeThrottle(data) {
      if (initialized.value) {
        void throttledOnResize(data);
      }
    }
    function onResize({ direction, x, width }) {
      const relativeDistance = pxToRelativeWidth(x);
      const relativeWidth = pxToRelativeWidth(width);
      if (direction === "left" && relativeDistance <= minimumLeftPosition.value) return;
      if (direction === "right" && 1 - relativeDistance <= maximumRightPosition.value) return;
      if (width <= MIN_PANEL_WIDTH) return;
      setMainPanelWidth(relativeWidth);
      setPositions(direction === "left" ? relativeDistance : mainPanelDimensions.value.relativeLeft);
    }
    function restorePositionData() {
      const storedPanelWidthData = useStorage(
        `${LOCAL_STORAGE_MAIN_PANEL_RELATIVE_WIDTH}_${currentNodePaneType.value}`
      ).value;
      if (storedPanelWidthData) {
        const parsedWidth = parseFloat(storedPanelWidthData);
        setMainPanelWidth(parsedWidth);
        const initialPosition = getInitialLeftPosition(parsedWidth);
        setPositions(initialPosition);
        return true;
      }
      return false;
    }
    function storePositionData() {
      useStorage(`${LOCAL_STORAGE_MAIN_PANEL_RELATIVE_WIDTH}_${currentNodePaneType.value}`).value = mainPanelDimensions.value.relativeWidth.toString();
    }
    function onDragStart() {
      isDragging.value = true;
      emit("dragstart", { position: mainPanelDimensions.value.relativeLeft });
    }
    function onDrag(position) {
      const relativeLeft = pxToRelativeWidth(position[0]) - mainPanelDimensions.value.relativeWidth / 2;
      setPositions(relativeLeft);
    }
    function onDragEnd() {
      setTimeout(() => {
        isDragging.value = false;
        emit("dragend", {
          windowWidth: containerWidth.value,
          position: mainPanelDimensions.value.relativeLeft
        });
      }, 0);
      storePositionData();
    }
    return (_ctx, _cache) => {
      const _component_N8nResizeWrapper = resolveComponent("N8nResizeWrapper");
      return openBlock(), createElementBlock("div", null, [
        unref(ndvStore).activeNode ? (openBlock(), createBlock(NDVFloatingNodes, {
          key: 0,
          "root-node": unref(ndvStore).activeNode,
          onSwitchSelectedNode
        }, null, 8, ["root-node"])) : createCommentVNode("", true),
        !_ctx.hideInputAndOutput ? (openBlock(), createElementBlock("div", {
          key: 1,
          class: normalizeClass(_ctx.$style.inputPanel),
          style: normalizeStyle(inputPanelStyles.value)
        }, [
          renderSlot(_ctx.$slots, "input")
        ], 6)) : createCommentVNode("", true),
        !_ctx.hideInputAndOutput ? (openBlock(), createElementBlock("div", {
          key: 2,
          class: normalizeClass(_ctx.$style.outputPanel),
          style: normalizeStyle(outputPanelStyles.value)
        }, [
          renderSlot(_ctx.$slots, "output")
        ], 6)) : createCommentVNode("", true),
        createBaseVNode("div", {
          class: normalizeClass(_ctx.$style.mainPanel),
          style: normalizeStyle(mainPanelStyles.value)
        }, [
          createVNode(_component_N8nResizeWrapper, {
            "is-resizing-enabled": currentNodePaneType.value !== "unknown",
            width: relativeWidthToPx(mainPanelDimensions.value.relativeWidth),
            "min-width": MIN_PANEL_WIDTH,
            "grid-size": 20,
            "supported-directions": supportedResizeDirections.value,
            outset: "",
            onResize: onResizeThrottle,
            onResizeend: onResizeEnd
          }, {
            default: withCtx(() => [
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.dragButtonContainer)
              }, [
                !_ctx.hideInputAndOutput && _ctx.isDraggable ? (openBlock(), createBlock(PanelDragButton, {
                  key: 0,
                  class: normalizeClass({ [_ctx.$style.draggable]: true, [_ctx.$style.visible]: isDragging.value }),
                  "can-move-left": canMoveLeft.value,
                  "can-move-right": canMoveRight.value,
                  onDragstart: onDragStart,
                  onDrag,
                  onDragend: onDragEnd
                }, null, 8, ["class", "can-move-left", "can-move-right"])) : createCommentVNode("", true)
              ], 2),
              createBaseVNode("div", {
                class: normalizeClass({ [_ctx.$style.mainPanelInner]: true, [_ctx.$style.dragging]: isDragging.value })
              }, [
                renderSlot(_ctx.$slots, "main")
              ], 2)
            ]),
            _: 3
          }, 8, ["is-resizing-enabled", "width", "supported-directions"])
        ], 6)
      ]);
    };
  }
});
const dataPanel = "_dataPanel_181lg_123";
const inputPanel = "_inputPanel_181lg_132 _dataPanel_181lg_123";
const outputPanel = "_outputPanel_181lg_140 _dataPanel_181lg_123";
const mainPanel = "_mainPanel_181lg_148";
const draggable = "_draggable_181lg_152";
const mainPanelInner = "_mainPanelInner_181lg_156";
const dragging = "_dragging_181lg_163";
const dragButtonContainer = "_dragButtonContainer_181lg_176";
const visible = "_visible_181lg_192";
const style0$7 = {
  dataPanel,
  inputPanel,
  outputPanel,
  mainPanel,
  draggable,
  mainPanelInner,
  dragging,
  "double-width": "_double-width_181lg_172",
  dragButtonContainer,
  visible
};
const cssModules$8 = {
  "$style": style0$7
};
const NDVDraggablePanels = /* @__PURE__ */ _export_sfc(_sfc_main$c, [["__cssModules", cssModules$8]]);
var vueJsonPretty$1 = { exports: {} };
var vueJsonPretty = vueJsonPretty$1.exports;
var hasRequiredVueJsonPretty;
function requireVueJsonPretty() {
  if (hasRequiredVueJsonPretty) return vueJsonPretty$1.exports;
  hasRequiredVueJsonPretty = 1;
  (function(module, exports) {
    !function(e, t) {
      module.exports = t(requireVue());
    }(vueJsonPretty, function(e) {
      return function() {
        var t = { 789: function(t2) {
          t2.exports = e;
        } }, n = {};
        function o(e2) {
          var r2 = n[e2];
          if (void 0 !== r2) return r2.exports;
          var l = n[e2] = { exports: {} };
          return t[e2](l, l.exports, o), l.exports;
        }
        o.d = function(e2, t2) {
          for (var n2 in t2) o.o(t2, n2) && !o.o(e2, n2) && Object.defineProperty(e2, n2, { enumerable: true, get: t2[n2] });
        }, o.o = function(e2, t2) {
          return Object.prototype.hasOwnProperty.call(e2, t2);
        }, o.r = function(e2) {
          "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
        };
        var r = {};
        return function() {
          function e2(e3, t3) {
            (null == t3 || t3 > e3.length) && (t3 = e3.length);
            for (var n3 = 0, o2 = new Array(t3); n3 < t3; n3++) o2[n3] = e3[n3];
            return o2;
          }
          function t2(t3, n3) {
            if (t3) {
              if ("string" == typeof t3) return e2(t3, n3);
              var o2 = Object.prototype.toString.call(t3).slice(8, -1);
              return "Object" === o2 && t3.constructor && (o2 = t3.constructor.name), "Map" === o2 || "Set" === o2 ? Array.from(t3) : "Arguments" === o2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o2) ? e2(t3, n3) : void 0;
            }
          }
          function n2(n3) {
            return function(t3) {
              if (Array.isArray(t3)) return e2(t3);
            }(n3) || function(e3) {
              if ("undefined" != typeof Symbol && null != e3[Symbol.iterator] || null != e3["@@iterator"]) return Array.from(e3);
            }(n3) || t2(n3) || function() {
              throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }();
          }
          function l(e3, t3, n3) {
            return t3 in e3 ? Object.defineProperty(e3, t3, { value: n3, enumerable: true, configurable: true, writable: true }) : e3[t3] = n3, e3;
          }
          o.r(r), o.d(r, { default: function() {
            return k;
          } });
          var a = o(789), c = (0, a.defineComponent)({ props: { data: { required: true, type: String }, onClick: Function }, render: function() {
            var e3 = this.data, t3 = this.onClick;
            return (0, a.createVNode)("span", { class: "vjs-tree-brackets", onClick: t3 }, [e3]);
          } }), i = (0, a.defineComponent)({ emits: ["change", "update:modelValue"], props: { checked: { type: Boolean, default: false }, isMultiple: Boolean, onChange: Function }, setup: function(e3, t3) {
            var n3 = t3.emit;
            return { uiType: (0, a.computed)(function() {
              return e3.isMultiple ? "checkbox" : "radio";
            }), model: (0, a.computed)({ get: function() {
              return e3.checked;
            }, set: function(e4) {
              return n3("update:modelValue", e4);
            } }) };
          }, render: function() {
            var e3 = this.uiType, t3 = this.model, n3 = this.$emit;
            return (0, a.createVNode)("label", { class: ["vjs-check-controller", t3 ? "is-checked" : ""], onClick: function(e4) {
              return e4.stopPropagation();
            } }, [(0, a.createVNode)("span", { class: "vjs-check-controller-inner is-".concat(e3) }, null), (0, a.createVNode)("input", { checked: t3, class: "vjs-check-controller-original is-".concat(e3), type: e3, onChange: function() {
              return n3("change", t3);
            } }, null)]);
          } }), u = (0, a.defineComponent)({ props: { nodeType: { required: true, type: String }, onClick: Function }, render: function() {
            var e3 = this.nodeType, t3 = this.onClick, n3 = "objectStart" === e3 || "arrayStart" === e3;
            return n3 || "objectCollapsed" === e3 || "arrayCollapsed" === e3 ? (0, a.createVNode)("span", { class: "vjs-carets vjs-carets-".concat(n3 ? "open" : "close"), onClick: t3 }, [(0, a.createVNode)("svg", { viewBox: "0 0 1024 1024", focusable: "false", "data-icon": "caret-down", width: "1em", height: "1em", fill: "currentColor", "aria-hidden": "true" }, [(0, a.createVNode)("path", { d: "M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z" }, null)])]) : null;
          } });
          function d(e3) {
            return d = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e4) {
              return typeof e4;
            } : function(e4) {
              return e4 && "function" == typeof Symbol && e4.constructor === Symbol && e4 !== Symbol.prototype ? "symbol" : typeof e4;
            }, d(e3);
          }
          function s(e3) {
            return Object.prototype.toString.call(e3).slice(8, -1).toLowerCase();
          }
          function p(e3) {
            var t3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "root", n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, o2 = arguments.length > 3 ? arguments[3] : void 0, r2 = o2 || {}, l2 = r2.key, a2 = r2.index, c2 = r2.type, i2 = void 0 === c2 ? "content" : c2, u2 = r2.showComma, d2 = void 0 !== u2 && u2, f2 = r2.length, y2 = void 0 === f2 ? 1 : f2, v2 = s(e3);
            if ("array" === v2) {
              var b2 = h2(e3.map(function(e4, o3, r3) {
                return p(e4, "".concat(t3, "[").concat(o3, "]"), n3 + 1, { index: o3, showComma: o3 !== r3.length - 1, length: y2, type: i2 });
              }));
              return [p("[", t3, n3, { showComma: false, key: l2, length: e3.length, type: "arrayStart" })[0]].concat(b2, p("]", t3, n3, { showComma: d2, length: e3.length, type: "arrayEnd" })[0]);
            }
            if ("object" === v2) {
              var g2 = Object.keys(e3), m2 = h2(g2.map(function(o3, r3, l3) {
                return p(e3[o3], /^[a-zA-Z_]\w*$/.test(o3) ? "".concat(t3, ".").concat(o3) : "".concat(t3, '["').concat(o3, '"]'), n3 + 1, { key: o3, showComma: r3 !== l3.length - 1, length: y2, type: i2 });
              }));
              return [p("{", t3, n3, { showComma: false, key: l2, index: a2, length: g2.length, type: "objectStart" })[0]].concat(m2, p("}", t3, n3, { showComma: d2, length: g2.length, type: "objectEnd" })[0]);
            }
            return [{ content: e3, level: n3, key: l2, index: a2, path: t3, showComma: d2, length: y2, type: i2 }];
          }
          function h2(e3) {
            if ("function" == typeof Array.prototype.flat) return e3.flat();
            for (var t3 = n2(e3), o2 = []; t3.length; ) {
              var r2 = t3.shift();
              Array.isArray(r2) ? t3.unshift.apply(t3, n2(r2)) : o2.push(r2);
            }
            return o2;
          }
          function f(e3) {
            var t3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : /* @__PURE__ */ new WeakMap();
            if (null == e3) return e3;
            if (e3 instanceof Date) return new Date(e3);
            if (e3 instanceof RegExp) return new RegExp(e3);
            if ("object" !== d(e3)) return e3;
            if (t3.get(e3)) return t3.get(e3);
            if (Array.isArray(e3)) {
              var n3 = e3.map(function(e4) {
                return f(e4, t3);
              });
              return t3.set(e3, n3), n3;
            }
            var o2 = {};
            for (var r2 in e3) o2[r2] = f(e3[r2], t3);
            return t3.set(e3, o2), o2;
          }
          function y(e3, t3) {
            var n3 = Object.keys(e3);
            if (Object.getOwnPropertySymbols) {
              var o2 = Object.getOwnPropertySymbols(e3);
              t3 && (o2 = o2.filter(function(t4) {
                return Object.getOwnPropertyDescriptor(e3, t4).enumerable;
              })), n3.push.apply(n3, o2);
            }
            return n3;
          }
          function v(e3) {
            for (var t3 = 1; t3 < arguments.length; t3++) {
              var n3 = null != arguments[t3] ? arguments[t3] : {};
              t3 % 2 ? y(Object(n3), true).forEach(function(t4) {
                l(e3, t4, n3[t4]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e3, Object.getOwnPropertyDescriptors(n3)) : y(Object(n3)).forEach(function(t4) {
                Object.defineProperty(e3, t4, Object.getOwnPropertyDescriptor(n3, t4));
              });
            }
            return e3;
          }
          var b = { showLength: { type: Boolean, default: false }, showDoubleQuotes: { type: Boolean, default: true }, renderNodeKey: Function, renderNodeValue: Function, selectableType: String, showSelectController: { type: Boolean, default: false }, showLine: { type: Boolean, default: true }, showLineNumber: { type: Boolean, default: false }, selectOnClickNode: { type: Boolean, default: true }, nodeSelectable: { type: Function, default: function() {
            return true;
          } }, highlightSelectedNode: { type: Boolean, default: true }, showIcon: { type: Boolean, default: false }, showKeyValueSpace: { type: Boolean, default: true }, editable: { type: Boolean, default: false }, editableTrigger: { type: String, default: "click" }, onNodeClick: { type: Function }, onBracketsClick: { type: Function }, onIconClick: { type: Function }, onValueChange: { type: Function } }, g = (0, a.defineComponent)({ name: "TreeNode", props: v(v({}, b), {}, { node: { type: Object, required: true }, collapsed: Boolean, checked: Boolean, style: Object, onSelectedChange: { type: Function } }), emits: ["nodeClick", "bracketsClick", "iconClick", "selectedChange", "valueChange"], setup: function(e3, t3) {
            var n3 = t3.emit, o2 = (0, a.computed)(function() {
              return s(e3.node.content);
            }), r2 = (0, a.computed)(function() {
              return "vjs-value vjs-value-".concat(o2.value);
            }), l2 = (0, a.computed)(function() {
              return e3.showDoubleQuotes ? '"'.concat(e3.node.key, '"') : e3.node.key;
            }), d2 = (0, a.computed)(function() {
              return "multiple" === e3.selectableType;
            }), p2 = (0, a.computed)(function() {
              return "single" === e3.selectableType;
            }), h3 = (0, a.computed)(function() {
              return e3.nodeSelectable(e3.node) && (d2.value || p2.value);
            }), f2 = (0, a.reactive)({ editing: false }), y2 = function(t4) {
              var o3, r3, l3 = "null" === (r3 = null === (o3 = t4.target) || void 0 === o3 ? void 0 : o3.value) ? null : "undefined" === r3 ? void 0 : "true" === r3 || "false" !== r3 && (r3[0] + r3[r3.length - 1] === '""' || r3[0] + r3[r3.length - 1] === "''" ? r3.slice(1, -1) : "number" == typeof Number(r3) && !isNaN(Number(r3)) || "NaN" === r3 ? Number(r3) : r3);
              n3("valueChange", l3, e3.node.path);
            }, v2 = (0, a.computed)(function() {
              var t4, n4 = null === (t4 = e3.node) || void 0 === t4 ? void 0 : t4.content;
              return null === n4 ? n4 = "null" : void 0 === n4 && (n4 = "undefined"), "string" === o2.value ? '"'.concat(n4, '"') : n4 + "";
            }), b2 = function() {
              var t4 = e3.renderNodeValue;
              return t4 ? t4({ node: e3.node, defaultValue: v2.value }) : v2.value;
            }, g2 = function() {
              n3("bracketsClick", !e3.collapsed, e3.node.path);
            }, m2 = function() {
              n3("iconClick", !e3.collapsed, e3.node.path);
            }, C2 = function() {
              n3("selectedChange", e3.node);
            }, k2 = function() {
              n3("nodeClick", e3.node), h3.value && e3.selectOnClickNode && n3("selectedChange", e3.node);
            }, w = function(t4) {
              if (e3.editable && !f2.editing) {
                f2.editing = true;
                var n4 = function e4(n5) {
                  var o3;
                  n5.target !== t4.target && (null === (o3 = n5.target) || void 0 === o3 ? void 0 : o3.parentElement) !== t4.target && (f2.editing = false, document.removeEventListener("click", e4));
                };
                document.removeEventListener("click", n4), document.addEventListener("click", n4);
              }
            };
            return function() {
              var t4, n4 = e3.node;
              return (0, a.createVNode)("div", { class: { "vjs-tree-node": true, "has-selector": e3.showSelectController, "has-carets": e3.showIcon, "is-highlight": e3.highlightSelectedNode && e3.checked }, onClick: k2, style: e3.style }, [e3.showLineNumber && (0, a.createVNode)("span", { class: "vjs-node-index" }, [n4.id + 1]), e3.showSelectController && h3.value && "objectEnd" !== n4.type && "arrayEnd" !== n4.type && (0, a.createVNode)(i, { isMultiple: d2.value, checked: e3.checked, onChange: C2 }, null), (0, a.createVNode)("div", { class: "vjs-indent" }, [Array.from(Array(n4.level)).map(function(t5, n5) {
                return (0, a.createVNode)("div", { key: n5, class: { "vjs-indent-unit": true, "has-line": e3.showLine } }, null);
              }), e3.showIcon && (0, a.createVNode)(u, { nodeType: n4.type, onClick: m2 }, null)]), n4.key && (0, a.createVNode)("span", { class: "vjs-key" }, [(t4 = e3.renderNodeKey, t4 ? t4({ node: e3.node, defaultKey: l2.value || "" }) : l2.value), (0, a.createVNode)("span", { class: "vjs-colon" }, [":".concat(e3.showKeyValueSpace ? " " : "")])]), (0, a.createVNode)("span", null, ["content" !== n4.type && n4.content ? (0, a.createVNode)(c, { data: n4.content.toString(), onClick: g2 }, null) : (0, a.createVNode)("span", { class: r2.value, onClick: !e3.editable || e3.editableTrigger && "click" !== e3.editableTrigger ? void 0 : w, onDblclick: e3.editable && "dblclick" === e3.editableTrigger ? w : void 0 }, [e3.editable && f2.editing ? (0, a.createVNode)("input", { value: v2.value, onChange: y2, style: { padding: "3px 8px", border: "1px solid #eee", boxShadow: "none", boxSizing: "border-box", borderRadius: 5, fontFamily: "inherit" } }, null) : b2()]), n4.showComma && (0, a.createVNode)("span", null, [","]), e3.showLength && e3.collapsed && (0, a.createVNode)("span", { class: "vjs-comment" }, [(0, a.createTextVNode)(" // "), n4.length, (0, a.createTextVNode)(" items ")])])]);
            };
          } });
          function m(e3, t3) {
            var n3 = Object.keys(e3);
            if (Object.getOwnPropertySymbols) {
              var o2 = Object.getOwnPropertySymbols(e3);
              t3 && (o2 = o2.filter(function(t4) {
                return Object.getOwnPropertyDescriptor(e3, t4).enumerable;
              })), n3.push.apply(n3, o2);
            }
            return n3;
          }
          function C(e3) {
            for (var t3 = 1; t3 < arguments.length; t3++) {
              var n3 = null != arguments[t3] ? arguments[t3] : {};
              t3 % 2 ? m(Object(n3), true).forEach(function(t4) {
                l(e3, t4, n3[t4]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e3, Object.getOwnPropertyDescriptors(n3)) : m(Object(n3)).forEach(function(t4) {
                Object.defineProperty(e3, t4, Object.getOwnPropertyDescriptor(n3, t4));
              });
            }
            return e3;
          }
          var k = (0, a.defineComponent)({ name: "Tree", props: C(C({}, b), {}, { data: { type: [String, Number, Boolean, Array, Object], default: null }, deep: { type: Number, default: 1 / 0 }, pathCollapsible: { type: Function, default: function() {
            return false;
          } }, rootPath: { type: String, default: "root" }, virtual: { type: Boolean, default: false }, height: { type: Number, default: 400 }, itemHeight: { type: Number, default: 20 }, selectedValue: { type: [String, Array], default: function() {
            return "";
          } }, collapsedOnClickBrackets: { type: Boolean, default: true }, style: Object, onSelectedChange: { type: Function } }), slots: ["renderNodeKey", "renderNodeValue"], emits: ["nodeClick", "bracketsClick", "iconClick", "selectedChange", "update:selectedValue", "update:data"], setup: function(e3, o2) {
            var r2 = o2.emit, c2 = o2.slots, i2 = (0, a.ref)(), u2 = (0, a.computed)(function() {
              return p(e3.data, e3.rootPath);
            }), d2 = function(t3) {
              return u2.value.reduce(function(n3, o3) {
                var r3, a2 = o3.level >= t3, c3 = null === (r3 = e3.pathCollapsible) || void 0 === r3 ? void 0 : r3.call(e3, o3);
                return "objectStart" !== o3.type && "arrayStart" !== o3.type || !a2 && !c3 ? n3 : C(C({}, n3), {}, l({}, o3.path, 1));
              }, {});
            }, s2 = (0, a.reactive)({ translateY: 0, visibleData: null, hiddenPaths: d2(e3.deep) }), h3 = (0, a.computed)(function() {
              for (var e4 = null, t3 = [], n3 = u2.value.length, o3 = 0; o3 < n3; o3++) {
                var r3 = C(C({}, u2.value[o3]), {}, { id: o3 }), l2 = s2.hiddenPaths[r3.path];
                if (e4 && e4.path === r3.path) {
                  var a2 = "objectStart" === e4.type, c3 = C(C(C({}, r3), e4), {}, { showComma: r3.showComma, content: a2 ? "{...}" : "[...]", type: a2 ? "objectCollapsed" : "arrayCollapsed" });
                  e4 = null, t3.push(c3);
                } else {
                  if (l2 && !e4) {
                    e4 = r3;
                    continue;
                  }
                  if (e4) continue;
                  t3.push(r3);
                }
              }
              return t3;
            }), y2 = (0, a.computed)(function() {
              var t3 = e3.selectedValue;
              return t3 && "multiple" === e3.selectableType && Array.isArray(t3) ? t3 : [t3];
            }), v2 = (0, a.computed)(function() {
              return !e3.selectableType || e3.selectOnClickNode || e3.showSelectController ? "" : "When selectableType is not null, selectOnClickNode and showSelectController cannot be false at the same time, because this will cause the selection to fail.";
            }), b2 = function() {
              var t3 = h3.value;
              if (e3.virtual) {
                var n3, o3 = e3.height / e3.itemHeight, r3 = (null === (n3 = i2.value) || void 0 === n3 ? void 0 : n3.scrollTop) || 0, l2 = Math.floor(r3 / e3.itemHeight), a2 = l2 < 0 ? 0 : l2 + o3 > t3.length ? t3.length - o3 : l2;
                a2 < 0 && (a2 = 0);
                var c3 = a2 + o3;
                s2.translateY = a2 * e3.itemHeight, s2.visibleData = t3.filter(function(e4, t4) {
                  return t4 >= a2 && t4 < c3;
                });
              } else s2.visibleData = t3;
            }, m2 = function() {
              b2();
            }, k2 = function(o3) {
              var l2, a2, c3 = o3.path, i3 = e3.selectableType;
              if ("multiple" === i3) {
                var u3 = y2.value.findIndex(function(e4) {
                  return e4 === c3;
                }), d3 = n2(y2.value);
                -1 !== u3 ? d3.splice(u3, 1) : d3.push(c3), r2("update:selectedValue", d3), r2("selectedChange", d3, n2(y2.value));
              } else if ("single" === i3 && y2.value[0] !== c3) {
                var s3 = (l2 = y2.value, a2 = 1, function(e4) {
                  if (Array.isArray(e4)) return e4;
                }(l2) || function(e4, t3) {
                  var n3 = null == e4 ? null : "undefined" != typeof Symbol && e4[Symbol.iterator] || e4["@@iterator"];
                  if (null != n3) {
                    var o4, r3, l3 = [], a3 = true, c4 = false;
                    try {
                      for (n3 = n3.call(e4); !(a3 = (o4 = n3.next()).done) && (l3.push(o4.value), !t3 || l3.length !== t3); a3 = true) ;
                    } catch (e5) {
                      c4 = true, r3 = e5;
                    } finally {
                      try {
                        a3 || null == n3.return || n3.return();
                      } finally {
                        if (c4) throw r3;
                      }
                    }
                    return l3;
                  }
                }(l2, a2) || t2(l2, a2) || function() {
                  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }())[0], p2 = c3;
                r2("update:selectedValue", p2), r2("selectedChange", p2, s3);
              }
            }, w = function(e4) {
              r2("nodeClick", e4);
            }, N = function(e4, t3) {
              if (e4) s2.hiddenPaths = C(C({}, s2.hiddenPaths), {}, l({}, t3, 1));
              else {
                var n3 = C({}, s2.hiddenPaths);
                delete n3[t3], s2.hiddenPaths = n3;
              }
            }, j = function(t3, n3) {
              e3.collapsedOnClickBrackets && N(t3, n3), r2("bracketsClick", t3);
            }, S = function(e4, t3) {
              N(e4, t3), r2("iconClick", e4);
            }, O = function(t3, n3) {
              var o3 = f(e3.data), l2 = e3.rootPath;
              new Function("data", "val", "data".concat(n3.slice(l2.length), "=val"))(o3, t3), r2("update:data", o3);
            };
            return (0, a.watchEffect)(function() {
              v2.value && function(e4) {
                throw new Error("[VueJSONPretty] ".concat(e4));
              }(v2.value);
            }), (0, a.watchEffect)(function() {
              h3.value && b2();
            }), (0, a.watch)(function() {
              return e3.deep;
            }, function(e4) {
              e4 && (s2.hiddenPaths = d2(e4));
            }), function() {
              var t3, n3, o3 = null !== (t3 = e3.renderNodeKey) && void 0 !== t3 ? t3 : c2.renderNodeKey, r3 = null !== (n3 = e3.renderNodeValue) && void 0 !== n3 ? n3 : c2.renderNodeValue, l2 = s2.visibleData && s2.visibleData.map(function(t4) {
                return (0, a.createVNode)(g, { key: t4.id, node: t4, collapsed: !!s2.hiddenPaths[t4.path], showDoubleQuotes: e3.showDoubleQuotes, showLength: e3.showLength, checked: y2.value.includes(t4.path), selectableType: e3.selectableType, showLine: e3.showLine, showLineNumber: e3.showLineNumber, showSelectController: e3.showSelectController, selectOnClickNode: e3.selectOnClickNode, nodeSelectable: e3.nodeSelectable, highlightSelectedNode: e3.highlightSelectedNode, editable: e3.editable, editableTrigger: e3.editableTrigger, showIcon: e3.showIcon, showKeyValueSpace: e3.showKeyValueSpace, renderNodeKey: o3, renderNodeValue: r3, onNodeClick: w, onBracketsClick: j, onIconClick: S, onSelectedChange: k2, onValueChange: O, style: e3.itemHeight && 20 !== e3.itemHeight ? { lineHeight: "".concat(e3.itemHeight, "px") } : {} }, null);
              });
              return (0, a.createVNode)("div", { ref: i2, class: { "vjs-tree": true, "is-virtual": e3.virtual }, onScroll: e3.virtual ? m2 : void 0, style: e3.showLineNumber ? C({ paddingLeft: "".concat(12 * Number(u2.value.length.toString().length), "px") }, e3.style) : e3.style }, [e3.virtual ? (0, a.createVNode)("div", { class: "vjs-tree-list", style: { height: "".concat(e3.height, "px") } }, [(0, a.createVNode)("div", { class: "vjs-tree-list-holder", style: { height: "".concat(h3.value.length * e3.itemHeight, "px") } }, [(0, a.createVNode)("div", { class: "vjs-tree-list-holder-inner", style: { transform: "translateY(".concat(s2.translateY, "px)") } }, [l2])])]) : l2]);
            };
          } });
        }(), r;
      }();
    });
  })(vueJsonPretty$1);
  return vueJsonPretty$1.exports;
}
var vueJsonPrettyExports = requireVueJsonPretty();
const VueJsonPretty = /* @__PURE__ */ getDefaultExportFromCjs(vueJsonPrettyExports);
const sanitizeOptions = {
  allowVulnerableTags: false,
  enforceHtmlBoundary: false,
  disallowedTagsMode: "discard",
  allowedTags: [...sanitizeHtmlExports.defaults.allowedTags, "style", "img", "title"],
  allowedAttributes: {
    ...sanitizeHtmlExports.defaults.allowedAttributes,
    "*": ["class", "style"]
  },
  transformTags: {
    head: ""
  }
};
const _sfc_main$b = {
  name: "RunDataHtml",
  props: {
    inputHtml: {
      type: String,
      required: true
    }
  },
  computed: {
    sanitizedHtml() {
      return sanitizeHtml(this.inputHtml, sanitizeOptions);
    }
  }
};
const _hoisted_1$8 = ["srcdoc"];
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("iframe", {
    class: "__html-display",
    srcdoc: $options.sanitizedHtml
  }, null, 8, _hoisted_1$8);
}
const RunDataHtml = /* @__PURE__ */ _export_sfc(_sfc_main$b, [["render", _sfc_render$1]]);
const RunDataHtml$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: RunDataHtml
}, Symbol.toStringTag, { value: "Module" }));
const _hoisted_1$7 = { key: 0 };
const _hoisted_2$4 = { key: 1 };
const _hoisted_3$3 = { key: 2 };
const _hoisted_4$3 = {
  key: 0,
  controls: "",
  autoplay: ""
};
const _hoisted_5$3 = ["src", "type"];
const _hoisted_6$2 = {
  key: 1,
  controls: "",
  autoplay: ""
};
const _hoisted_7$2 = ["src", "type"];
const _hoisted_8$2 = ["src"];
const _hoisted_9$1 = ["src"];
const _sfc_main$a = /* @__PURE__ */ defineComponent({
  __name: "BinaryDataDisplayEmbed",
  props: {
    binaryData: {}
  },
  setup(__props) {
    const props = __props;
    const isLoading = ref(true);
    const embedSource = ref("");
    const error = ref(false);
    const data = ref("");
    const workflowsStore = useWorkflowsStore();
    const i18n = useI18n();
    const embedClass = computed(() => {
      return [props.binaryData.fileType ?? "other"];
    });
    onMounted(async () => {
      const { id, data: binaryData, fileName, fileType, mimeType } = props.binaryData;
      const isJSONData = fileType === "json";
      const isHTMLData = fileType === "html";
      if (!id) {
        if (isJSONData || isHTMLData) {
          data.value = jsonParse(atob(binaryData));
        } else {
          embedSource.value = "data:" + mimeType + ";base64," + binaryData;
        }
      } else {
        try {
          const binaryUrl = workflowsStore.getBinaryUrl(id, "view", fileName ?? "", mimeType);
          if (isJSONData || isHTMLData) {
            const fetchedData = await fetch(binaryUrl, { credentials: "include" });
            data.value = await (isJSONData ? fetchedData.json() : fetchedData.text());
          } else {
            embedSource.value = binaryUrl;
          }
        } catch (e) {
          error.value = true;
        }
      }
      isLoading.value = false;
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("span", null, [
        isLoading.value ? (openBlock(), createElementBlock("div", _hoisted_1$7, "Loading binary data...")) : error.value ? (openBlock(), createElementBlock("div", _hoisted_2$4, "Error loading binary data")) : (openBlock(), createElementBlock("span", _hoisted_3$3, [
          _ctx.binaryData.fileType === "video" ? (openBlock(), createElementBlock("video", _hoisted_4$3, [
            createBaseVNode("source", {
              src: embedSource.value,
              type: _ctx.binaryData.mimeType
            }, null, 8, _hoisted_5$3),
            createTextVNode(" " + toDisplayString(unref(i18n).baseText("binaryDataDisplay.yourBrowserDoesNotSupport")), 1)
          ])) : _ctx.binaryData.fileType === "audio" ? (openBlock(), createElementBlock("audio", _hoisted_6$2, [
            createBaseVNode("source", {
              src: embedSource.value,
              type: _ctx.binaryData.mimeType
            }, null, 8, _hoisted_7$2),
            createTextVNode(" " + toDisplayString(unref(i18n).baseText("binaryDataDisplay.yourBrowserDoesNotSupport")), 1)
          ])) : _ctx.binaryData.fileType === "image" ? (openBlock(), createElementBlock("img", {
            key: 2,
            src: embedSource.value
          }, null, 8, _hoisted_8$2)) : _ctx.binaryData.fileType === "json" ? (openBlock(), createBlock(unref(VueJsonPretty), {
            key: 3,
            data: data.value,
            deep: 3,
            "show-length": true
          }, null, 8, ["data"])) : _ctx.binaryData.fileType === "html" ? (openBlock(), createBlock(RunDataHtml, {
            key: 4,
            "input-html": data.value
          }, null, 8, ["input-html"])) : (openBlock(), createElementBlock("embed", {
            key: 5,
            src: embedSource.value,
            class: normalizeClass(["binary-data", embedClass.value])
          }, null, 10, _hoisted_9$1))
        ]))
      ]);
    };
  }
});
const _hoisted_1$6 = { class: "binary-data-window-wrapper" };
const _hoisted_2$3 = { key: 0 };
const _sfc_main$9 = /* @__PURE__ */ defineComponent({
  __name: "BinaryDataDisplay",
  props: {
    displayData: {},
    windowVisible: { type: Boolean }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const nodeHelpers = useNodeHelpers();
    const workflowsStore = useWorkflowsStore();
    const i18n = useI18n();
    const workflowRunData = computed(() => {
      const workflowExecution = workflowsStore.getWorkflowExecution;
      if (workflowExecution === null) {
        return null;
      }
      const executionData = workflowExecution.data;
      return executionData ? executionData.resultData.runData : null;
    });
    const binaryData = computed(() => {
      if (typeof props.displayData.node !== "string" || typeof props.displayData.key !== "string" || typeof props.displayData.runIndex !== "number" || typeof props.displayData.index !== "number" || typeof props.displayData.outputIndex !== "number") {
        return null;
      }
      const binaryDataLocal = nodeHelpers.getBinaryData(
        workflowRunData.value,
        props.displayData.node,
        props.displayData.runIndex,
        props.displayData.outputIndex
      );
      if (binaryDataLocal.length === 0) {
        return null;
      }
      if (props.displayData.index >= binaryDataLocal.length || binaryDataLocal[props.displayData.index][props.displayData.key] === void 0) {
        return null;
      }
      const binaryDataItem = binaryDataLocal[props.displayData.index][props.displayData.key];
      return binaryDataItem;
    });
    function closeWindow() {
      emit("close");
      return false;
    }
    return (_ctx, _cache) => {
      const _component_n8n_button = resolveComponent("n8n-button");
      return _ctx.windowVisible ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: normalizeClass(["binary-data-window", binaryData.value?.fileType])
      }, [
        createVNode(_component_n8n_button, {
          size: "small",
          class: "binary-data-window-back",
          title: unref(i18n).baseText("binaryDataDisplay.backToOverviewPage"),
          icon: "arrow-left",
          label: unref(i18n).baseText("binaryDataDisplay.backToList"),
          onClick: withModifiers(closeWindow, ["stop"])
        }, null, 8, ["title", "label"]),
        createBaseVNode("div", _hoisted_1$6, [
          !binaryData.value ? (openBlock(), createElementBlock("div", _hoisted_2$3, toDisplayString(unref(i18n).baseText("binaryDataDisplay.noDataFoundToDisplay")), 1)) : (openBlock(), createBlock(_sfc_main$a, {
            key: 1,
            "binary-data": binaryData.value
          }, null, 8, ["binary-data"]))
        ])
      ], 2)) : createCommentVNode("", true);
    };
  }
});
const _hoisted_1$5 = { key: 0 };
const _hoisted_2$2 = { key: 1 };
const _hoisted_3$2 = { key: 2 };
const _hoisted_4$2 = { key: 0 };
const _hoisted_5$2 = { key: 1 };
const _sfc_main$8 = /* @__PURE__ */ defineComponent({
  __name: "RunDataPinButton",
  props: {
    tooltipContentsVisibility: {},
    dataPinningDocsUrl: {},
    pinnedData: {},
    disabled: { type: Boolean }
  },
  emits: ["togglePinData"],
  setup(__props, { emit: __emit }) {
    const locale = useI18n();
    const props = __props;
    const emit = __emit;
    const visible2 = computed(
      () => props.tooltipContentsVisibility.pinDataDiscoveryTooltipContent ? true : void 0
    );
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(N8nTooltip), {
        placement: "bottom-end",
        visible: visible2.value
      }, {
        content: withCtx(() => [
          props.tooltipContentsVisibility.binaryDataTooltipContent ? (openBlock(), createElementBlock("div", _hoisted_1$5, toDisplayString(unref(locale).baseText("ndv.pinData.pin.binary")), 1)) : props.tooltipContentsVisibility.pinDataDiscoveryTooltipContent ? (openBlock(), createElementBlock("div", _hoisted_2$2, toDisplayString(unref(locale).baseText("node.discovery.pinData.ndv")), 1)) : (openBlock(), createElementBlock("div", _hoisted_3$2, [
            _ctx.pinnedData.hasData.value ? (openBlock(), createElementBlock("div", _hoisted_4$2, [
              createBaseVNode("strong", null, toDisplayString(unref(locale).baseText("ndv.pinData.unpin.title")), 1)
            ])) : (openBlock(), createElementBlock("div", _hoisted_5$2, [
              createBaseVNode("strong", null, toDisplayString(unref(locale).baseText("ndv.pinData.pin.title")), 1),
              createVNode(unref(N8nText), {
                size: "small",
                tag: "p"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(locale).baseText("ndv.pinData.pin.description")) + " ", 1),
                  createVNode(unref(N8nLink), {
                    to: props.dataPinningDocsUrl,
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(locale).baseText("ndv.pinData.pin.link")), 1)
                    ]),
                    _: 1
                  }, 8, ["to"])
                ]),
                _: 1
              })
            ]))
          ]))
        ]),
        default: withCtx(() => [
          createVNode(unref(_sfc_main$o), {
            class: normalizeClass(_ctx.$style.pinDataButton),
            type: "tertiary",
            active: props.pinnedData.hasData.value,
            icon: "thumbtack",
            disabled: props.disabled,
            "data-test-id": "ndv-pin-data",
            onClick: _cache[0] || (_cache[0] = ($event) => emit("togglePinData"))
          }, null, 8, ["class", "active", "disabled"])
        ]),
        _: 1
      }, 8, ["visible"]);
    };
  }
});
const pinDataButton = "_pinDataButton_12tk2_123";
const style0$6 = {
  pinDataButton
};
const cssModules$7 = {
  "$style": style0$6
};
const RunDataPinButton = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["__cssModules", cssModules$7]]);
const _hoisted_1$4 = {
  key: 0,
  class: "ml-4xs"
};
const _hoisted_2$1 = { key: 0 };
const _hoisted_3$1 = { key: 1 };
const _hoisted_4$1 = { key: 0 };
const _hoisted_5$1 = ["data-test-id"];
const _hoisted_6$1 = { key: 0 };
const _hoisted_7$1 = { key: 1 };
const _hoisted_8$1 = { key: 2 };
const _hoisted_9 = { key: 3 };
const _hoisted_10 = { key: 4 };
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "RunData",
  props: {
    workflow: {},
    runIndex: {},
    tooMuchDataTitle: {},
    executingMessage: {},
    pushRef: {},
    paneType: {},
    noDataInBranchMessage: {},
    node: { default: null },
    nodes: { default: () => [] },
    linkedRuns: { type: Boolean },
    canLinkRuns: { type: Boolean },
    isExecuting: { type: Boolean, default: false },
    overrideOutputs: { default: void 0 },
    mappingEnabled: { type: Boolean, default: false },
    distanceFromActive: { default: 0 },
    blockUI: { type: Boolean, default: false },
    isProductionExecutionPreview: { type: Boolean, default: false },
    isPaneActive: { type: Boolean, default: false },
    hidePagination: { type: Boolean, default: false },
    calloutMessage: { default: void 0 }
  },
  emits: ["search", "runChange", "itemHover", "linkRun", "unlinkRun", "activatePane", "tableMounted"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const LazyRunDataTable = defineAsyncComponent(
      async () => await __vitePreload(() => import("./RunDataTable-DEgXQifj.js"), true ? __vite__mapDeps([0,1,2,3,4,5]) : void 0)
    );
    const LazyRunDataJson = defineAsyncComponent(
      async () => await __vitePreload(() => import("./RunDataJson-BRO48N-i.js"), true ? __vite__mapDeps([6,1,2,7,8,9,10,3,4,11,12,13]) : void 0)
    );
    const LazyRunDataSchema = defineAsyncComponent(
      async () => await __vitePreload(() => import("./index-CN4JmOoA.js").then((n) => n.iq), true ? __vite__mapDeps([1,2]) : void 0)
    );
    const LazyRunDataHtml = defineAsyncComponent(
      async () => await __vitePreload(() => Promise.resolve().then(() => RunDataHtml$1), true ? void 0 : void 0)
    );
    const LazyRunDataSearch = defineAsyncComponent(
      async () => await __vitePreload(() => import("./RunDataSearch-CNfhlvrK.js"), true ? __vite__mapDeps([14,1,2,15]) : void 0)
    );
    const props = __props;
    const emit = __emit;
    const connectionType2 = ref(NodeConnectionTypes.Main);
    const dataSize = ref(0);
    const showData = ref(false);
    const userEnabledShowData = ref(false);
    const outputIndex = ref(0);
    const binaryDataDisplayVisible = ref(false);
    const binaryDataDisplayData = ref(null);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const pageSizes = [1, 10, 25, 50, 100];
    const pinDataDiscoveryTooltipVisible = ref(false);
    const isControlledPinDataTooltip = ref(false);
    const search2 = ref("");
    const dataContainerRef = ref();
    const nodeTypesStore = useNodeTypesStore();
    const ndvStore = useNDVStore();
    const workflowsStore = useWorkflowsStore();
    const sourceControlStore = useSourceControlStore();
    const rootStore = useRootStore();
    const uiStore = useUIStore();
    const schemaPreviewStore = useSchemaPreviewStore();
    const posthogStore = usePostHog();
    const toast = useToast();
    const route = useRoute();
    const nodeHelpers = useNodeHelpers();
    const externalHooks = useExternalHooks();
    const telemetry = useTelemetry();
    const i18n = useI18n();
    const node2 = toRef$1(props, "node");
    const pinnedData = usePinnedData(node2, {
      runIndex: props.runIndex,
      displayMode: props.paneType === "input" ? ndvStore.inputPanelDisplayMode : ndvStore.outputPanelDisplayMode
    });
    const { isSubNodeType } = useNodeType({
      node: node2
    });
    const displayMode = computed(
      () => props.paneType === "input" ? ndvStore.inputPanelDisplayMode : ndvStore.outputPanelDisplayMode
    );
    const isReadOnlyRoute = computed(() => route.meta.readOnlyCanvas === true);
    const isWaitNodeWaiting = computed(() => {
      return node2.value?.name && workflowExecution.value?.data?.resultData?.runData?.[node2.value?.name]?.[props.runIndex]?.executionStatus === "waiting";
    });
    const { activeNode } = storeToRefs(ndvStore);
    const nodeType = computed(() => {
      if (!node2.value) return null;
      return nodeTypesStore.getNodeType(node2.value.type, node2.value.typeVersion);
    });
    const isSchemaView = computed(() => displayMode.value === "schema");
    const isSearchInSchemaView = computed(() => isSchemaView.value && !!search2.value);
    const displaysMultipleNodes = computed(
      () => isSchemaView.value && props.paneType === "input" && props.nodes.length > 0
    );
    const isTriggerNode = computed(() => !!node2.value && nodeTypesStore.isTriggerNode(node2.value.type));
    const canPinData = computed(
      () => !!node2.value && pinnedData.canPinNode(false, currentOutputIndex.value) && !isPaneTypeInput.value && pinnedData.isValidNodeType.value && !(binaryData.value && binaryData.value.length > 0)
    );
    const displayModes2 = computed(() => {
      const defaults = [
        { label: i18n.baseText("runData.table"), value: "table" },
        { label: i18n.baseText("runData.json"), value: "json" }
      ];
      if (binaryData.value.length) {
        defaults.push({ label: i18n.baseText("runData.binary"), value: "binary" });
      }
      const schemaView = { label: i18n.baseText("runData.schema"), value: "schema" };
      if (isPaneTypeInput.value) {
        defaults.unshift(schemaView);
      } else {
        defaults.push(schemaView);
      }
      if (isPaneTypeOutput.value && activeNode.value?.type === HTML_NODE_TYPE && activeNode.value.parameters.operation === "generateHtmlTemplate") {
        defaults.unshift({ label: "HTML", value: "html" });
      }
      return defaults;
    });
    const hasNodeRun = computed(
      () => Boolean(
        !props.isExecuting && node2.value && (workflowRunData.value && workflowRunData.value.hasOwnProperty(node2.value.name) || pinnedData.hasData.value)
      )
    );
    const isArtificialRecoveredEventItem = computed(
      () => rawInputData.value?.[0]?.json?.isArtificialRecoveredEventItem
    );
    const isTrimmedManualExecutionDataItem = computed(
      () => rawInputData.value?.[0]?.json?.[TRIMMED_TASK_DATA_CONNECTIONS_KEY]
    );
    const subworkflowExecutionError = computed(() => {
      if (!node2.value) return null;
      return {
        node: node2.value,
        messages: [workflowsStore.subWorkflowExecutionError?.message ?? ""]
      };
    });
    const hasSubworkflowExecutionError = computed(
      () => Boolean(workflowsStore.subWorkflowExecutionError)
    );
    const parentNodeError = computed(() => {
      const parentNode = props.workflow.getChildNodes(node2.value?.name ?? "", "ALL_NON_MAIN")[0];
      return workflowRunData.value?.[parentNode]?.[props.runIndex]?.error;
    });
    const workflowRunErrorAsNodeError = computed(() => {
      if (!node2.value) {
        return null;
      }
      if (isSubNodeType.value && props.paneType === "input") {
        return parentNodeError.value;
      }
      return workflowRunData.value?.[node2.value?.name]?.[props.runIndex]?.error;
    });
    const hasRunError = computed(() => Boolean(node2.value && workflowRunErrorAsNodeError.value));
    const executionHints = computed(() => {
      if (hasNodeRun.value) {
        const hints = node2.value && workflowRunData.value?.[node2.value.name]?.[props.runIndex]?.hints;
        if (hints) return hints;
      }
      return [];
    });
    const workflowExecution = computed(() => workflowsStore.getWorkflowExecution);
    const workflowRunData = computed(() => {
      if (workflowExecution.value === null) {
        return null;
      }
      const executionData = workflowExecution.value.data;
      if (executionData?.resultData) {
        return executionData.resultData.runData;
      }
      return null;
    });
    const dataCount = computed(
      () => getDataCount(props.runIndex, currentOutputIndex.value, connectionType2.value)
    );
    const unfilteredDataCount = computed(
      () => pinnedData.data.value ? pinnedData.data.value.length : rawInputData.value.length
    );
    const dataSizeInMB = computed(() => (dataSize.value / (1024 * 1024)).toFixed(1));
    const maxOutputIndex = computed(() => {
      if (node2.value === null || props.runIndex === void 0) {
        return 0;
      }
      const runData = workflowRunData.value;
      if (runData === null || !runData.hasOwnProperty(node2.value.name)) {
        return 0;
      }
      if (runData[node2.value.name].length < props.runIndex) {
        return 0;
      }
      if (runData[node2.value.name][props.runIndex]) {
        const taskData = runData[node2.value.name][props.runIndex].data;
        if (taskData?.main) {
          return taskData.main.length - 1;
        }
      }
      return 0;
    });
    const currentPageOffset = computed(() => pageSize.value * (currentPage.value - 1));
    const maxRunIndex = computed(() => {
      if (!node2.value) {
        return 0;
      }
      const runData = workflowRunData.value;
      if (runData === null || !runData.hasOwnProperty(node2.value.name)) {
        return 0;
      }
      if (runData[node2.value.name].length) {
        return runData[node2.value.name].length - 1;
      }
      return 0;
    });
    const rawInputData = computed(
      () => getRawInputData(props.runIndex, currentOutputIndex.value, connectionType2.value)
    );
    const unfilteredInputData = computed(() => getPinDataOrLiveData(rawInputData.value));
    const inputData = computed(() => getFilteredData(unfilteredInputData.value));
    const inputDataPage = computed(() => {
      const offset = pageSize.value * (currentPage.value - 1);
      return inputData.value.slice(offset, offset + pageSize.value);
    });
    const jsonData = computed(() => executionDataToJson(inputData.value));
    const binaryData = computed(() => {
      if (!node2.value) {
        return [];
      }
      return nodeHelpers.getBinaryData(workflowRunData.value, node2.value.name, props.runIndex, currentOutputIndex.value).filter((data) => Boolean(data && Object.keys(data).length));
    });
    const inputHtml = computed(() => String(inputData.value[0]?.json?.html ?? ""));
    const currentOutputIndex = computed(() => {
      if (props.overrideOutputs?.length && !props.overrideOutputs.includes(outputIndex.value)) {
        return props.overrideOutputs[0];
      }
      return Math.min(outputIndex.value, maxOutputIndex.value);
    });
    const branches = computed(() => {
      const capitalize = (name) => name.charAt(0).toLocaleUpperCase() + name.slice(1);
      const result = [];
      for (let i = 0; i <= maxOutputIndex.value; i++) {
        if (props.overrideOutputs && !props.overrideOutputs.includes(i)) {
          continue;
        }
        const totalItemsCount = getRawInputData(props.runIndex, i).length;
        const itemsCount2 = getDataCount(props.runIndex, i);
        const items = search2.value ? i18n.baseText("ndv.search.items", {
          adjustToNumber: totalItemsCount,
          interpolate: { matched: itemsCount2, total: totalItemsCount }
        }) : i18n.baseText("ndv.output.items", {
          adjustToNumber: itemsCount2,
          interpolate: { count: itemsCount2 }
        });
        let outputName = getOutputName(i);
        if (`${outputName}` === `${i}`) {
          outputName = `${i18n.baseText("ndv.output")} ${outputName}`;
        } else {
          const appendBranchWord = NODE_TYPES_EXCLUDED_FROM_OUTPUT_NAME_APPEND.includes(
            node2.value?.type ?? ""
          ) ? "" : ` ${i18n.baseText("ndv.output.branch")}`;
          outputName = capitalize(`${getOutputName(i)}${appendBranchWord}`);
        }
        result.push({
          label: search2.value && itemsCount2 || totalItemsCount ? `${outputName} (${items})` : outputName,
          value: i
        });
      }
      return result;
    });
    const editMode2 = computed(() => {
      return isPaneTypeInput.value ? { enabled: false, value: "" } : ndvStore.outputPanelEditMode;
    });
    const isPaneTypeInput = computed(() => props.paneType === "input");
    const isPaneTypeOutput = computed(() => props.paneType === "output");
    const readOnlyEnv = computed(() => sourceControlStore.preferences.branchReadOnly);
    const showIOSearch = computed(
      () => hasNodeRun.value && !hasRunError.value && unfilteredInputData.value.length > 0
    );
    const inputSelectLocation = computed(() => {
      if (isSchemaView.value) return "none";
      if (!hasNodeRun.value) return "header";
      if (maxRunIndex.value > 0) return "runs";
      if (maxOutputIndex.value > 0 && branches.value.length > 1) {
        return "outputs";
      }
      return "items";
    });
    const showIoSearchNoMatchContent = computed(
      () => hasNodeRun.value && !inputData.value.length && !!search2.value
    );
    const parentNodeOutputData = computed(() => {
      const parentNode = props.workflow.getParentNodesByDepth(node2.value?.name ?? "")[0];
      let parentNodeData = [];
      if (parentNode?.name) {
        parentNodeData = nodeHelpers.getNodeInputData(
          props.workflow.getNode(parentNode?.name),
          props.runIndex,
          outputIndex.value,
          "input",
          connectionType2.value
        );
      }
      return parentNodeData;
    });
    const parentNodePinnedData = computed(() => {
      const parentNode = props.workflow.getParentNodesByDepth(node2.value?.name ?? "")[0];
      return props.workflow.pinData?.[parentNode?.name || ""] ?? [];
    });
    const showPinButton = computed(() => {
      if (!rawInputData.value.length && !pinnedData.hasData.value) {
        return false;
      }
      if (editMode2.value.enabled) {
        return false;
      }
      if (binaryData.value?.length) {
        return isPaneTypeOutput.value;
      }
      return canPinData.value;
    });
    const pinButtonDisabled = computed(
      () => !rawInputData.value.length && !pinnedData.hasData.value || !!binaryData.value?.length || isReadOnlyRoute.value || readOnlyEnv.value
    );
    const activeTaskMetadata = computed(() => {
      if (!node2.value) {
        return null;
      }
      const errorMetadata = parseErrorMetadata(workflowRunErrorAsNodeError.value);
      if (errorMetadata !== void 0) {
        return errorMetadata;
      }
      if (parentNodeError.value) {
        const subNodeMetadata = parseErrorMetadata(parentNodeError.value);
        if (subNodeMetadata !== void 0) {
          return subNodeMetadata;
        }
      }
      return workflowRunData.value?.[node2.value.name]?.[props.runIndex]?.metadata ?? null;
    });
    const hasInputOverwrite = computed(() => {
      if (!node2.value) {
        return false;
      }
      const taskData = nodeHelpers.getNodeTaskData(node2.value, props.runIndex);
      return Boolean(taskData?.inputOverride);
    });
    const isSchemaPreviewEnabled = computed(
      () => props.paneType === "input" && !(nodeType.value?.codex?.categories ?? []).some(
        (category) => category === CORE_NODES_CATEGORY
      ) && posthogStore.isVariantEnabled(
        SCHEMA_PREVIEW_EXPERIMENT.name,
        SCHEMA_PREVIEW_EXPERIMENT.variant
      )
    );
    const hasPreviewSchema = computedAsync(async () => {
      if (!isSchemaPreviewEnabled.value || props.nodes.length === 0) return false;
      const nodes = props.nodes.filter((n) => n.depth === 1).map((n) => workflowsStore.getNodeByName(n.name)).filter(isPresent);
      for (const connectedNode2 of nodes) {
        const { type, typeVersion, parameters } = connectedNode2;
        const hasPreview = await schemaPreviewStore.getSchemaPreview({
          nodeType: type,
          version: typeVersion,
          resource: parameters.resource,
          operation: parameters.operation
        });
        if (hasPreview.ok) return true;
      }
      return false;
    }, false);
    watch(node2, (newNode, prevNode) => {
      if (newNode?.id === prevNode?.id) return;
      init();
    });
    watch(hasNodeRun, () => {
      if (props.paneType === "output") setDisplayMode();
      else {
        outputIndex.value = determineInitialOutputIndex();
      }
    });
    watch(
      inputDataPage,
      (data) => {
        if (props.paneType && data) {
          ndvStore.setNDVPanelDataIsEmpty({
            panel: props.paneType,
            isEmpty: data.every((item) => isEmpty(item.json))
          });
        }
      },
      { immediate: true, deep: true }
    );
    watch(jsonData, (data, prevData) => {
      if (isEqual(data, prevData)) return;
      refreshDataSize();
      if (dataCount.value) {
        resetCurrentPageIfTooFar();
      }
      showPinDataDiscoveryTooltip(data);
    });
    watch(binaryData, (newData, prevData) => {
      if (newData.length && !prevData.length && displayMode.value !== "binary") {
        switchToBinary();
      } else if (!newData.length && displayMode.value === "binary") {
        onDisplayModeChange("table");
      }
    });
    watch(currentOutputIndex, (branchIndex) => {
      ndvStore.setNDVBranchIndex({
        pane: props.paneType,
        branchIndex
      });
    });
    watch(search2, (newSearch) => {
      emit("search", newSearch);
    });
    onMounted(() => {
      init();
      if (!isPaneTypeInput.value) {
        showPinDataDiscoveryTooltip(jsonData.value);
      }
      ndvStore.setNDVBranchIndex({
        pane: props.paneType,
        branchIndex: currentOutputIndex.value
      });
      if (props.paneType === "output") {
        setDisplayMode();
        activatePane();
      }
      if (hasRunError.value && node2.value) {
        const error = workflowRunData.value?.[node2.value.name]?.[props.runIndex]?.error;
        const errorsToTrack = ["unknown error"];
        if (error && errorsToTrack.some((e) => error.message?.toLowerCase().includes(e))) {
          telemetry.track(
            "User encountered an error",
            {
              node: node2.value.type,
              errorMessage: error.message,
              nodeVersion: node2.value.typeVersion,
              n8nVersion: rootStore.versionCli
            },
            {
              withPostHog: true
            }
          );
        }
      }
    });
    onBeforeUnmount(() => {
      hidePinDataDiscoveryTooltip();
    });
    function getResolvedNodeOutputs() {
      if (node2.value && nodeType.value) {
        const workflowNode = props.workflow.getNode(node2.value.name);
        if (workflowNode) {
          const outputs2 = getNodeOutputs(props.workflow, workflowNode, nodeType.value);
          return outputs2;
        }
      }
      return [];
    }
    function shouldHintBeDisplayed(hint) {
      const { location, whenToDisplay } = hint;
      if (location) {
        if (location === "ndv" && !["input", "output"].includes(props.paneType)) {
          return false;
        }
        if (location === "inputPane" && props.paneType !== "input") {
          return false;
        }
        if (location === "outputPane" && props.paneType !== "output") {
          return false;
        }
      }
      if (whenToDisplay === "afterExecution" && !hasNodeRun.value) {
        return false;
      }
      if (whenToDisplay === "beforeExecution" && hasNodeRun.value) {
        return false;
      }
      return true;
    }
    function getNodeHints$1() {
      try {
        if (node2.value && nodeType.value) {
          const workflowNode = props.workflow.getNode(node2.value.name);
          if (workflowNode) {
            const nodeHints = getNodeHints(props.workflow, workflowNode, nodeType.value, {
              runExecutionData: workflowExecution.value?.data ?? null,
              runIndex: props.runIndex,
              connectionInputData: parentNodeOutputData.value
            });
            const hasMultipleInputItems = parentNodeOutputData.value.length > 1 || parentNodePinnedData.value.length > 1;
            const nodeOutputData = workflowRunData.value?.[node2.value.name]?.[props.runIndex]?.data?.main?.[0] ?? [];
            const genericHints = getGenericHints({
              workflowNode,
              node: node2.value,
              nodeType: nodeType.value,
              nodeOutputData,
              workflow: props.workflow,
              hasNodeRun: hasNodeRun.value,
              hasMultipleInputItems
            });
            return executionHints.value.concat(nodeHints, genericHints).filter(shouldHintBeDisplayed);
          }
        }
      } catch (error) {
        console.error("Error while getting node hints", error);
      }
      return [];
    }
    function onItemHover(itemIndex) {
      if (itemIndex === null) {
        emit("itemHover", null);
        return;
      }
      emit("itemHover", {
        outputIndex: currentOutputIndex.value,
        itemIndex
      });
    }
    function onClickDataPinningDocsLink() {
      telemetry.track("User clicked ndv link", {
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        node_type: activeNode.value?.type,
        pane: "output",
        type: "data-pinning-docs"
      });
    }
    function showPinDataDiscoveryTooltip(value) {
      if (!isTriggerNode.value) {
        return;
      }
      const pinDataDiscoveryFlag = useStorage(LOCAL_STORAGE_PIN_DATA_DISCOVERY_NDV_FLAG).value;
      if (value && value.length > 0 && !isReadOnlyRoute.value && !pinDataDiscoveryFlag) {
        pinDataDiscoveryComplete();
        setTimeout(() => {
          isControlledPinDataTooltip.value = true;
          pinDataDiscoveryTooltipVisible.value = true;
          dataPinningEventBus.emit("data-pinning-discovery", { isTooltipVisible: true });
        }, 500);
      }
    }
    function hidePinDataDiscoveryTooltip() {
      if (pinDataDiscoveryTooltipVisible.value) {
        isControlledPinDataTooltip.value = false;
        pinDataDiscoveryTooltipVisible.value = false;
        dataPinningEventBus.emit("data-pinning-discovery", { isTooltipVisible: false });
      }
    }
    function pinDataDiscoveryComplete() {
      useStorage(LOCAL_STORAGE_PIN_DATA_DISCOVERY_NDV_FLAG).value = "true";
      useStorage(LOCAL_STORAGE_PIN_DATA_DISCOVERY_CANVAS_FLAG).value = "true";
    }
    function enterEditMode({ origin }) {
      const inputData2 = pinnedData.data.value ? clearJsonKey(pinnedData.data.value) : executionDataToJson(rawInputData.value);
      const inputDataLength = Array.isArray(inputData2) ? inputData2.length : Object.keys(inputData2 ?? {}).length;
      const data = inputDataLength > 0 ? inputData2 : TEST_PIN_DATA;
      ndvStore.setOutputPanelEditModeEnabled(true);
      ndvStore.setOutputPanelEditModeValue(JSON.stringify(data, null, 2));
      telemetry.track("User opened ndv edit state", {
        node_type: activeNode.value?.type,
        click_type: origin === "editIconButton" ? "button" : "link",
        push_ref: props.pushRef,
        run_index: props.runIndex,
        is_output_present: hasNodeRun.value || pinnedData.hasData.value,
        view: !hasNodeRun.value && !pinnedData.hasData.value ? "undefined" : displayMode.value,
        is_data_pinned: pinnedData.hasData.value
      });
    }
    function onClickCancelEdit() {
      ndvStore.setOutputPanelEditModeEnabled(false);
      ndvStore.setOutputPanelEditModeValue("");
      onExitEditMode({ type: "cancel" });
    }
    function onClickSaveEdit() {
      if (!node2.value) {
        return;
      }
      const { value } = editMode2.value;
      toast.clearAllStickyNotifications();
      try {
        const clearedValue = clearJsonKey(value);
        try {
          pinnedData.setData(clearedValue, "save-edit");
        } catch (error) {
          return;
        }
      } catch (error) {
        toast.showError(error, i18n.baseText("ndv.pinData.error.syntaxError.title"));
        return;
      }
      ndvStore.setOutputPanelEditModeEnabled(false);
      onExitEditMode({ type: "save" });
    }
    function onExitEditMode({ type }) {
      telemetry.track("User closed ndv edit state", {
        node_type: activeNode.value?.type,
        push_ref: props.pushRef,
        run_index: props.runIndex,
        view: displayMode.value,
        type
      });
    }
    async function onTogglePinData({ source }) {
      if (!node2.value) {
        return;
      }
      if (source === "pin-icon-click") {
        const telemetryPayload = {
          node_type: activeNode.value?.type,
          push_ref: props.pushRef,
          run_index: props.runIndex,
          view: !hasNodeRun.value && !pinnedData.hasData.value ? "none" : displayMode.value
        };
        void externalHooks.run("runData.onTogglePinData", telemetryPayload);
        telemetry.track("User clicked pin data icon", telemetryPayload);
      }
      nodeHelpers.updateNodeParameterIssues(node2.value);
      if (pinnedData.hasData.value) {
        pinnedData.unsetData(source);
        return;
      }
      try {
        pinnedData.setData(rawInputData.value, "pin-icon-click");
      } catch (error) {
        console.error(error);
        return;
      }
      if (maxRunIndex.value > 0) {
        toast.showToast({
          title: i18n.baseText("ndv.pinData.pin.multipleRuns.title", {
            interpolate: {
              index: `${props.runIndex}`
            }
          }),
          message: i18n.baseText("ndv.pinData.pin.multipleRuns.description"),
          type: "success",
          duration: 2e3
        });
      }
      hidePinDataDiscoveryTooltip();
      pinDataDiscoveryComplete();
    }
    function switchToBinary() {
      onDisplayModeChange("binary");
    }
    function onBranchChange(value) {
      outputIndex.value = value;
      telemetry.track("User changed ndv branch", {
        push_ref: props.pushRef,
        branch_index: value,
        node_type: activeNode.value?.type,
        node_type_input_selection: nodeType.value ? nodeType.value.name : "",
        pane: props.paneType
      });
    }
    function showTooMuchData() {
      showData.value = true;
      userEnabledShowData.value = true;
      telemetry.track("User clicked ndv button", {
        node_type: activeNode.value?.type,
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        pane: props.paneType,
        type: "showTooMuchData"
      });
    }
    function toggleLinkRuns() {
      if (props.linkedRuns) {
        unlinkRun();
      } else {
        linkRun();
      }
    }
    function linkRun() {
      emit("linkRun");
    }
    function unlinkRun() {
      emit("unlinkRun");
    }
    function onCurrentPageChange(value) {
      currentPage.value = value;
      telemetry.track("User changed ndv page", {
        node_type: activeNode.value?.type,
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        pane: props.paneType,
        page_selected: currentPage.value,
        page_size: pageSize.value,
        items_total: dataCount.value
      });
    }
    function resetCurrentPageIfTooFar() {
      const maxPage = Math.ceil(dataCount.value / pageSize.value);
      if (maxPage < currentPage.value) {
        currentPage.value = maxPage;
      }
    }
    function onPageSizeChange(newPageSize) {
      pageSize.value = newPageSize;
      resetCurrentPageIfTooFar();
      telemetry.track("User changed ndv page size", {
        node_type: activeNode.value?.type,
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        pane: props.paneType,
        page_selected: currentPage.value,
        page_size: pageSize.value,
        items_total: dataCount.value
      });
    }
    function onDisplayModeChange(newDisplayMode) {
      const previous = displayMode.value;
      ndvStore.setPanelDisplayMode({ pane: props.paneType, mode: newDisplayMode });
      if (!userEnabledShowData.value) updateShowData();
      if (dataContainerRef.value) {
        const dataDisplay2 = dataContainerRef.value.children[0];
        if (dataDisplay2) {
          dataDisplay2.scrollTo(0, 0);
        }
      }
      closeBinaryDataDisplay();
      void externalHooks.run("runData.displayModeChanged", {
        newValue: newDisplayMode,
        oldValue: previous
      });
      if (activeNode.value) {
        telemetry.track("User changed ndv item view", {
          previous_view: previous,
          new_view: newDisplayMode,
          node_type: activeNode.value.type,
          workflow_id: workflowsStore.workflowId,
          push_ref: props.pushRef,
          pane: props.paneType
        });
      }
    }
    function getRunLabel(option) {
      if (!node2.value) {
        return;
      }
      let itemsCount2 = 0;
      for (let i = 0; i <= maxOutputIndex.value; i++) {
        itemsCount2 += getPinDataOrLiveData(getRawInputData(option - 1, i)).length;
      }
      const items = i18n.baseText("ndv.output.items", {
        adjustToNumber: itemsCount2,
        interpolate: { count: itemsCount2 }
      });
      const metadata = workflowRunData.value?.[node2.value.name]?.[option - 1]?.metadata ?? null;
      const subexecutions = metadata?.subExecutionsCount ? i18n.baseText("ndv.output.andSubExecutions", {
        adjustToNumber: metadata.subExecutionsCount,
        interpolate: {
          count: metadata.subExecutionsCount
        }
      }) : "";
      const itemsLabel = itemsCount2 > 0 ? ` (${items}${subexecutions})` : "";
      return option + i18n.baseText("ndv.output.of") + (maxRunIndex.value + 1) + itemsLabel;
    }
    function getRawInputData(runIndex, outputIndex2, connectionType22 = NodeConnectionTypes.Main) {
      let inputData2 = [];
      if (node2.value) {
        inputData2 = nodeHelpers.getNodeInputData(
          node2.value,
          runIndex,
          outputIndex2,
          props.paneType,
          connectionType22
        );
      }
      if (inputData2.length === 0 || !Array.isArray(inputData2)) {
        return [];
      }
      return inputData2;
    }
    function getPinDataOrLiveData(data) {
      if (pinnedData.data.value && !props.isProductionExecutionPreview) {
        return Array.isArray(pinnedData.data.value) ? pinnedData.data.value.map((value) => ({
          json: value
        })) : [
          {
            json: pinnedData.data.value
          }
        ];
      }
      return data;
    }
    function getFilteredData(data) {
      if (!search2.value || isSchemaView.value) {
        return data;
      }
      currentPage.value = 1;
      return data.filter(({ json }) => searchInObject(json, search2.value));
    }
    function getDataCount(runIndex, outputIndex2, connectionType22 = NodeConnectionTypes.Main) {
      if (!node2.value) {
        return 0;
      }
      if (workflowRunData.value?.[node2.value.name]?.[runIndex]?.hasOwnProperty("error")) {
        return 1;
      }
      const rawInputData2 = getRawInputData(runIndex, outputIndex2, connectionType22);
      const pinOrLiveData = getPinDataOrLiveData(rawInputData2);
      return getFilteredData(pinOrLiveData).length;
    }
    function determineInitialOutputIndex() {
      for (let i = 0; i <= maxOutputIndex.value; i++) {
        if (getRawInputData(props.runIndex, i).length) {
          return i;
        }
      }
      return 0;
    }
    function init() {
      outputIndex.value = determineInitialOutputIndex();
      refreshDataSize();
      closeBinaryDataDisplay();
      let outputTypes = [];
      if (node2.value && nodeType.value) {
        const outputs2 = getResolvedNodeOutputs();
        outputTypes = getConnectionTypes(outputs2);
      }
      connectionType2.value = outputTypes.length === 0 ? NodeConnectionTypes.Main : outputTypes[0];
      if (binaryData.value.length > 0) {
        ndvStore.setPanelDisplayMode({
          pane: props.paneType,
          mode: "binary"
        });
      } else if (displayMode.value === "binary") {
        ndvStore.setPanelDisplayMode({
          pane: props.paneType,
          mode: "schema"
        });
      }
    }
    function closeBinaryDataDisplay() {
      binaryDataDisplayVisible.value = false;
      binaryDataDisplayData.value = null;
    }
    function isViewable(index, key) {
      const { fileType } = binaryData.value[index][key];
      return !!fileType && ["image", "audio", "video", "text", "json", "pdf", "html"].includes(fileType);
    }
    function isDownloadable(index, key) {
      const { mimeType, fileName } = binaryData.value[index][key];
      return !!(mimeType && fileName);
    }
    async function downloadBinaryData(index, key) {
      const { id, data, fileName, fileExtension, mimeType } = binaryData.value[index][key];
      if (id) {
        const url = workflowsStore.getBinaryUrl(id, "download", fileName ?? "", mimeType);
        FileSaver_minExports.saveAs(url, [fileName, fileExtension].join("."));
        return;
      } else {
        const bufferString = "data:" + mimeType + ";base64," + data;
        const blob = await fetch(bufferString).then(async (d) => await d.blob());
        FileSaver_minExports.saveAs(blob, fileName);
      }
    }
    async function downloadJsonData() {
      const fileName = (node2.value?.name ?? "").replace(/[^\w\d]/g, "_");
      const blob = new Blob([JSON.stringify(rawInputData.value, null, 2)], {
        type: "application/json"
      });
      FileSaver_minExports.saveAs(blob, `${fileName}.json`);
    }
    function displayBinaryData(index, key) {
      const { data, mimeType } = binaryData.value[index][key];
      binaryDataDisplayVisible.value = true;
      binaryDataDisplayData.value = {
        node: node2.value?.name,
        runIndex: props.runIndex,
        outputIndex: currentOutputIndex.value,
        index,
        key,
        data,
        mimeType
      };
    }
    function getOutputName(outputIndex2) {
      if (node2.value === null) {
        return outputIndex2 + 1;
      }
      const outputs2 = getResolvedNodeOutputs();
      const outputConfiguration = outputs2?.[outputIndex2];
      if (outputConfiguration && isObject$1(outputConfiguration)) {
        return outputConfiguration?.displayName;
      }
      if (!nodeType.value?.outputNames || nodeType.value.outputNames.length <= outputIndex2) {
        return outputIndex2 + 1;
      }
      return nodeType.value.outputNames[outputIndex2];
    }
    function refreshDataSize() {
      showData.value = false;
      const jsonItems = inputDataPage.value.map((item) => item.json);
      const byteSize = new Blob([JSON.stringify(jsonItems)]).size;
      dataSize.value = byteSize;
      updateShowData();
    }
    function updateShowData() {
      showData.value = isSchemaView.value && dataSize.value < MAX_DISPLAY_DATA_SIZE_SCHEMA_VIEW || dataSize.value < MAX_DISPLAY_DATA_SIZE;
    }
    function onRunIndexChange(run) {
      emit("runChange", run);
    }
    function enableNode() {
      if (node2.value) {
        const updateInformation = {
          name: node2.value.name,
          properties: {
            disabled: !node2.value.disabled
          }
        };
        workflowsStore.updateNodeProperties(updateInformation);
      }
    }
    function setDisplayMode() {
      if (!activeNode.value) return;
      const shouldDisplayHtml = activeNode.value.type === HTML_NODE_TYPE && activeNode.value.parameters.operation === "generateHtmlTemplate";
      if (shouldDisplayHtml) {
        ndvStore.setPanelDisplayMode({
          pane: "output",
          mode: "html"
        });
      }
    }
    function activatePane() {
      emit("activatePane");
    }
    function onSearchClear() {
      search2.value = "";
      document.dispatchEvent(new KeyboardEvent("keyup", { key: "/" }));
    }
    __expose({ enterEditMode });
    return (_ctx, _cache) => {
      const _component_i18n_t = resolveComponent("i18n-t");
      const _component_el_pagination = resolveComponent("el-pagination");
      const _directive_n8n_html = resolveDirective("n8n-html");
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["run-data", _ctx.$style.container]),
        onMouseover: activatePane
      }, [
        !isPaneTypeInput.value && unref(pinnedData).hasData.value && !editMode2.value.enabled && !_ctx.isProductionExecutionPreview ? (openBlock(), createBlock(unref(N8nCallout), {
          key: 0,
          theme: "secondary",
          icon: "thumbtack",
          class: normalizeClass(_ctx.$style.pinnedDataCallout),
          "data-test-id": "ndv-pinned-data-callout"
        }, {
          trailingContent: withCtx(() => [
            createVNode(unref(N8nLink), {
              to: unref(DATA_PINNING_DOCS_URL),
              size: "small",
              theme: "secondary",
              bold: "",
              underline: "",
              onClick: onClickDataPinningDocsLink
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("runData.pindata.learnMore")), 1)
              ]),
              _: 1
            }, 8, ["to"])
          ]),
          default: withCtx(() => [
            createTextVNode(toDisplayString(unref(i18n).baseText("runData.pindata.thisDataIsPinned")) + " ", 1),
            !isReadOnlyRoute.value && !readOnlyEnv.value ? (openBlock(), createElementBlock("span", _hoisted_1$4, [
              createVNode(unref(N8nLink), {
                theme: "secondary",
                size: "small",
                underline: "",
                bold: "",
                "data-test-id": "ndv-unpin-data",
                onClick: _cache[0] || (_cache[0] = withModifiers(($event) => onTogglePinData({ source: "banner-link" }), ["stop"]))
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("runData.pindata.unpin")), 1)
                ]),
                _: 1
              })
            ])) : createCommentVNode("", true)
          ]),
          _: 1
        }, 8, ["class"])) : createCommentVNode("", true),
        binaryDataDisplayData.value ? (openBlock(), createBlock(_sfc_main$9, {
          key: 1,
          "window-visible": binaryDataDisplayVisible.value,
          "display-data": binaryDataDisplayData.value,
          onClose: closeBinaryDataDisplay
        }, null, 8, ["window-visible", "display-data"])) : createCommentVNode("", true),
        createBaseVNode("div", {
          class: normalizeClass(_ctx.$style.header)
        }, [
          renderSlot(_ctx.$slots, "header", {}, void 0, true),
          withDirectives(createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.displayModes),
            "data-test-id": "run-data-pane-header",
            onClick: _cache[4] || (_cache[4] = withModifiers(() => {
            }, ["stop"]))
          }, [
            (openBlock(), createBlock(Suspense, null, {
              default: withCtx(() => [
                showIOSearch.value ? (openBlock(), createBlock(unref(LazyRunDataSearch), {
                  key: 0,
                  modelValue: search2.value,
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => search2.value = $event),
                  class: normalizeClass(_ctx.$style.search),
                  "pane-type": _ctx.paneType,
                  "display-mode": displayMode.value,
                  "is-area-active": _ctx.isPaneActive,
                  onFocus: activatePane
                }, null, 8, ["modelValue", "class", "pane-type", "display-mode", "is-area-active"])) : createCommentVNode("", true)
              ]),
              _: 1
            })),
            withDirectives(createVNode(unref(N8nRadioButtons), {
              "model-value": displayMode.value,
              options: displayModes2.value,
              "data-test-id": "ndv-run-data-display-mode",
              "onUpdate:modelValue": onDisplayModeChange
            }, null, 8, ["model-value", "options"]), [
              [
                vShow,
                unref(hasPreviewSchema) || hasNodeRun.value && (inputData.value.length || binaryData.value.length || search2.value) && !editMode2.value.enabled
              ]
            ]),
            canPinData.value && !isReadOnlyRoute.value && !readOnlyEnv.value ? withDirectives((openBlock(), createBlock(unref(_sfc_main$o), {
              key: 0,
              title: unref(i18n).baseText("runData.editOutput"),
              circle: false,
              disabled: node2.value?.disabled,
              icon: "pencil-alt",
              type: "tertiary",
              "data-test-id": "ndv-edit-pinned-data",
              onClick: _cache[2] || (_cache[2] = ($event) => enterEditMode({ origin: "editIconButton" }))
            }, null, 8, ["title", "disabled"])), [
              [vShow, !editMode2.value.enabled]
            ]) : createCommentVNode("", true),
            showPinButton.value ? (openBlock(), createBlock(RunDataPinButton, {
              key: 1,
              disabled: pinButtonDisabled.value,
              "tooltip-contents-visibility": {
                binaryDataTooltipContent: !!binaryData.value?.length,
                pinDataDiscoveryTooltipContent: isControlledPinDataTooltip.value && pinDataDiscoveryTooltipVisible.value
              },
              "data-pinning-docs-url": unref(DATA_PINNING_DOCS_URL),
              "pinned-data": unref(pinnedData),
              onTogglePinData: _cache[3] || (_cache[3] = ($event) => onTogglePinData({ source: "pin-icon-click" }))
            }, null, 8, ["disabled", "tooltip-contents-visibility", "data-pinning-docs-url", "pinned-data"])) : createCommentVNode("", true),
            withDirectives(createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.editModeActions)
            }, [
              createVNode(unref(N8nButton), {
                type: "tertiary",
                label: unref(i18n).baseText("runData.editor.cancel"),
                onClick: onClickCancelEdit
              }, null, 8, ["label"]),
              createVNode(unref(N8nButton), {
                class: "ml-2xs",
                type: "primary",
                label: unref(i18n).baseText("runData.editor.save"),
                onClick: onClickSaveEdit
              }, null, 8, ["label"])
            ], 2), [
              [vShow, editMode2.value.enabled]
            ])
          ], 2), [
            [vShow, !hasRunError.value && !isTrimmedManualExecutionDataItem.value]
          ])
        ], 2),
        inputSelectLocation.value === "header" ? (openBlock(), createElementBlock("div", {
          key: 2,
          class: normalizeClass(_ctx.$style.inputSelect)
        }, [
          renderSlot(_ctx.$slots, "input-select", {}, void 0, true)
        ], 2)) : createCommentVNode("", true),
        maxRunIndex.value > 0 && !displaysMultipleNodes.value ? withDirectives((openBlock(), createElementBlock("div", {
          key: 3,
          class: normalizeClass(_ctx.$style.runSelector)
        }, [
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.runSelectorInner)
          }, [
            inputSelectLocation.value === "runs" ? renderSlot(_ctx.$slots, "input-select", { key: 0 }, void 0, true) : createCommentVNode("", true),
            createVNode(unref(N8nSelect), {
              "model-value": _ctx.runIndex,
              class: normalizeClass(_ctx.$style.runSelectorSelect),
              size: "small",
              teleported: "",
              "data-test-id": "run-selector",
              "onUpdate:modelValue": onRunIndexChange,
              onClick: _cache[5] || (_cache[5] = withModifiers(() => {
              }, ["stop"]))
            }, {
              prepend: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.run")), 1)
              ]),
              default: withCtx(() => [
                (openBlock(true), createElementBlock(Fragment, null, renderList(maxRunIndex.value + 1, (option) => {
                  return openBlock(), createBlock(unref(_sfc_main$p), {
                    key: option,
                    label: getRunLabel(option),
                    value: option - 1
                  }, null, 8, ["label", "value"]);
                }), 128))
              ]),
              _: 1
            }, 8, ["model-value", "class"]),
            _ctx.canLinkRuns ? (openBlock(), createBlock(unref(N8nTooltip), {
              key: 1,
              placement: "right"
            }, {
              content: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText(_ctx.linkedRuns ? "runData.unlinking.hint" : "runData.linking.hint")), 1)
              ]),
              default: withCtx(() => [
                createVNode(unref(_sfc_main$o), {
                  icon: _ctx.linkedRuns ? "unlink" : "link",
                  class: normalizeClass(["linkRun", _ctx.linkedRuns ? "linked" : ""]),
                  text: "",
                  type: "tertiary",
                  size: "small",
                  "data-test-id": "link-run",
                  onClick: toggleLinkRuns
                }, null, 8, ["icon", "class"])
              ]),
              _: 1
            })) : createCommentVNode("", true),
            renderSlot(_ctx.$slots, "run-info", {}, void 0, true)
          ], 2),
          activeTaskMetadata.value && !(_ctx.paneType === "input" && hasInputOverwrite.value) ? (openBlock(), createBlock(ViewSubExecution, {
            key: 0,
            "task-metadata": activeTaskMetadata.value,
            "display-mode": displayMode.value
          }, null, 8, ["task-metadata", "display-mode"])) : createCommentVNode("", true)
        ], 2)), [
          [vShow, !editMode2.value.enabled]
        ]) : createCommentVNode("", true),
        !displaysMultipleNodes.value ? renderSlot(_ctx.$slots, "before-data", { key: 4 }, void 0, true) : createCommentVNode("", true),
        props.calloutMessage ? (openBlock(), createElementBlock("div", {
          key: 5,
          class: normalizeClass(_ctx.$style.hintCallout)
        }, [
          createVNode(unref(N8nCallout), {
            theme: "info",
            "data-test-id": "run-data-callout"
          }, {
            default: withCtx(() => [
              withDirectives(createVNode(unref(N8nText), { size: "small" }, null, 512), [
                [_directive_n8n_html, props.calloutMessage]
              ])
            ]),
            _: 1
          })
        ], 2)) : createCommentVNode("", true),
        (openBlock(true), createElementBlock(Fragment, null, renderList(getNodeHints$1(), (hint) => {
          return openBlock(), createBlock(unref(N8nCallout), {
            key: hint.message,
            class: normalizeClass(_ctx.$style.hintCallout),
            theme: hint.type || "info",
            "data-test-id": "node-hint"
          }, {
            default: withCtx(() => [
              withDirectives(createVNode(unref(N8nText), { size: "small" }, null, 512), [
                [_directive_n8n_html, hint.message]
              ])
            ]),
            _: 2
          }, 1032, ["class", "theme"]);
        }), 128)),
        maxOutputIndex.value > 0 && branches.value.length > 1 && !displaysMultipleNodes.value ? (openBlock(), createElementBlock("div", {
          key: 6,
          class: normalizeClass(_ctx.$style.outputs),
          "data-test-id": "branches"
        }, [
          inputSelectLocation.value === "outputs" ? renderSlot(_ctx.$slots, "input-select", { key: 0 }, void 0, true) : createCommentVNode("", true),
          activeTaskMetadata.value && !(_ctx.paneType === "input" && hasInputOverwrite.value) ? (openBlock(), createBlock(ViewSubExecution, {
            key: 1,
            "task-metadata": activeTaskMetadata.value,
            "display-mode": displayMode.value
          }, null, 8, ["task-metadata", "display-mode"])) : createCommentVNode("", true),
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.tabs)
          }, [
            createVNode(unref(N8nTabs), {
              "model-value": currentOutputIndex.value,
              options: branches.value,
              "onUpdate:modelValue": onBranchChange
            }, null, 8, ["model-value", "options"])
          ], 2)
        ], 2)) : hasNodeRun.value && !isSearchInSchemaView.value && (dataCount.value > 0 && maxRunIndex.value === 0 || search2.value) && !isArtificialRecoveredEventItem.value && !displaysMultipleNodes.value ? withDirectives((openBlock(), createElementBlock("div", {
          key: 7,
          class: normalizeClass([_ctx.$style.itemsCount, { [_ctx.$style.muted]: _ctx.paneType === "input" && maxRunIndex.value === 0 }]),
          "data-test-id": "ndv-items-count"
        }, [
          inputSelectLocation.value === "items" ? renderSlot(_ctx.$slots, "input-select", { key: 0 }, void 0, true) : createCommentVNode("", true),
          search2.value ? (openBlock(), createBlock(unref(N8nText), {
            key: 1,
            class: normalizeClass(_ctx.$style.itemsText)
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(unref(i18n).baseText("ndv.search.items", {
                adjustToNumber: unfilteredDataCount.value,
                interpolate: { matched: dataCount.value, count: unfilteredDataCount.value }
              })), 1)
            ]),
            _: 1
          }, 8, ["class"])) : (openBlock(), createBlock(unref(N8nText), {
            key: 2,
            class: normalizeClass(_ctx.$style.itemsText)
          }, {
            default: withCtx(() => [
              createBaseVNode("span", null, toDisplayString(unref(i18n).baseText("ndv.output.items", {
                adjustToNumber: dataCount.value,
                interpolate: { count: dataCount.value }
              })), 1),
              activeTaskMetadata.value?.subExecutionsCount ? (openBlock(), createElementBlock("span", _hoisted_2$1, toDisplayString(unref(i18n).baseText("ndv.output.andSubExecutions", {
                adjustToNumber: activeTaskMetadata.value.subExecutionsCount,
                interpolate: { count: activeTaskMetadata.value.subExecutionsCount }
              })), 1)) : createCommentVNode("", true)
            ]),
            _: 1
          }, 8, ["class"])),
          activeTaskMetadata.value && !(_ctx.paneType === "input" && hasInputOverwrite.value) ? (openBlock(), createBlock(ViewSubExecution, {
            key: 3,
            "task-metadata": activeTaskMetadata.value,
            "display-mode": displayMode.value
          }, null, 8, ["task-metadata", "display-mode"])) : createCommentVNode("", true)
        ], 2)), [
          [vShow, !editMode2.value.enabled]
        ]) : createCommentVNode("", true),
        createBaseVNode("div", {
          ref_key: "dataContainerRef",
          ref: dataContainerRef,
          class: normalizeClass(_ctx.$style.dataContainer),
          "data-test-id": "ndv-data-container"
        }, [
          _ctx.isExecuting && !isWaitNodeWaiting.value ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: normalizeClass(_ctx.$style.center),
            "data-test-id": "ndv-executing"
          }, [
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.spinner)
            }, [
              createVNode(unref(_sfc_main$q), { type: "ring" })
            ], 2),
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(_ctx.executingMessage), 1)
              ]),
              _: 1
            })
          ], 2)) : editMode2.value.enabled ? (openBlock(), createElementBlock("div", {
            key: 1,
            class: normalizeClass(_ctx.$style.editMode)
          }, [
            createBaseVNode("div", {
              class: normalizeClass([_ctx.$style.editModeBody, "ignore-key-press-canvas"])
            }, [
              createVNode(JsonEditor, {
                "model-value": editMode2.value.value,
                "fill-parent": true,
                "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => unref(ndvStore).setOutputPanelEditModeValue($event))
              }, null, 8, ["model-value"])
            ], 2),
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.editModeFooter)
            }, [
              createVNode(unref(InfoTip), {
                bold: false,
                class: normalizeClass(_ctx.$style.editModeFooterInfotip)
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("runData.editor.copyDataInfo")) + " ", 1),
                  createVNode(unref(N8nLink), {
                    to: unref(DATA_EDITING_DOCS_URL),
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(i18n).baseText("generic.learnMore")), 1)
                    ]),
                    _: 1
                  }, 8, ["to"])
                ]),
                _: 1
              }, 8, ["class"])
            ], 2)
          ], 2)) : _ctx.paneType === "output" && hasSubworkflowExecutionError.value && subworkflowExecutionError.value ? (openBlock(), createElementBlock("div", {
            key: 2,
            class: normalizeClass(_ctx.$style.stretchVertically)
          }, [
            createVNode(_sfc_main$r, {
              error: subworkflowExecutionError.value,
              class: normalizeClass(_ctx.$style.errorDisplay)
            }, null, 8, ["error", "class"])
          ], 2)) : isWaitNodeWaiting.value ? (openBlock(), createElementBlock("div", {
            key: 3,
            class: normalizeClass(_ctx.$style.center)
          }, [
            renderSlot(_ctx.$slots, "node-waiting", {}, () => [
              _cache[9] || (_cache[9] = createTextVNode("xxx"))
            ], true)
          ], 2)) : !hasNodeRun.value && !(displaysMultipleNodes.value && (node2.value?.disabled || unref(hasPreviewSchema))) ? (openBlock(), createElementBlock("div", {
            key: 4,
            class: normalizeClass(_ctx.$style.center)
          }, [
            renderSlot(_ctx.$slots, "node-not-run", {}, void 0, true)
          ], 2)) : _ctx.paneType === "input" && !displaysMultipleNodes.value && node2.value?.disabled ? (openBlock(), createElementBlock("div", {
            key: 5,
            class: normalizeClass(_ctx.$style.center)
          }, [
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.disabled", { interpolate: { nodeName: node2.value.name } })) + " ", 1),
                createVNode(unref(N8nLink), { onClick: enableNode }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.disabled.cta")), 1)
                  ]),
                  _: 1
                })
              ]),
              _: 1
            })
          ], 2)) : isTrimmedManualExecutionDataItem.value && unref(uiStore).isProcessingExecutionResults ? (openBlock(), createElementBlock("div", {
            key: 6,
            class: normalizeClass(_ctx.$style.center)
          }, [
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.spinner)
            }, [
              createVNode(unref(_sfc_main$q), { type: "ring" })
            ], 2),
            createVNode(unref(N8nText), {
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("runData.trimmedData.loading")), 1)
              ]),
              _: 1
            })
          ], 2)) : isTrimmedManualExecutionDataItem.value ? (openBlock(), createElementBlock("div", {
            key: 7,
            class: normalizeClass(_ctx.$style.center)
          }, [
            createVNode(unref(N8nText), {
              bold: "",
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("runData.trimmedData.title")), 1)
              ]),
              _: 1
            }),
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("runData.trimmedData.message")), 1)
              ]),
              _: 1
            })
          ], 2)) : hasNodeRun.value && isArtificialRecoveredEventItem.value ? (openBlock(), createElementBlock("div", {
            key: 8,
            class: normalizeClass(_ctx.$style.center)
          }, [
            renderSlot(_ctx.$slots, "recovered-artificial-output-data", {}, void 0, true)
          ], 2)) : hasNodeRun.value && hasRunError.value ? (openBlock(), createElementBlock("div", {
            key: 9,
            class: normalizeClass(_ctx.$style.stretchVertically)
          }, [
            isPaneTypeInput.value ? (openBlock(), createBlock(unref(N8nText), {
              key: 0,
              class: normalizeClass(_ctx.$style.center),
              size: "large",
              tag: "p",
              bold: ""
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("nodeErrorView.inputPanel.previousNodeError.title", {
                  interpolate: { nodeName: node2.value?.name ?? "" }
                })), 1)
              ]),
              _: 1
            }, 8, ["class"])) : _ctx.$slots["content"] ? (openBlock(), createElementBlock("div", _hoisted_3$1, [
              workflowRunErrorAsNodeError.value ? (openBlock(), createBlock(_sfc_main$r, {
                key: 0,
                error: workflowRunErrorAsNodeError.value,
                class: normalizeClass(_ctx.$style.inlineError),
                compact: ""
              }, null, 8, ["error", "class"])) : createCommentVNode("", true),
              renderSlot(_ctx.$slots, "content", {}, void 0, true)
            ])) : workflowRunErrorAsNodeError.value ? (openBlock(), createBlock(_sfc_main$r, {
              key: 2,
              error: workflowRunErrorAsNodeError.value,
              class: normalizeClass(_ctx.$style.dataDisplay)
            }, null, 8, ["error", "class"])) : createCommentVNode("", true)
          ], 2)) : hasNodeRun.value && (!unfilteredDataCount.value || search2.value && !dataCount.value) && branches.value.length > 1 ? (openBlock(), createElementBlock("div", {
            key: 10,
            class: normalizeClass(_ctx.$style.center)
          }, [
            search2.value ? (openBlock(), createElementBlock("div", _hoisted_4$1, [
              createVNode(unref(N8nText), {
                tag: "h3",
                size: "large"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("ndv.search.noMatch.title")), 1)
                ]),
                _: 1
              }),
              createVNode(unref(N8nText), null, {
                default: withCtx(() => [
                  createVNode(_component_i18n_t, {
                    keypath: "ndv.search.noMatch.description",
                    tag: "span"
                  }, {
                    link: withCtx(() => [
                      createBaseVNode("a", {
                        href: "#",
                        onClick: onSearchClear
                      }, toDisplayString(unref(i18n).baseText("ndv.search.noMatch.description.link")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              })
            ])) : (openBlock(), createBlock(unref(N8nText), { key: 1 }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(_ctx.noDataInBranchMessage), 1)
              ]),
              _: 1
            }))
          ], 2)) : hasNodeRun.value && !inputData.value.length && !search2.value ? (openBlock(), createElementBlock("div", {
            key: 11,
            class: normalizeClass(_ctx.$style.center)
          }, [
            renderSlot(_ctx.$slots, "no-output-data", {}, () => [
              _cache[10] || (_cache[10] = createTextVNode("xxx"))
            ], true)
          ], 2)) : hasNodeRun.value && !showData.value ? (openBlock(), createElementBlock("div", {
            key: 12,
            "data-test-id": "ndv-data-size-warning",
            class: normalizeClass(_ctx.$style.center)
          }, [
            createVNode(unref(N8nText), {
              bold: true,
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(_ctx.tooMuchDataTitle), 1)
              ]),
              _: 1
            }),
            createVNode(unref(N8nText), {
              align: "center",
              tag: "div"
            }, {
              default: withCtx(() => [
                withDirectives(createBaseVNode("span", null, null, 512), [
                  [
                    _directive_n8n_html,
                    unref(i18n).baseText("ndv.output.tooMuchData.message", {
                      interpolate: { size: dataSizeInMB.value }
                    })
                  ]
                ])
              ]),
              _: 1
            }),
            createVNode(unref(N8nButton), {
              outline: "",
              label: unref(i18n).baseText("ndv.output.tooMuchData.showDataAnyway"),
              onClick: showTooMuchData
            }, null, 8, ["label"]),
            createVNode(unref(N8nButton), {
              size: "small",
              label: unref(i18n).baseText("runData.downloadBinaryData"),
              onClick: _cache[7] || (_cache[7] = ($event) => downloadJsonData())
            }, null, 8, ["label"])
          ], 2)) : hasNodeRun.value && _ctx.$slots["content"] ? renderSlot(_ctx.$slots, "content", { key: 13 }, void 0, true) : hasNodeRun.value && displayMode.value === "table" && binaryData.value.length > 0 && inputData.value.length === 1 && Object.keys(jsonData.value[0] || {}).length === 0 ? (openBlock(), createElementBlock("div", {
            key: 14,
            class: normalizeClass(_ctx.$style.center)
          }, [
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("runData.switchToBinary.info")) + " ", 1),
                createBaseVNode("a", { onClick: switchToBinary }, toDisplayString(unref(i18n).baseText("runData.switchToBinary.binary")), 1)
              ]),
              _: 1
            })
          ], 2)) : showIoSearchNoMatchContent.value ? (openBlock(), createElementBlock("div", {
            key: 15,
            class: normalizeClass(_ctx.$style.center)
          }, [
            createVNode(unref(N8nText), {
              tag: "h3",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.search.noMatch.title")), 1)
              ]),
              _: 1
            }),
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createVNode(_component_i18n_t, {
                  keypath: "ndv.search.noMatch.description",
                  tag: "span"
                }, {
                  link: withCtx(() => [
                    createBaseVNode("a", {
                      href: "#",
                      onClick: onSearchClear
                    }, toDisplayString(unref(i18n).baseText("ndv.search.noMatch.description.link")), 1)
                  ]),
                  _: 1
                })
              ]),
              _: 1
            })
          ], 2)) : hasNodeRun.value && displayMode.value === "table" && node2.value ? (openBlock(), createBlock(Suspense, { key: 16 }, {
            default: withCtx(() => [
              createVNode(unref(LazyRunDataTable), {
                node: node2.value,
                "input-data": inputDataPage.value,
                "mapping-enabled": _ctx.mappingEnabled,
                "distance-from-active": _ctx.distanceFromActive,
                "run-index": _ctx.runIndex,
                "page-offset": currentPageOffset.value,
                "total-runs": maxRunIndex.value,
                "has-default-hover-state": _ctx.paneType === "input" && !search2.value,
                search: search2.value,
                onMounted: _cache[8] || (_cache[8] = ($event) => emit("tableMounted", $event)),
                onActiveRowChanged: onItemHover,
                onDisplayModeChange
              }, null, 8, ["node", "input-data", "mapping-enabled", "distance-from-active", "run-index", "page-offset", "total-runs", "has-default-hover-state", "search"])
            ]),
            _: 1
          })) : hasNodeRun.value && displayMode.value === "json" && node2.value ? (openBlock(), createBlock(Suspense, { key: 17 }, {
            default: withCtx(() => [
              createVNode(unref(LazyRunDataJson), {
                "pane-type": _ctx.paneType,
                "edit-mode": editMode2.value,
                "push-ref": _ctx.pushRef,
                node: node2.value,
                "input-data": inputDataPage.value,
                "mapping-enabled": _ctx.mappingEnabled,
                "distance-from-active": _ctx.distanceFromActive,
                "run-index": _ctx.runIndex,
                "output-index": currentOutputIndex.value,
                "total-runs": maxRunIndex.value,
                search: search2.value
              }, null, 8, ["pane-type", "edit-mode", "push-ref", "node", "input-data", "mapping-enabled", "distance-from-active", "run-index", "output-index", "total-runs", "search"])
            ]),
            _: 1
          })) : hasNodeRun.value && isPaneTypeOutput.value && displayMode.value === "html" ? (openBlock(), createBlock(Suspense, { key: 18 }, {
            default: withCtx(() => [
              createVNode(unref(LazyRunDataHtml), { "input-html": inputHtml.value }, null, 8, ["input-html"])
            ]),
            _: 1
          })) : (hasNodeRun.value || unref(hasPreviewSchema)) && isSchemaView.value ? (openBlock(), createBlock(Suspense, { key: 19 }, {
            default: withCtx(() => [
              createVNode(unref(LazyRunDataSchema), {
                nodes: _ctx.nodes,
                "mapping-enabled": _ctx.mappingEnabled,
                node: node2.value,
                data: jsonData.value,
                "pane-type": _ctx.paneType,
                "connection-type": connectionType2.value,
                "run-index": _ctx.runIndex,
                "output-index": currentOutputIndex.value,
                "total-runs": maxRunIndex.value,
                search: search2.value,
                class: normalizeClass(_ctx.$style.schema),
                "onClear:search": onSearchClear
              }, null, 8, ["nodes", "mapping-enabled", "node", "data", "pane-type", "connection-type", "run-index", "output-index", "total-runs", "search", "class"])
            ]),
            _: 1
          })) : displayMode.value === "binary" && binaryData.value.length === 0 ? (openBlock(), createElementBlock("div", {
            key: 20,
            class: normalizeClass(_ctx.$style.center)
          }, [
            createVNode(unref(N8nText), {
              align: "center",
              tag: "div"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("runData.noBinaryDataFound")), 1)
              ]),
              _: 1
            })
          ], 2)) : displayMode.value === "binary" ? (openBlock(), createElementBlock("div", {
            key: 21,
            class: normalizeClass(_ctx.$style.dataDisplay)
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(binaryData.value, (binaryDataEntry, index) => {
              return openBlock(), createElementBlock("div", { key: index }, [
                binaryData.value.length > 1 ? (openBlock(), createElementBlock("div", {
                  key: 0,
                  class: normalizeClass(_ctx.$style.binaryIndex)
                }, [
                  createBaseVNode("div", null, toDisplayString(index + 1), 1)
                ], 2)) : createCommentVNode("", true),
                createBaseVNode("div", {
                  class: normalizeClass(_ctx.$style.binaryRow)
                }, [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(binaryDataEntry, (binaryData2, key) => {
                    return openBlock(), createElementBlock("div", {
                      key: index + "_" + key,
                      class: normalizeClass(_ctx.$style.binaryCell)
                    }, [
                      createBaseVNode("div", {
                        "data-test-id": "ndv-binary-data_" + index
                      }, [
                        createBaseVNode("div", {
                          class: normalizeClass(_ctx.$style.binaryHeader)
                        }, toDisplayString(key), 3),
                        binaryData2.fileName ? (openBlock(), createElementBlock("div", _hoisted_6$1, [
                          createBaseVNode("div", null, [
                            createVNode(unref(N8nText), {
                              size: "small",
                              bold: true
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(i18n).baseText("runData.fileName")) + ": ", 1)
                              ]),
                              _: 1
                            })
                          ]),
                          createBaseVNode("div", {
                            class: normalizeClass(_ctx.$style.binaryValue)
                          }, toDisplayString(binaryData2.fileName), 3)
                        ])) : createCommentVNode("", true),
                        binaryData2.directory ? (openBlock(), createElementBlock("div", _hoisted_7$1, [
                          createBaseVNode("div", null, [
                            createVNode(unref(N8nText), {
                              size: "small",
                              bold: true
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(i18n).baseText("runData.directory")) + ": ", 1)
                              ]),
                              _: 1
                            })
                          ]),
                          createBaseVNode("div", {
                            class: normalizeClass(_ctx.$style.binaryValue)
                          }, toDisplayString(binaryData2.directory), 3)
                        ])) : createCommentVNode("", true),
                        binaryData2.fileExtension ? (openBlock(), createElementBlock("div", _hoisted_8$1, [
                          createBaseVNode("div", null, [
                            createVNode(unref(N8nText), {
                              size: "small",
                              bold: true
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(i18n).baseText("runData.fileExtension")) + ":", 1)
                              ]),
                              _: 1
                            })
                          ]),
                          createBaseVNode("div", {
                            class: normalizeClass(_ctx.$style.binaryValue)
                          }, toDisplayString(binaryData2.fileExtension), 3)
                        ])) : createCommentVNode("", true),
                        binaryData2.mimeType ? (openBlock(), createElementBlock("div", _hoisted_9, [
                          createBaseVNode("div", null, [
                            createVNode(unref(N8nText), {
                              size: "small",
                              bold: true
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(i18n).baseText("runData.mimeType")) + ": ", 1)
                              ]),
                              _: 1
                            })
                          ]),
                          createBaseVNode("div", {
                            class: normalizeClass(_ctx.$style.binaryValue)
                          }, toDisplayString(binaryData2.mimeType), 3)
                        ])) : createCommentVNode("", true),
                        binaryData2.fileSize ? (openBlock(), createElementBlock("div", _hoisted_10, [
                          createBaseVNode("div", null, [
                            createVNode(unref(N8nText), {
                              size: "small",
                              bold: true
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(unref(i18n).baseText("runData.fileSize")) + ": ", 1)
                              ]),
                              _: 1
                            })
                          ]),
                          createBaseVNode("div", {
                            class: normalizeClass(_ctx.$style.binaryValue)
                          }, toDisplayString(binaryData2.fileSize), 3)
                        ])) : createCommentVNode("", true),
                        createBaseVNode("div", {
                          class: normalizeClass(_ctx.$style.binaryButtonContainer)
                        }, [
                          isViewable(index, key) ? (openBlock(), createBlock(unref(N8nButton), {
                            key: 0,
                            size: "small",
                            label: unref(i18n).baseText("runData.showBinaryData"),
                            "data-test-id": "ndv-view-binary-data",
                            onClick: ($event) => displayBinaryData(index, key)
                          }, null, 8, ["label", "onClick"])) : createCommentVNode("", true),
                          isDownloadable(index, key) ? (openBlock(), createBlock(unref(N8nButton), {
                            key: 1,
                            size: "small",
                            type: "secondary",
                            label: unref(i18n).baseText("runData.downloadBinaryData"),
                            "data-test-id": "ndv-download-binary-data",
                            onClick: ($event) => downloadBinaryData(index, key)
                          }, null, 8, ["label", "onClick"])) : createCommentVNode("", true)
                        ], 2)
                      ], 8, _hoisted_5$1)
                    ], 2);
                  }), 128))
                ], 2)
              ]);
            }), 128))
          ], 2)) : createCommentVNode("", true)
        ], 2),
        _ctx.hidePagination === false && hasNodeRun.value && !hasRunError.value && displayMode.value !== "binary" && dataCount.value > pageSize.value && !isSchemaView.value && !isArtificialRecoveredEventItem.value ? withDirectives((openBlock(), createElementBlock("div", {
          key: 8,
          class: normalizeClass(_ctx.$style.pagination),
          "data-test-id": "ndv-data-pagination"
        }, [
          createVNode(_component_el_pagination, {
            background: "",
            "hide-on-single-page": true,
            "current-page": currentPage.value,
            "pager-count": 5,
            "page-size": pageSize.value,
            layout: "prev, pager, next",
            total: dataCount.value,
            "onUpdate:currentPage": onCurrentPageChange
          }, null, 8, ["current-page", "page-size", "total"]),
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.pageSizeSelector)
          }, [
            createVNode(unref(N8nSelect), {
              size: "mini",
              "model-value": pageSize.value,
              teleported: "",
              "onUpdate:modelValue": onPageSizeChange
            }, {
              prepend: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.pageSize")), 1)
              ]),
              default: withCtx(() => [
                (openBlock(), createElementBlock(Fragment, null, renderList(pageSizes, (size) => {
                  return createVNode(unref(_sfc_main$p), {
                    key: size,
                    label: size,
                    value: size
                  }, null, 8, ["label", "value"]);
                }), 64)),
                createVNode(unref(_sfc_main$p), {
                  label: unref(i18n).baseText("ndv.output.all"),
                  value: dataCount.value
                }, null, 8, ["label", "value"])
              ]),
              _: 1
            }, 8, ["model-value"])
          ], 2)
        ], 2)), [
          [vShow, !editMode2.value.enabled]
        ]) : createCommentVNode("", true),
        createVNode(unref(N8nBlockUi), {
          show: _ctx.blockUI,
          class: normalizeClass(_ctx.$style.uiBlocker)
        }, null, 8, ["show", "class"])
      ], 34);
    };
  }
});
const infoIcon = "_infoIcon_1vmxg_123";
const center = "_center_1vmxg_127";
const container$1 = "_container_1vmxg_141";
const pinnedDataCallout = "_pinnedDataCallout_1vmxg_150";
const header$1 = "_header_1vmxg_158";
const dataContainer = "_dataContainer_1vmxg_173";
const dataDisplay = "_dataDisplay_1vmxg_182";
const inlineError = "_inlineError_1vmxg_194";
const outputs = "_outputs_1vmxg_201";
const tabs = "_tabs_1vmxg_210";
const itemsCount = "_itemsCount_1vmxg_218";
const itemsText = "_itemsText_1vmxg_227";
const muted = "_muted_1vmxg_233";
const inputSelect = "_inputSelect_1vmxg_238";
const runSelector = "_runSelector_1vmxg_244";
const runSelectorInner = "_runSelectorInner_1vmxg_257";
const runSelectorSelect = "_runSelectorSelect_1vmxg_263";
const search = "_search_1vmxg_267";
const pagination = "_pagination_1vmxg_271";
const pageSizeSelector = "_pageSizeSelector_1vmxg_281";
const binaryIndex = "_binaryIndex_1vmxg_287";
const binaryRow = "_binaryRow_1vmxg_304";
const binaryCell = "_binaryCell_1vmxg_309";
const binaryHeader = "_binaryHeader_1vmxg_321";
const binaryButtonContainer = "_binaryButtonContainer_1vmxg_330";
const binaryValue = "_binaryValue_1vmxg_341";
const displayModes = "_displayModes_1vmxg_346";
const tooltipContain = "_tooltipContain_1vmxg_353";
const spinner = "_spinner_1vmxg_357";
const editMode = "_editMode_1vmxg_368";
const editModeBody = "_editModeBody_1vmxg_377";
const editModeFooter = "_editModeFooter_1vmxg_384";
const editModeFooterInfotip = "_editModeFooterInfotip_1vmxg_394";
const editModeActions = "_editModeActions_1vmxg_400";
const stretchVertically = "_stretchVertically_1vmxg_407";
const uiBlocker = "_uiBlocker_1vmxg_411";
const hintCallout = "_hintCallout_1vmxg_416";
const schema = "_schema_1vmxg_422";
const style0$5 = {
  infoIcon,
  center,
  container: container$1,
  pinnedDataCallout,
  header: header$1,
  dataContainer,
  "actions-group": "_actions-group_1vmxg_178",
  dataDisplay,
  inlineError,
  outputs,
  tabs,
  itemsCount,
  itemsText,
  muted,
  inputSelect,
  runSelector,
  runSelectorInner,
  runSelectorSelect,
  search,
  pagination,
  pageSizeSelector,
  binaryIndex,
  binaryRow,
  binaryCell,
  binaryHeader,
  binaryButtonContainer,
  binaryValue,
  displayModes,
  tooltipContain,
  spinner,
  editMode,
  editModeBody,
  editModeFooter,
  editModeFooterInfotip,
  editModeActions,
  stretchVertically,
  uiBlocker,
  hintCallout,
  schema
};
const cssModules$6 = {
  "$style": style0$5
};
const RunData = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["__cssModules", cssModules$6], ["__scopeId", "data-v-7bd2e155"]]);
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "RunInfo",
  props: {
    taskData: {},
    hasStaleData: { type: Boolean },
    hasPinData: { type: Boolean }
  },
  setup(__props) {
    const i18n = useI18n();
    const props = __props;
    const runTaskData = computed(() => {
      return props.taskData;
    });
    const theme = computed(() => {
      return props.taskData?.error ? "danger" : "success";
    });
    const runMetadata = computed(() => {
      if (!runTaskData.value) {
        return null;
      }
      const { date, time } = convertToDisplayDateComponents(runTaskData.value.startTime);
      return {
        executionTime: runTaskData.value.executionTime,
        startTime: `${date} at ${time}`
      };
    });
    return (_ctx, _cache) => {
      const _component_n8n_text = resolveComponent("n8n-text");
      const _directive_n8n_html = resolveDirective("n8n-html");
      return _ctx.hasStaleData ? (openBlock(), createBlock(unref(InfoTip), {
        key: 0,
        theme: "warning-light",
        type: "tooltip",
        "tooltip-placement": "right",
        "data-test-id": "node-run-info-stale"
      }, {
        default: withCtx(() => [
          withDirectives(createBaseVNode("span", null, null, 512), [
            [
              _directive_n8n_html,
              unref(i18n).baseText(
                _ctx.hasPinData ? "ndv.output.staleDataWarning.pinData" : "ndv.output.staleDataWarning.regular"
              )
            ]
          ])
        ]),
        _: 1
      })) : runMetadata.value ? (openBlock(), createElementBlock("div", {
        key: 1,
        class: normalizeClass(_ctx.$style.tooltipRow)
      }, [
        createVNode(unref(InfoTip), {
          type: "note",
          theme: theme.value,
          "data-test-id": `node-run-status-${theme.value}`
        }, null, 8, ["theme", "data-test-id"]),
        createVNode(unref(InfoTip), {
          type: "tooltip",
          theme: "info",
          "data-test-id": `node-run-info`,
          "tooltip-placement": "right"
        }, {
          default: withCtx(() => [
            createBaseVNode("div", null, [
              createVNode(_component_n8n_text, {
                bold: true,
                size: "small"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(runTaskData.value?.error ? unref(i18n).baseText("runData.executionStatus.failed") : unref(i18n).baseText("runData.executionStatus.success")), 1)
                ]),
                _: 1
              }),
              _cache[0] || (_cache[0] = createBaseVNode("br", null, null, -1)),
              createVNode(_component_n8n_text, {
                bold: true,
                size: "small"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("runData.startTime") + ":"), 1)
                ]),
                _: 1
              }),
              createTextVNode(" " + toDisplayString(runMetadata.value.startTime), 1),
              _cache[1] || (_cache[1] = createBaseVNode("br", null, null, -1)),
              createVNode(_component_n8n_text, {
                bold: true,
                size: "small"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("runData.executionTime") + ":"), 1)
                ]),
                _: 1
              }),
              createTextVNode(" " + toDisplayString(runMetadata.value.executionTime) + " " + toDisplayString(unref(i18n).baseText("runData.ms")), 1)
            ])
          ]),
          _: 1
        })
      ], 2)) : createCommentVNode("", true);
    };
  }
});
const tooltipRow = "_tooltipRow_14r7d_123";
const style0$4 = {
  tooltipRow
};
const cssModules$5 = {
  "$style": style0$4
};
const RunInfo = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["__cssModules", cssModules$5]]);
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "OutputPanel",
  props: {
    workflow: {},
    runIndex: {},
    isReadOnly: { type: Boolean },
    linkedRuns: { type: Boolean },
    canLinkRuns: { type: Boolean },
    pushRef: {},
    blockUI: { type: Boolean, default: false },
    isProductionExecutionPreview: { type: Boolean, default: false },
    isPaneActive: { type: Boolean, default: false }
  },
  emits: ["linkRun", "unlinkRun", "runChange", "activatePane", "tableMounted", "itemHover", "search", "openSettings"],
  setup(__props, { emit: __emit }) {
    const OUTPUT_TYPE = {
      REGULAR: "regular",
      LOGS: "logs"
    };
    const props = __props;
    const emit = __emit;
    const ndvStore = useNDVStore();
    const nodeTypesStore = useNodeTypesStore();
    const workflowsStore = useWorkflowsStore();
    const uiStore = useUIStore();
    const telemetry = useTelemetry();
    const i18n = useI18n();
    const { activeNode } = storeToRefs(ndvStore);
    const settings = useSettingsStore();
    const { dirtinessByName } = useNodeDirtiness();
    const { isSubNodeType } = useNodeType({
      node: activeNode
    });
    const pinnedData = usePinnedData(activeNode, {
      runIndex: props.runIndex,
      displayMode: ndvStore.outputPanelDisplayMode
    });
    const outputMode = ref(OUTPUT_TYPE.REGULAR);
    const outputTypes = ref([
      { label: i18n.baseText("ndv.output.outType.regular"), value: OUTPUT_TYPE.REGULAR },
      { label: i18n.baseText("ndv.output.outType.logs"), value: OUTPUT_TYPE.LOGS }
    ]);
    const runDataRef = ref();
    const node2 = computed(() => {
      return ndvStore.activeNode ?? void 0;
    });
    const isTriggerNode = computed(() => {
      return !!node2.value && nodeTypesStore.isTriggerNode(node2.value.type);
    });
    const hasAiMetadata = computed(() => {
      if (isNodeRunning.value || !workflowRunData.value) {
        return false;
      }
      if (node2.value) {
        const connectedSubNodes = props.workflow.getParentNodes(node2.value.name, "ALL_NON_MAIN");
        const resultData = connectedSubNodes.map(workflowsStore.getWorkflowResultDataByNodeName);
        return resultData && Array.isArray(resultData) && resultData.length > 0;
      }
      return false;
    });
    const hasError = computed(
      () => Boolean(
        workflowRunData.value && node2.value && workflowRunData.value[node2.value.name]?.[props.runIndex]?.error
      )
    );
    const defaultOutputMode = computed(() => {
      return hasError.value && hasAiMetadata.value ? OUTPUT_TYPE.LOGS : OUTPUT_TYPE.REGULAR;
    });
    const isNodeRunning = computed(() => {
      return workflowRunning.value && !!node2.value && workflowsStore.isNodeExecuting(node2.value.name);
    });
    const workflowRunning = computed(() => uiStore.isActionActive.workflowRunning);
    const workflowExecution = computed(() => {
      return workflowsStore.getWorkflowExecution;
    });
    const workflowRunData = computed(() => {
      if (workflowExecution.value === null) {
        return null;
      }
      const executionData = workflowExecution.value.data;
      if (!executionData?.resultData?.runData) {
        return null;
      }
      return executionData.resultData.runData;
    });
    const hasNodeRun = computed(() => {
      if (workflowsStore.subWorkflowExecutionError) return true;
      return Boolean(
        node2.value && workflowRunData.value && workflowRunData.value.hasOwnProperty(node2.value.name)
      );
    });
    const runTaskData = computed(() => {
      if (!node2.value || workflowExecution.value === null) {
        return null;
      }
      const runData = workflowRunData.value;
      if (runData === null || !runData.hasOwnProperty(node2.value.name)) {
        return null;
      }
      if (runData[node2.value.name].length <= props.runIndex) {
        return null;
      }
      return runData[node2.value.name][props.runIndex];
    });
    const runsCount = computed(() => {
      if (node2.value === null) {
        return 0;
      }
      const runData = workflowRunData.value;
      if (runData === null || node2.value && !runData.hasOwnProperty(node2.value.name)) {
        return 0;
      }
      if (node2.value && runData[node2.value.name].length) {
        return runData[node2.value.name].length;
      }
      return 0;
    });
    const staleData = computed(() => {
      if (!node2.value) {
        return false;
      }
      if (settings.partialExecutionVersion === 2) {
        return dirtinessByName.value[node2.value.name] === CanvasNodeDirtiness.PARAMETERS_UPDATED;
      }
      const updatedAt = workflowsStore.getParametersLastUpdate(node2.value.name);
      if (!updatedAt || !runTaskData.value) {
        return false;
      }
      const runAt = runTaskData.value.startTime;
      return updatedAt > runAt;
    });
    const outputPanelEditMode = computed(() => {
      return ndvStore.outputPanelEditMode;
    });
    const canPinData = computed(() => {
      return pinnedData.isValidNodeType.value && !props.isReadOnly;
    });
    const allToolsWereUnusedNotice = computed(() => {
      if (!node2.value || runsCount.value === 0 || hasError.value) return void 0;
      if (pinnedData.hasData.value) return void 0;
      const toolsAvailable = props.workflow.getParentNodes(
        node2.value.name,
        NodeConnectionTypes.AiTool,
        1
      );
      const toolsUsedInLatestRun = toolsAvailable.filter(
        (tool) => !!workflowRunData.value?.[tool]?.[props.runIndex]
      );
      if (toolsAvailable.length > 0 && toolsUsedInLatestRun.length === 0) {
        return i18n.baseText("ndv.output.noToolUsedInfo");
      } else {
        return void 0;
      }
    });
    const insertTestData = () => {
      if (!runDataRef.value) return;
      runDataRef.value.enterEditMode({
        origin: "insertTestDataLink"
      });
      telemetry.track("User clicked ndv link", {
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        node_type: node2.value?.type,
        pane: "output",
        type: "insert-test-data"
      });
    };
    const onLinkRun = () => {
      emit("linkRun");
    };
    const onUnlinkRun = () => {
      emit("unlinkRun");
    };
    const openSettings = () => {
      emit("openSettings");
      telemetry.track("User clicked ndv link", {
        node_type: node2.value?.type,
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        pane: "output",
        type: "settings"
      });
    };
    const onRunIndexChange = (run) => {
      emit("runChange", run);
    };
    const onUpdateOutputMode = (newOutputMode) => {
      if (newOutputMode === OUTPUT_TYPE.LOGS) {
        ndvEventBus.emit("setPositionByName", "minLeft");
      } else {
        ndvEventBus.emit("setPositionByName", "initial");
      }
    };
    onMounted(() => {
      outputMode.value = defaultOutputMode.value;
    });
    watch(defaultOutputMode, (newValue, oldValue) => {
      if (newValue === OUTPUT_TYPE.LOGS && oldValue === OUTPUT_TYPE.REGULAR && hasNodeRun.value) {
        outputMode.value = defaultOutputMode.value;
      }
    });
    const activatePane = () => {
      emit("activatePane");
    };
    return (_ctx, _cache) => {
      const _directive_n8n_html = resolveDirective("n8n-html");
      return openBlock(), createBlock(RunData, {
        ref_key: "runDataRef",
        ref: runDataRef,
        node: node2.value,
        workflow: _ctx.workflow,
        "run-index": _ctx.runIndex,
        "linked-runs": _ctx.linkedRuns,
        "can-link-runs": _ctx.canLinkRuns,
        "too-much-data-title": unref(i18n).baseText("ndv.output.tooMuchData.title"),
        "no-data-in-branch-message": unref(i18n).baseText("ndv.output.noOutputDataInBranch"),
        "is-executing": isNodeRunning.value,
        "executing-message": unref(i18n).baseText("ndv.output.executing"),
        "push-ref": _ctx.pushRef,
        "block-u-i": _ctx.blockUI,
        "is-production-execution-preview": _ctx.isProductionExecutionPreview,
        "is-pane-active": _ctx.isPaneActive,
        "hide-pagination": outputMode.value === "logs",
        "pane-type": "output",
        "data-output-type": outputMode.value,
        "callout-message": allToolsWereUnusedNotice.value,
        onActivatePane: activatePane,
        onRunChange: onRunIndexChange,
        onLinkRun,
        onUnlinkRun,
        onTableMounted: _cache[1] || (_cache[1] = ($event) => emit("tableMounted", $event)),
        onItemHover: _cache[2] || (_cache[2] = ($event) => emit("itemHover", $event)),
        onSearch: _cache[3] || (_cache[3] = ($event) => emit("search", $event))
      }, createSlots({
        header: withCtx(() => [
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.titleSection)
          }, [
            hasAiMetadata.value ? (openBlock(), createBlock(unref(N8nRadioButtons), {
              key: 0,
              modelValue: outputMode.value,
              "onUpdate:modelValue": [
                _cache[0] || (_cache[0] = ($event) => outputMode.value = $event),
                onUpdateOutputMode
              ],
              "data-test-id": "ai-output-mode-select",
              options: outputTypes.value
            }, null, 8, ["modelValue", "options"])) : (openBlock(), createElementBlock("span", {
              key: 1,
              class: normalizeClass(_ctx.$style.title)
            }, toDisplayString(unref(i18n).baseText(outputPanelEditMode.value.enabled ? "ndv.output.edit" : "ndv.output")), 3)),
            hasNodeRun.value && !unref(pinnedData).hasData.value && (runsCount.value === 1 || runsCount.value > 0 && staleData.value) ? withDirectives((openBlock(), createBlock(RunInfo, {
              key: 2,
              "task-data": runTaskData.value,
              "has-stale-data": staleData.value,
              "has-pin-data": unref(pinnedData).hasData.value
            }, null, 8, ["task-data", "has-stale-data", "has-pin-data"])), [
              [vShow, !outputPanelEditMode.value.enabled]
            ]) : createCommentVNode("", true)
          ], 2)
        ]),
        "node-not-run": withCtx(() => [
          workflowRunning.value && !isTriggerNode.value ? (openBlock(), createBlock(unref(N8nText), {
            key: 0,
            "data-test-id": "ndv-output-waiting"
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.waitingToRun")), 1)
            ]),
            _: 1
          })) : createCommentVNode("", true),
          !workflowRunning.value ? (openBlock(), createBlock(unref(N8nText), {
            key: 1,
            "data-test-id": "ndv-output-run-node-hint"
          }, {
            default: withCtx(() => [
              unref(isSubNodeType) ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.runNodeHintSubNode")), 1)
              ], 64)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.runNodeHint")) + " ", 1),
                canPinData.value ? (openBlock(), createElementBlock("span", {
                  key: 0,
                  onClick: insertTestData
                }, [
                  _cache[4] || (_cache[4] = createBaseVNode("br", null, null, -1)),
                  createTextVNode(" " + toDisplayString(unref(i18n).baseText("generic.or")) + " ", 1),
                  createVNode(unref(N8nText), {
                    tag: "a",
                    size: "medium",
                    color: "primary"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.insertTestData")), 1)
                    ]),
                    _: 1
                  })
                ])) : createCommentVNode("", true)
              ], 64))
            ]),
            _: 1
          })) : createCommentVNode("", true)
        ]),
        "node-waiting": withCtx(() => [
          createVNode(unref(N8nText), {
            bold: true,
            color: "text-dark",
            size: "large"
          }, {
            default: withCtx(() => _cache[5] || (_cache[5] = [
              createTextVNode("Waiting for input")
            ])),
            _: 1
          }),
          withDirectives(createVNode(unref(N8nText), null, null, 512), [
            [_directive_n8n_html, unref(waitingNodeTooltip)(node2.value)]
          ])
        ]),
        "no-output-data": withCtx(() => [
          createVNode(unref(N8nText), {
            bold: true,
            color: "text-dark",
            size: "large"
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.noOutputData.title")), 1)
            ]),
            _: 1
          }),
          createVNode(unref(N8nText), null, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(unref(i18n).baseText("ndv.output.noOutputData.message")) + " ", 1),
              createBaseVNode("a", { onClick: openSettings }, toDisplayString(unref(i18n).baseText("ndv.output.noOutputData.message.settings")), 1),
              createTextVNode(" " + toDisplayString(unref(i18n).baseText("ndv.output.noOutputData.message.settingsOption")), 1)
            ]),
            _: 1
          })
        ]),
        "recovered-artificial-output-data": withCtx(() => [
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.recoveredOutputData)
          }, [
            createVNode(unref(N8nText), {
              tag: "div",
              bold: true,
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("executionDetails.executionFailed.recoveredNodeTitle")), 1)
              ]),
              _: 1
            }),
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("executionDetails.executionFailed.recoveredNodeMessage")), 1)
              ]),
              _: 1
            })
          ], 2)
        ]),
        _: 2
      }, [
        outputMode.value === "logs" && node2.value ? {
          name: "content",
          fn: withCtx(() => [
            createVNode(RunDataAi, {
              node: node2.value,
              "run-index": _ctx.runIndex,
              workflow: _ctx.workflow
            }, null, 8, ["node", "run-index", "workflow"])
          ]),
          key: "0"
        } : void 0,
        !unref(pinnedData).hasData.value && runsCount.value > 1 ? {
          name: "run-info",
          fn: withCtx(() => [
            createVNode(RunInfo, { "task-data": runTaskData.value }, null, 8, ["task-data"])
          ]),
          key: "1"
        } : void 0
      ]), 1032, ["node", "workflow", "run-index", "linked-runs", "can-link-runs", "too-much-data-title", "no-data-in-branch-message", "is-executing", "executing-message", "push-ref", "block-u-i", "is-production-execution-preview", "is-pane-active", "hide-pagination", "data-output-type", "callout-message"]);
    };
  }
});
const outputTypeSelect = "_outputTypeSelect_1g8bw_128";
const titleSection$1 = "_titleSection_1g8bw_133";
const title$2 = "_title_1g8bw_133";
const noOutputData$1 = "_noOutputData_1g8bw_149";
const recoveredOutputData$1 = "_recoveredOutputData_1g8bw_159";
const style0$3 = {
  outputTypeSelect,
  titleSection: titleSection$1,
  title: title$2,
  noOutputData: noOutputData$1,
  recoveredOutputData: recoveredOutputData$1
};
const cssModules$4 = {
  "$style": style0$3
};
const OutputPanel = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["__cssModules", cssModules$4]]);
const _hoisted_1$3 = { key: 0 };
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "InputNodeSelect",
  props: {
    nodes: {},
    workflow: {},
    modelValue: {}
  },
  emits: ["update:model-value"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const i18n = useI18n();
    const workflowsStore = useWorkflowsStore();
    const nodeTypesStore = useNodeTypesStore();
    const ndvStore = useNDVStore();
    const selectedInputNode = computed(() => workflowsStore.getNodeByName(props.modelValue ?? ""));
    const selectedInputNodeType = computed(() => {
      const node2 = selectedInputNode.value;
      if (!node2) return null;
      return nodeTypesStore.getNodeType(node2.type, node2.typeVersion);
    });
    const inputNodes = computed(
      () => props.nodes.map((node2) => {
        const fullNode = workflowsStore.getNodeByName(node2.name);
        if (!fullNode) return null;
        return {
          node: fullNode,
          type: nodeTypesStore.getNodeType(fullNode.type, fullNode.typeVersion),
          depth: node2.depth
        };
      }).filter(isPresent)
    );
    const activeNode = computed(() => ndvStore.activeNode);
    const activeNodeType = computed(() => {
      const node2 = activeNode.value;
      if (!node2) return null;
      return nodeTypesStore.getNodeType(node2.type, node2.typeVersion);
    });
    const isMultiInputNode = computed(() => {
      const nodeType = activeNodeType.value;
      return nodeType !== null && nodeType.inputs.length > 1;
    });
    const connectedTo = (nodeName) => {
      const connections2 = ndvStore.ndvNodeInputNumber[nodeName];
      if (!connections2) return "";
      if (connections2.length === 1) {
        return `Input ${ndvStore.ndvNodeInputNumber[nodeName]}`;
      }
      return `Inputs ${ndvStore.ndvNodeInputNumber[nodeName].join(", ")}`;
    };
    function getMultipleNodesText(nodeName) {
      if (!nodeName || !isMultiInputNode.value || !activeNode.value || !activeNodeType.value?.inputNames)
        return "";
      const activeNodeConnections = props.workflow.connectionsByDestinationNode[activeNode.value.name].main || [];
      const connectedInputIndexes = activeNodeConnections.reduce((acc, node2, index) => {
        if (node2?.[0] && node2[0].node === nodeName) return [...acc, index];
        return acc;
      }, []);
      const connectedInputs = connectedInputIndexes.map(
        (inputIndex) => activeNodeType.value?.inputNames?.[inputIndex]
      );
      if (connectedInputs.length === 0) return "";
      return `(${connectedInputs.join(" & ")})`;
    }
    function title2(nodeName, length = 30) {
      return truncate(nodeName, length);
    }
    function subtitle2(nodeName, depth) {
      const multipleNodesText = getMultipleNodesText(nodeName);
      if (multipleNodesText) return multipleNodesText;
      return i18n.baseText("ndv.input.nodeDistance", { adjustToNumber: depth });
    }
    function onInputNodeChange(value) {
      emit("update:model-value", value);
    }
    return (_ctx, _cache) => {
      const _component_n8n_option = resolveComponent("n8n-option");
      const _component_n8n_select = resolveComponent("n8n-select");
      return openBlock(), createBlock(_component_n8n_select, {
        "model-value": _ctx.modelValue,
        "no-data-text": unref(i18n).baseText("ndv.input.noNodesFound"),
        placeholder: unref(i18n).baseText("ndv.input.parentNodes"),
        class: normalizeClass(_ctx.$style.select),
        teleported: "",
        size: "small",
        filterable: "",
        "data-test-id": "ndv-input-select",
        "onUpdate:modelValue": onInputNodeChange
      }, {
        prefix: withCtx(() => [
          createVNode(_sfc_main$l, {
            disabled: selectedInputNode.value?.disabled,
            "node-type": selectedInputNodeType.value,
            size: 14,
            shrink: false
          }, null, 8, ["disabled", "node-type"])
        ]),
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(inputNodes.value, ({ node: node2, type, depth }) => {
            return openBlock(), createBlock(_component_n8n_option, {
              key: node2.name,
              value: node2.name,
              class: normalizeClass([_ctx.$style.node, { [_ctx.$style.disabled]: node2.disabled }]),
              label: `${title2(node2.name)} ${getMultipleNodesText(node2.name)}`,
              "data-test-id": "ndv-input-option"
            }, {
              default: withCtx(() => [
                createVNode(_sfc_main$l, {
                  disabled: node2.disabled,
                  "node-type": type,
                  size: 14,
                  shrink: false,
                  class: normalizeClass(_ctx.$style.icon)
                }, null, 8, ["disabled", "node-type", "class"]),
                createBaseVNode("span", {
                  class: normalizeClass(_ctx.$style.title)
                }, [
                  createTextVNode(toDisplayString(title2(node2.name)) + " ", 1),
                  node2.disabled ? (openBlock(), createElementBlock("span", _hoisted_1$3, "(" + toDisplayString(unref(i18n).baseText("node.disabled")) + ")", 1)) : createCommentVNode("", true)
                ], 2),
                createBaseVNode("span", {
                  class: normalizeClass(_ctx.$style.subtitle)
                }, toDisplayString(connectedTo(node2.name) ? connectedTo(node2.name) : subtitle2(node2.name, depth)), 3)
              ]),
              _: 2
            }, 1032, ["value", "class", "label"]);
          }), 128))
        ]),
        _: 1
      }, 8, ["model-value", "no-data-text", "placeholder", "class"]);
    };
  }
});
const select = "_select_wv0ev_123";
const node = "_node_wv0ev_132";
const icon = "_icon_wv0ev_140";
const title$1 = "_title_wv0ev_144";
const disabled = "_disabled_wv0ev_153";
const subtitle = "_subtitle_wv0ev_157";
const style0$2 = {
  select,
  node,
  icon,
  title: title$1,
  disabled,
  subtitle
};
const cssModules$3 = {
  "$style": style0$2
};
const InputNodeSelect = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["__cssModules", cssModules$3]]);
const _sfc_main$3 = {};
const _hoisted_1$2 = {
  width: "112",
  height: "80",
  viewBox: "0 0 112 80",
  fill: "none",
  xmlns: "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink"
};
function _sfc_render(_ctx, _cache) {
  return openBlock(), createElementBlock("svg", _hoisted_1$2, _cache[0] || (_cache[0] = [
    createStaticVNode('<mask id="mask0_489_46042" style="mask-type:alpha;" maskUnits="userSpaceOnUse" x="0" y="0" width="112" height="80"><rect width="112" height="80" fill="url(#paint0_linear_489_46042)"></rect></mask><g mask="url(#mask0_489_46042)"><rect x="-0.5" width="112" height="80" fill="url(#pattern0)" fill-opacity="0.6"></rect></g><defs><pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_489_46042" transform="scale(0.00357143 0.005)"></use></pattern><linearGradient id="paint0_linear_489_46042" x1="90.5" y1="40.4494" x2="112.5" y2="40.4494" gradientUnits="userSpaceOnUse"><stop></stop><stop offset="1" stop-color="white" stop-opacity="0"></stop></linearGradient><image id="image0_489_46042" width="280" height="200" xlink:href="data:image/png;base64,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"></image></defs>', 3)
  ]));
}
const WireMeUp = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render]]);
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "InputPanel",
  props: {
    runIndex: {},
    workflow: {},
    pushRef: {},
    currentNodeName: { default: "" },
    canLinkRuns: { type: Boolean, default: false },
    linkedRuns: { type: Boolean },
    readOnly: { type: Boolean, default: false },
    isProductionExecutionPreview: { type: Boolean, default: false },
    isPaneActive: { type: Boolean, default: false }
  },
  emits: ["itemHover", "tableMounted", "linkRun", "unlinkRun", "runChange", "search", "changeInputNode", "execute", "activatePane"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const i18n = useI18n();
    const telemetry = useTelemetry();
    const showDraggableHintWithDelay = ref(false);
    const draggableHintShown = ref(false);
    const mappedNode2 = ref(null);
    const inputModes = [
      { value: "mapping", label: i18n.baseText("ndv.input.mapping") },
      { value: "debugging", label: i18n.baseText("ndv.input.debugging") }
    ];
    const nodeTypesStore = useNodeTypesStore();
    const ndvStore = useNDVStore();
    const workflowsStore = useWorkflowsStore();
    const uiStore = useUIStore();
    const {
      activeNode,
      focusedMappableInput,
      isMappingOnboarded: isUserOnboarded
    } = storeToRefs(ndvStore);
    const rootNode = computed(() => {
      if (!activeNode.value) return null;
      return props.workflow.getChildNodes(activeNode.value.name, "ALL").at(0) ?? null;
    });
    const hasRootNodeRun = computed(() => {
      return !!(rootNode.value && workflowsStore.getWorkflowExecution?.data?.resultData.runData[rootNode.value]);
    });
    const inputMode = ref(
      // Show debugging mode by default only when the node has already run
      activeNode.value && workflowsStore.getWorkflowExecution?.data?.resultData.runData[activeNode.value.name] ? "debugging" : "mapping"
    );
    const isMappingMode = computed(() => isActiveNodeConfig.value && inputMode.value === "mapping");
    const showDraggableHint = computed(() => {
      const toIgnore = [START_NODE_TYPE, MANUAL_TRIGGER_NODE_TYPE, CRON_NODE_TYPE, INTERVAL_NODE_TYPE];
      if (!currentNode.value || toIgnore.includes(currentNode.value.type)) {
        return false;
      }
      return !!focusedMappableInput.value && !isUserOnboarded.value;
    });
    const isActiveNodeConfig = computed(() => {
      let inputs = activeNodeType.value?.inputs ?? [];
      let outputs2 = activeNodeType.value?.outputs ?? [];
      if (props.workflow && activeNode.value) {
        const node2 = props.workflow.getNode(activeNode.value.name);
        if (node2 && activeNodeType.value) {
          inputs = getNodeInputs(props.workflow, node2, activeNodeType.value);
          outputs2 = getNodeOutputs(props.workflow, node2, activeNodeType.value);
        }
      }
      if (!Array.isArray(inputs)) {
        inputs = [];
      }
      if (!Array.isArray(outputs2)) {
        outputs2 = [];
      }
      return inputs.length === 0 || inputs.every((input) => filterOutConnectionType(input, NodeConnectionTypes.Main)) && outputs2.find((output) => filterOutConnectionType(output, NodeConnectionTypes.Main));
    });
    const isMappingEnabled = computed(() => {
      if (props.readOnly) return false;
      if (isActiveNodeConfig.value) return isMappingMode.value && mappedNode2.value !== null;
      return true;
    });
    const isExecutingPrevious = computed(() => {
      if (!workflowRunning.value) {
        return false;
      }
      const triggeredNode = workflowsStore.executedNode;
      const executingNode = workflowsStore.executingNode;
      if (activeNode.value && triggeredNode === activeNode.value.name && workflowsStore.isNodeExecuting(props.currentNodeName)) {
        return true;
      }
      if (executingNode.length || triggeredNode) {
        return !!parentNodes.value.find(
          (node2) => workflowsStore.isNodeExecuting(node2.name) || node2.name === triggeredNode
        );
      }
      return false;
    });
    const workflowRunning = computed(() => uiStore.isActionActive.workflowRunning);
    const rootNodesParents = computed(() => {
      if (!rootNode.value) return [];
      return props.workflow.getParentNodesByDepth(rootNode.value);
    });
    const currentNode = computed(() => {
      if (isActiveNodeConfig.value) {
        if (mappedNode2.value) {
          return workflowsStore.getNodeByName(mappedNode2.value);
        }
        return activeNode.value;
      }
      return workflowsStore.getNodeByName(props.currentNodeName ?? "");
    });
    const connectedCurrentNodeOutputs = computed(() => {
      const search2 = parentNodes.value.find(({ name }) => name === props.currentNodeName);
      return search2?.indicies;
    });
    const parentNodes = computed(() => {
      if (!activeNode.value) {
        return [];
      }
      const parents = props.workflow.getParentNodesByDepth(activeNode.value.name).filter((parent) => parent.name !== activeNode.value?.name);
      return uniqBy(parents, (parent) => parent.name);
    });
    const currentNodeDepth = computed(() => {
      const node2 = parentNodes.value.find(
        (parent) => currentNode.value && parent.name === currentNode.value.name
      );
      return node2?.depth ?? -1;
    });
    const activeNodeType = computed(() => {
      if (!activeNode.value) return null;
      return nodeTypesStore.getNodeType(activeNode.value.type, activeNode.value.typeVersion);
    });
    const waitingMessage = computed(() => {
      const parentNode = parentNodes.value[0];
      return parentNode && waitingNodeTooltip(workflowsStore.getNodeByName(parentNode.name));
    });
    watch(
      inputMode,
      (mode) => {
        onRunIndexChange(-1);
        if (mode === "mapping") {
          onUnlinkRun();
          mappedNode2.value = rootNodesParents.value[0]?.name ?? null;
        } else {
          mappedNode2.value = null;
        }
      },
      { immediate: true }
    );
    watch(showDraggableHint, (curr, prev) => {
      if (curr && !prev) {
        setTimeout(() => {
          if (draggableHintShown.value) {
            return;
          }
          showDraggableHintWithDelay.value = showDraggableHint.value;
          if (showDraggableHintWithDelay.value) {
            draggableHintShown.value = true;
            telemetry.track("User viewed data mapping tooltip", {
              type: "unexecuted input pane"
            });
          }
        }, 1e3);
      } else if (!curr) {
        showDraggableHintWithDelay.value = false;
      }
    });
    function filterOutConnectionType(item, type) {
      if (!item) return false;
      return typeof item === "string" ? item !== type : item.type !== type;
    }
    function onInputModeChange(val) {
      inputMode.value = val;
    }
    function onMappedNodeSelected(val) {
      mappedNode2.value = val;
      onRunIndexChange(0);
      onUnlinkRun();
    }
    function onNodeExecute() {
      emit("execute");
      if (activeNode.value) {
        telemetry.track("User clicked ndv button", {
          node_type: activeNode.value.type,
          workflow_id: workflowsStore.workflowId,
          push_ref: props.pushRef,
          pane: "input",
          type: "executePrevious"
        });
      }
    }
    function onRunIndexChange(run) {
      emit("runChange", run);
    }
    function onLinkRun() {
      emit("linkRun");
    }
    function onUnlinkRun() {
      emit("unlinkRun");
    }
    function onSearch(search2) {
      emit("search", search2);
    }
    function onItemHover(item) {
      emit("itemHover", item);
    }
    function onTableMounted(event) {
      emit("tableMounted", event);
    }
    function onInputNodeChange(value) {
      const index = parentNodes.value.findIndex((node2) => node2.name === value) + 1;
      emit("changeInputNode", value, index);
    }
    function onConnectionHelpClick() {
      if (activeNode.value) {
        telemetry.track("User clicked ndv link", {
          node_type: activeNode.value.type,
          workflow_id: workflowsStore.workflowId,
          push_ref: props.pushRef,
          pane: "input",
          type: "not-connected-help"
        });
      }
    }
    function activatePane() {
      emit("activatePane");
    }
    return (_ctx, _cache) => {
      const _component_i18n_t = resolveComponent("i18n-t");
      const _directive_n8n_html = resolveDirective("n8n-html");
      return openBlock(), createBlock(RunData, {
        node: currentNode.value,
        nodes: isMappingMode.value ? rootNodesParents.value : parentNodes.value,
        workflow: _ctx.workflow,
        "run-index": isMappingMode.value ? 0 : _ctx.runIndex,
        "linked-runs": _ctx.linkedRuns,
        "can-link-runs": !mappedNode2.value && _ctx.canLinkRuns,
        "too-much-data-title": unref(i18n).baseText("ndv.input.tooMuchData.title"),
        "no-data-in-branch-message": unref(i18n).baseText("ndv.input.noOutputDataInBranch"),
        "is-executing": isExecutingPrevious.value,
        "executing-message": unref(i18n).baseText("ndv.input.executingPrevious"),
        "push-ref": _ctx.pushRef,
        "override-outputs": connectedCurrentNodeOutputs.value,
        "mapping-enabled": isMappingEnabled.value,
        "distance-from-active": currentNodeDepth.value,
        "is-production-execution-preview": _ctx.isProductionExecutionPreview,
        "is-pane-active": _ctx.isPaneActive,
        "pane-type": "input",
        "data-test-id": "ndv-input-panel",
        onActivatePane: activatePane,
        onItemHover,
        onLinkRun,
        onUnlinkRun,
        onRunChange: onRunIndexChange,
        onTableMounted,
        onSearch
      }, createSlots({
        header: withCtx(() => [
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.titleSection)
          }, [
            createBaseVNode("span", {
              class: normalizeClass(_ctx.$style.title)
            }, toDisplayString(unref(i18n).baseText("ndv.input")), 3),
            isActiveNodeConfig.value && !_ctx.readOnly ? (openBlock(), createBlock(unref(N8nRadioButtons), {
              key: 0,
              "data-test-id": "input-panel-mode",
              options: inputModes,
              "model-value": inputMode.value,
              "onUpdate:modelValue": onInputModeChange
            }, null, 8, ["model-value"])) : createCommentVNode("", true)
          ], 2)
        ]),
        "input-select": withCtx(() => [
          parentNodes.value.length && _ctx.currentNodeName ? (openBlock(), createBlock(InputNodeSelect, {
            key: 0,
            "model-value": _ctx.currentNodeName,
            workflow: _ctx.workflow,
            nodes: parentNodes.value,
            "onUpdate:modelValue": onInputNodeChange
          }, null, 8, ["model-value", "workflow", "nodes"])) : createCommentVNode("", true)
        ]),
        "node-not-run": withCtx(() => [
          isActiveNodeConfig.value && rootNode.value || parentNodes.value.length ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: normalizeClass(_ctx.$style.noOutputData)
          }, [
            isMappingEnabled.value || hasRootNodeRun.value ? (openBlock(), createBlock(unref(N8nText), {
              key: 0,
              tag: "div",
              bold: true,
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.noOutputData.title")), 1)
              ]),
              _: 1
            })) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
              createVNode(unref(N8nText), {
                tag: "div",
                bold: true,
                color: "text-dark",
                size: "large"
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.rootNodeHasNotRun.title")), 1)
                ]),
                _: 1
              }),
              createVNode(unref(N8nText), {
                tag: "div",
                color: "text-dark",
                size: "medium"
              }, {
                default: withCtx(() => [
                  createVNode(_component_i18n_t, {
                    tag: "span",
                    keypath: "ndv.input.rootNodeHasNotRun.description"
                  }, {
                    link: withCtx(() => [
                      createBaseVNode("a", {
                        href: "#",
                        "data-test-id": "switch-to-mapping-mode-link",
                        onClick: _cache[0] || (_cache[0] = withModifiers(($event) => onInputModeChange("mapping"), ["prevent"]))
                      }, toDisplayString(unref(i18n).baseText("ndv.input.rootNodeHasNotRun.description.link")), 1)
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              })
            ], 64)),
            !_ctx.readOnly ? (openBlock(), createBlock(unref(N8nTooltip), {
              key: 2,
              visible: showDraggableHint.value && showDraggableHintWithDelay.value
            }, {
              content: withCtx(() => [
                withDirectives(createBaseVNode("div", null, null, 512), [
                  [
                    _directive_n8n_html,
                    unref(i18n).baseText("dataMapping.dragFromPreviousHint", {
                      interpolate: { name: unref(focusedMappableInput) }
                    })
                  ]
                ])
              ]),
              default: withCtx(() => [
                createVNode(_sfc_main$m, {
                  type: "secondary",
                  "hide-icon": "",
                  transparent: true,
                  "node-name": (isActiveNodeConfig.value ? rootNode.value : _ctx.currentNodeName) ?? "",
                  label: unref(i18n).baseText("ndv.input.noOutputData.executePrevious"),
                  class: "mt-m",
                  "telemetry-source": "inputs",
                  "data-test-id": "execute-previous-node",
                  onExecute: onNodeExecute
                }, null, 8, ["node-name", "label"])
              ]),
              _: 1
            }, 8, ["visible"])) : createCommentVNode("", true),
            !_ctx.readOnly ? (openBlock(), createBlock(unref(N8nText), {
              key: 3,
              tag: "div",
              size: "small"
            }, {
              default: withCtx(() => [
                createVNode(_component_i18n_t, { keypath: "ndv.input.noOutputData.hint" }, {
                  info: withCtx(() => [
                    createVNode(unref(N8nTooltip), { placement: "bottom" }, {
                      content: withCtx(() => [
                        createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.noOutputData.hint.tooltip")), 1)
                      ]),
                      default: withCtx(() => [
                        createVNode(unref(N8nIcon), { icon: "question-circle" })
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                })
              ]),
              _: 1
            })) : createCommentVNode("", true)
          ], 2)) : (openBlock(), createElementBlock("div", {
            key: 1,
            class: normalizeClass(_ctx.$style.notConnected)
          }, [
            createBaseVNode("div", null, [
              createVNode(WireMeUp)
            ]),
            createVNode(unref(N8nText), {
              tag: "div",
              bold: true,
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.notConnected.title")), 1)
              ]),
              _: 1
            }),
            createVNode(unref(N8nText), { tag: "div" }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.notConnected.message")) + " ", 1),
                createBaseVNode("a", {
                  href: "https://docs.n8n.io/workflows/connections/",
                  target: "_blank",
                  onClick: onConnectionHelpClick
                }, toDisplayString(unref(i18n).baseText("ndv.input.notConnected.learnMore")), 1)
              ]),
              _: 1
            })
          ], 2))
        ]),
        "node-waiting": withCtx(() => [
          createVNode(unref(N8nText), {
            bold: true,
            color: "text-dark",
            size: "large"
          }, {
            default: withCtx(() => _cache[2] || (_cache[2] = [
              createTextVNode("Waiting for input")
            ])),
            _: 1
          }),
          withDirectives(createVNode(unref(N8nText), null, null, 512), [
            [_directive_n8n_html, waitingMessage.value]
          ])
        ]),
        "no-output-data": withCtx(() => [
          createVNode(unref(N8nText), {
            tag: "div",
            bold: true,
            color: "text-dark",
            size: "large"
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(unref(i18n).baseText("ndv.input.noOutputData")), 1)
            ]),
            _: 1
          })
        ]),
        "recovered-artificial-output-data": withCtx(() => [
          createBaseVNode("div", {
            class: normalizeClass(_ctx.$style.recoveredOutputData)
          }, [
            createVNode(unref(N8nText), {
              tag: "div",
              bold: true,
              color: "text-dark",
              size: "large"
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("executionDetails.executionFailed.recoveredNodeTitle")), 1)
              ]),
              _: 1
            }),
            createVNode(unref(N8nText), null, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(unref(i18n).baseText("executionDetails.executionFailed.recoveredNodeMessage")), 1)
              ]),
              _: 1
            })
          ], 2)
        ]),
        _: 2
      }, [
        isMappingMode.value ? {
          name: "before-data",
          fn: withCtx(() => [
            (openBlock(), createBlock(resolveDynamicComponent("style"), null, {
              default: withCtx(() => _cache[1] || (_cache[1] = [
                createTextVNode("button.linkRun { display: none }")
              ])),
              _: 1
            })),
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.mappedNode)
            }, [
              createVNode(InputNodeSelect, {
                "model-value": mappedNode2.value,
                workflow: _ctx.workflow,
                nodes: rootNodesParents.value,
                "onUpdate:modelValue": onMappedNodeSelected
              }, null, 8, ["model-value", "workflow", "nodes"])
            ], 2)
          ]),
          key: "0"
        } : void 0
      ]), 1032, ["node", "nodes", "workflow", "run-index", "linked-runs", "can-link-runs", "too-much-data-title", "no-data-in-branch-message", "is-executing", "executing-message", "push-ref", "override-outputs", "mapping-enabled", "distance-from-active", "is-production-execution-preview", "is-pane-active"]);
    };
  }
});
const mappedNode = "_mappedNode_48j1a_123";
const titleSection = "_titleSection_48j1a_127";
const inputModeTab = "_inputModeTab_48j1a_136";
const noOutputData = "_noOutputData_48j1a_140";
const recoveredOutputData = "_recoveredOutputData_48j1a_147";
const notConnected = "_notConnected_48j1a_156";
const title = "_title_48j1a_127";
const style0$1 = {
  mappedNode,
  titleSection,
  inputModeTab,
  noOutputData,
  recoveredOutputData,
  notConnected,
  title
};
const cssModules$2 = {
  "$style": style0$1
};
const InputPanel = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__cssModules", cssModules$2]]);
const _hoisted_1$1 = { key: "empty" };
const _hoisted_2 = { key: "listening" };
const _hoisted_3 = { key: 0 };
const _hoisted_4 = { key: 1 };
const _hoisted_5 = { key: 0 };
const _hoisted_6 = { key: "default" };
const _hoisted_7 = {
  key: 0,
  class: "mb-xl"
};
const _hoisted_8 = ["textContent"];
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "TriggerPanel",
  props: {
    nodeName: {},
    pushRef: { default: "" }
  },
  emits: ["activate", "execute"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const nodesTypeStore = useNodeTypesStore();
    const uiStore = useUIStore();
    const workflowsStore = useWorkflowsStore();
    const ndvStore = useNDVStore();
    const router = useRouter();
    const workflowHelpers = useWorkflowHelpers({ router });
    const i18n = useI18n();
    const telemetry = useTelemetry();
    const executionsHelpEventBus = createEventBus();
    const help = ref(null);
    const node2 = computed(() => workflowsStore.getNodeByName(props.nodeName));
    const nodeType = computed(() => {
      if (node2.value) {
        return nodesTypeStore.getNodeType(node2.value.type, node2.value.typeVersion);
      }
      return null;
    });
    const triggerPanel = computed(() => {
      const panel = nodeType.value?.triggerPanel;
      if (isTriggerPanelObject(panel)) {
        return panel;
      }
      return void 0;
    });
    const hideContent = computed(() => {
      const hideContent2 = triggerPanel.value?.hideContent;
      if (typeof hideContent2 === "boolean") {
        return hideContent2;
      }
      if (node2.value) {
        const hideContentValue = workflowHelpers.getCurrentWorkflow().expression.getSimpleParameterValue(node2.value, hideContent2, "internal", {});
        if (typeof hideContentValue === "boolean") {
          return hideContentValue;
        }
      }
      return false;
    });
    const hasIssues2 = computed(() => {
      return Boolean(
        node2.value?.issues && (node2.value.issues.parameters ?? node2.value.issues.credentials)
      );
    });
    const serviceName = computed(() => {
      if (nodeType.value) {
        return getTriggerNodeServiceName(nodeType.value);
      }
      return "";
    });
    const displayChatButton = computed(() => {
      return Boolean(
        node2.value && node2.value.type === CHAT_TRIGGER_NODE_TYPE && node2.value.parameters.mode !== "webhook"
      );
    });
    const isWebhookNode = computed(() => {
      return Boolean(node2.value && node2.value.type === WEBHOOK_NODE_TYPE);
    });
    const webhookHttpMethod = computed(() => {
      if (!node2.value || !nodeType.value?.webhooks?.length) {
        return void 0;
      }
      const httpMethod = workflowHelpers.getWebhookExpressionValue(
        nodeType.value.webhooks[0],
        "httpMethod",
        false
      );
      if (Array.isArray(httpMethod)) {
        return httpMethod.join(", ");
      }
      return httpMethod;
    });
    const webhookTestUrl = computed(() => {
      if (!node2.value || !nodeType.value?.webhooks?.length) {
        return void 0;
      }
      return workflowHelpers.getWebhookUrl(nodeType.value.webhooks[0], node2.value, "test");
    });
    const isWebhookBasedNode = computed(() => {
      return Boolean(nodeType.value?.webhooks?.length);
    });
    const isPollingNode = computed(() => {
      return Boolean(nodeType.value?.polling);
    });
    const isListeningForEvents = computed(() => {
      const waitingOnWebhook = workflowsStore.executionWaitingForWebhook;
      const executedNode = workflowsStore.executedNode;
      return !!node2.value && !node2.value.disabled && isWebhookBasedNode.value && waitingOnWebhook && (!executedNode || executedNode === props.nodeName);
    });
    const workflowRunning = computed(() => {
      return uiStore.isActionActive.workflowRunning;
    });
    const isActivelyPolling = computed(() => {
      const triggeredNode = workflowsStore.executedNode;
      return workflowRunning.value && isPollingNode.value && props.nodeName === triggeredNode;
    });
    const isWorkflowActive = computed(() => {
      return workflowsStore.isWorkflowActive;
    });
    const listeningTitle = computed(() => {
      return nodeType.value?.name === FORM_TRIGGER_NODE_TYPE ? i18n.baseText("ndv.trigger.webhookNode.formTrigger.listening") : i18n.baseText("ndv.trigger.webhookNode.listening");
    });
    const listeningHint = computed(() => {
      switch (nodeType.value?.name) {
        case CHAT_TRIGGER_NODE_TYPE:
          return i18n.baseText("ndv.trigger.webhookBasedNode.chatTrigger.serviceHint");
        case FORM_TRIGGER_NODE_TYPE:
          return i18n.baseText("ndv.trigger.webhookBasedNode.formTrigger.serviceHint");
        default:
          return i18n.baseText("ndv.trigger.webhookBasedNode.serviceHint", {
            interpolate: { service: serviceName.value }
          });
      }
    });
    const header2 = computed(() => {
      if (isActivelyPolling.value) {
        return i18n.baseText("ndv.trigger.pollingNode.fetchingEvent");
      }
      if (triggerPanel.value?.header) {
        return triggerPanel.value.header;
      }
      if (isWebhookBasedNode.value) {
        return i18n.baseText("ndv.trigger.webhookBasedNode.action", {
          interpolate: { name: serviceName.value }
        });
      }
      return "";
    });
    const subheader = computed(() => {
      if (isActivelyPolling.value) {
        return i18n.baseText("ndv.trigger.pollingNode.fetchingHint", {
          interpolate: { name: serviceName.value }
        });
      }
      return "";
    });
    const executionsHelp = computed(() => {
      if (triggerPanel.value?.executionsHelp) {
        if (typeof triggerPanel.value.executionsHelp === "string") {
          return triggerPanel.value.executionsHelp;
        }
        if (!isWorkflowActive.value && triggerPanel.value.executionsHelp.inactive) {
          return triggerPanel.value.executionsHelp.inactive;
        }
        if (isWorkflowActive.value && triggerPanel.value.executionsHelp.active) {
          return triggerPanel.value.executionsHelp.active;
        }
      }
      if (isWebhookBasedNode.value) {
        if (isWorkflowActive.value) {
          return i18n.baseText("ndv.trigger.webhookBasedNode.executionsHelp.active", {
            interpolate: { service: serviceName.value }
          });
        } else {
          return i18n.baseText("ndv.trigger.webhookBasedNode.executionsHelp.inactive", {
            interpolate: { service: serviceName.value }
          });
        }
      }
      if (isPollingNode.value) {
        if (isWorkflowActive.value) {
          return i18n.baseText("ndv.trigger.pollingNode.executionsHelp.active", {
            interpolate: { service: serviceName.value }
          });
        } else {
          return i18n.baseText("ndv.trigger.pollingNode.executionsHelp.inactive", {
            interpolate: { service: serviceName.value }
          });
        }
      }
      return "";
    });
    const activationHint = computed(() => {
      if (isActivelyPolling.value || !triggerPanel.value) {
        return "";
      }
      if (triggerPanel.value.activationHint) {
        if (typeof triggerPanel.value.activationHint === "string") {
          return triggerPanel.value.activationHint;
        }
        if (!isWorkflowActive.value && typeof triggerPanel.value.activationHint.inactive === "string") {
          return triggerPanel.value.activationHint.inactive;
        }
        if (isWorkflowActive.value && typeof triggerPanel.value.activationHint.active === "string") {
          return triggerPanel.value.activationHint.active;
        }
      }
      if (isWebhookBasedNode.value) {
        if (isWorkflowActive.value) {
          return i18n.baseText("ndv.trigger.webhookBasedNode.activationHint.active", {
            interpolate: { service: serviceName.value }
          });
        } else {
          return i18n.baseText("ndv.trigger.webhookBasedNode.activationHint.inactive", {
            interpolate: { service: serviceName.value }
          });
        }
      }
      if (isPollingNode.value) {
        if (isWorkflowActive.value) {
          return i18n.baseText("ndv.trigger.pollingNode.activationHint.active", {
            interpolate: { service: serviceName.value }
          });
        } else {
          return i18n.baseText("ndv.trigger.pollingNode.activationHint.inactive", {
            interpolate: { service: serviceName.value }
          });
        }
      }
      return "";
    });
    const expandExecutionHelp = () => {
      if (help.value) {
        executionsHelpEventBus.emit("expand");
      }
    };
    const openWebhookUrl = () => {
      telemetry.track("User clicked ndv link", {
        workflow_id: workflowsStore.workflowId,
        push_ref: props.pushRef,
        pane: "input",
        type: "open-chat"
      });
      window.open(webhookTestUrl.value, "_blank", "noreferrer");
    };
    const onLinkClick = (e) => {
      if (!e.target) {
        return;
      }
      const target = e.target;
      if (target.localName !== "a") return;
      if (target.dataset?.key) {
        e.stopPropagation();
        e.preventDefault();
        if (target.dataset.key === "activate") {
          emit("activate");
        } else if (target.dataset.key === "executions") {
          telemetry.track("User clicked ndv link", {
            workflow_id: workflowsStore.workflowId,
            push_ref: props.pushRef,
            pane: "input",
            type: "open-executions-log"
          });
          ndvStore.activeNodeName = null;
          void router.push({
            name: VIEWS.EXECUTIONS
          });
        } else if (target.dataset.key === "settings") {
          uiStore.openModal(WORKFLOW_SETTINGS_MODAL_KEY);
        }
      }
    };
    const onTestLinkCopied = () => {
      telemetry.track("User copied webhook URL", {
        pane: "inputs",
        type: "test url"
      });
    };
    const onNodeExecute = () => {
      emit("execute");
    };
    return (_ctx, _cache) => {
      const _component_n8n_pulse = resolveComponent("n8n-pulse");
      const _component_n8n_text = resolveComponent("n8n-text");
      const _component_n8n_button = resolveComponent("n8n-button");
      const _component_n8n_spinner = resolveComponent("n8n-spinner");
      const _component_n8n_heading = resolveComponent("n8n-heading");
      const _component_n8n_link = resolveComponent("n8n-link");
      const _component_n8n_info_accordion = resolveComponent("n8n-info-accordion");
      const _directive_n8n_html = resolveDirective("n8n-html");
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(_ctx.$style.container)
      }, [
        createVNode(Transition, {
          name: "fade",
          mode: "out-in"
        }, {
          default: withCtx(() => [
            hasIssues2.value || hideContent.value ? (openBlock(), createElementBlock("div", _hoisted_1$1)) : isListeningForEvents.value ? (openBlock(), createElementBlock("div", _hoisted_2, [
              createVNode(_component_n8n_pulse, null, {
                default: withCtx(() => [
                  createVNode(_sfc_main$l, {
                    "node-type": nodeType.value,
                    size: 40
                  }, null, 8, ["node-type"])
                ]),
                _: 1
              }),
              isWebhookNode.value ? (openBlock(), createElementBlock("div", _hoisted_3, [
                createVNode(_component_n8n_text, {
                  tag: "div",
                  size: "large",
                  color: "text-dark",
                  class: "mb-2xs",
                  bold: ""
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(i18n).baseText("ndv.trigger.webhookNode.listening")), 1)
                  ]),
                  _: 1
                }),
                createBaseVNode("div", {
                  class: normalizeClass([_ctx.$style.shake, "mb-xs"])
                }, [
                  createVNode(_component_n8n_text, null, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(i18n).baseText("ndv.trigger.webhookNode.requestHint", {
                        interpolate: { type: webhookHttpMethod.value ?? "" }
                      })), 1)
                    ]),
                    _: 1
                  })
                ], 2),
                createVNode(CopyInput, {
                  value: webhookTestUrl.value,
                  "toast-title": unref(i18n).baseText("ndv.trigger.copiedTestUrl"),
                  class: "mb-2xl",
                  size: "medium",
                  collapse: true,
                  "copy-button-text": unref(i18n).baseText("generic.clickToCopy"),
                  onCopy: onTestLinkCopied
                }, null, 8, ["value", "toast-title", "copy-button-text"]),
                createVNode(_sfc_main$m, {
                  "data-test-id": "trigger-execute-button",
                  "node-name": _ctx.nodeName,
                  size: "medium",
                  "telemetry-source": "inputs",
                  onExecute: onNodeExecute
                }, null, 8, ["node-name"])
              ])) : (openBlock(), createElementBlock("div", _hoisted_4, [
                createVNode(_component_n8n_text, {
                  tag: "div",
                  size: "large",
                  color: "text-dark",
                  class: "mb-2xs",
                  bold: ""
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(listeningTitle.value), 1)
                  ]),
                  _: 1
                }),
                createBaseVNode("div", {
                  class: normalizeClass([_ctx.$style.shake, "mb-xs"])
                }, [
                  createVNode(_component_n8n_text, { tag: "div" }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(listeningHint.value), 1)
                    ]),
                    _: 1
                  })
                ], 2),
                displayChatButton.value ? (openBlock(), createElementBlock("div", _hoisted_5, [
                  createVNode(_component_n8n_button, {
                    class: "mb-xl",
                    onClick: _cache[0] || (_cache[0] = ($event) => openWebhookUrl())
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(unref(i18n).baseText("ndv.trigger.chatTrigger.openChat")), 1)
                    ]),
                    _: 1
                  })
                ])) : createCommentVNode("", true),
                createVNode(_sfc_main$m, {
                  "data-test-id": "trigger-execute-button",
                  "node-name": _ctx.nodeName,
                  size: "medium",
                  "telemetry-source": "inputs",
                  onExecute: onNodeExecute
                }, null, 8, ["node-name"])
              ]))
            ])) : (openBlock(), createElementBlock("div", _hoisted_6, [
              isActivelyPolling.value ? (openBlock(), createElementBlock("div", _hoisted_7, [
                createVNode(_component_n8n_spinner, { type: "ring" })
              ])) : createCommentVNode("", true),
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.action)
              }, [
                createBaseVNode("div", {
                  class: normalizeClass(_ctx.$style.header)
                }, [
                  header2.value ? (openBlock(), createBlock(_component_n8n_heading, {
                    key: 0,
                    tag: "h1",
                    bold: ""
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(header2.value), 1)
                    ]),
                    _: 1
                  })) : createCommentVNode("", true),
                  subheader.value ? (openBlock(), createBlock(_component_n8n_text, { key: 1 }, {
                    default: withCtx(() => [
                      createBaseVNode("span", {
                        textContent: toDisplayString(subheader.value)
                      }, null, 8, _hoisted_8)
                    ]),
                    _: 1
                  })) : createCommentVNode("", true)
                ], 2),
                createVNode(_sfc_main$m, {
                  "data-test-id": "trigger-execute-button",
                  "node-name": _ctx.nodeName,
                  size: "medium",
                  "telemetry-source": "inputs",
                  onExecute: onNodeExecute
                }, null, 8, ["node-name"])
              ], 2),
              activationHint.value ? (openBlock(), createBlock(_component_n8n_text, {
                key: 1,
                size: "small",
                onClick: onLinkClick
              }, {
                default: withCtx(() => [
                  withDirectives(createBaseVNode("span", null, null, 512), [
                    [_directive_n8n_html, activationHint.value]
                  ]),
                  _cache[1] || (_cache[1] = createTextVNode("  "))
                ]),
                _: 1
              })) : createCommentVNode("", true),
              activationHint.value && executionsHelp.value ? (openBlock(), createBlock(_component_n8n_link, {
                key: 2,
                size: "small",
                onClick: expandExecutionHelp
              }, {
                default: withCtx(() => [
                  createTextVNode(toDisplayString(unref(i18n).baseText("ndv.trigger.moreInfo")), 1)
                ]),
                _: 1
              })) : createCommentVNode("", true),
              executionsHelp.value ? (openBlock(), createBlock(_component_n8n_info_accordion, {
                key: 3,
                ref_key: "help",
                ref: help,
                class: normalizeClass(_ctx.$style.accordion),
                title: unref(i18n).baseText("ndv.trigger.executionsHint.question"),
                description: executionsHelp.value,
                "event-bus": unref(executionsHelpEventBus),
                "onClick:body": onLinkClick
              }, null, 8, ["class", "title", "description", "event-bus"])) : createCommentVNode("", true)
            ]))
          ]),
          _: 1
        })
      ], 2);
    };
  }
});
const container = "_container_13dut_123";
const header = "_header_13dut_140";
const action = "_action_13dut_147";
const shake = "_shake_13dut_151";
const accordion = "_accordion_13dut_172";
const style0 = {
  container,
  header,
  action,
  shake,
  accordion
};
const cssModules$1 = {
  "$style": style0
};
const TriggerPanel = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__cssModules", cssModules$1], ["__scopeId", "data-v-e575e8c9"]]);
const _hoisted_1 = {
  key: 0,
  ref: "container",
  class: "data-display",
  "data-test-id": "ndv-modal",
  tabindex: "0"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "NodeDetailsView",
  props: {
    workflowObject: {},
    readOnly: { type: Boolean, default: false },
    renaming: { type: Boolean },
    isProductionExecutionPreview: { type: Boolean, default: false }
  },
  emits: ["saveKeyboardShortcut", "valueChanged", "switchSelectedNode", "openConnectionNodeCreator", "redrawNode", "stopExecution"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const ndvStore = useNDVStore();
    const externalHooks = useExternalHooks();
    const nodeHelpers = useNodeHelpers();
    const { activeNode } = storeToRefs(ndvStore);
    const pinnedData = usePinnedData(activeNode);
    const workflowActivate = useWorkflowActivate();
    const nodeTypesStore = useNodeTypesStore();
    const uiStore = useUIStore();
    const workflowsStore = useWorkflowsStore();
    const settingsStore = useSettingsStore();
    const deviceSupport = useDeviceSupport();
    const telemetry = useTelemetry();
    const i18n = useI18n();
    const message = useMessage();
    const { APP_Z_INDEXES } = useStyles();
    const settingsEventBus = createEventBus();
    const redrawRequired = ref(false);
    const runInputIndex = ref(-1);
    const runOutputIndex = ref(-1);
    const isLinkingEnabled = ref(true);
    const selectedInput = ref();
    const triggerWaitingWarningEnabled = ref(false);
    const isDragging = ref(false);
    const mainPanelPosition = ref(0);
    const pinDataDiscoveryTooltipVisible = ref(false);
    const avgInputRowHeight = ref(0);
    const avgOutputRowHeight = ref(0);
    const isInputPaneActive = ref(false);
    const isOutputPaneActive = ref(false);
    const isPairedItemHoveringEnabled = ref(true);
    const pushRef = computed(() => ndvStore.pushRef);
    const activeNodeType = computed(() => {
      if (activeNode.value) {
        return nodeTypesStore.getNodeType(activeNode.value.type, activeNode.value.typeVersion);
      }
      return null;
    });
    const workflowRunning = computed(() => uiStore.isActionActive.workflowRunning);
    const showTriggerWaitingWarning = computed(
      () => triggerWaitingWarningEnabled.value && !!activeNodeType.value && !activeNodeType.value.group.includes("trigger") && workflowRunning.value && workflowsStore.executionWaitingForWebhook
    );
    const workflowRunData = computed(() => {
      if (workflowExecution.value === null) {
        return null;
      }
      const executionData = workflowExecution.value.data;
      if (executionData?.resultData) {
        return executionData.resultData.runData;
      }
      return null;
    });
    const parentNodes = computed(() => {
      if (activeNode.value) {
        return props.workflowObject.getParentNodesByDepth(activeNode.value.name, 1).map(({ name }) => name) || [];
      } else {
        return [];
      }
    });
    const parentNode = computed(() => {
      for (const parentNodeName of parentNodes.value) {
        if (workflowsStore?.pinnedWorkflowData?.[parentNodeName]) {
          return parentNodeName;
        }
        if (workflowRunData.value?.[parentNodeName]) {
          return parentNodeName;
        }
      }
      return parentNodes.value[0];
    });
    const inputNodeName = computed(() => {
      const nodeOutputs = activeNode.value && activeNodeType.value ? getNodeOutputs(props.workflowObject, activeNode.value, activeNodeType.value) : [];
      const nonMainOutputs = nodeOutputs.filter((output) => {
        if (typeof output === "string") return output !== NodeConnectionTypes.Main;
        return output.type !== NodeConnectionTypes.Main;
      });
      const isSubNode = nonMainOutputs.length > 0;
      if (isSubNode && activeNode.value) {
        const connectedOutputNode = props.workflowObject.getChildNodes(
          activeNode.value.name,
          "ALL_NON_MAIN"
        )?.[0];
        return connectedOutputNode;
      }
      return selectedInput.value || parentNode.value;
    });
    const inputNode = computed(() => {
      if (inputNodeName.value) {
        return workflowsStore.getNodeByName(inputNodeName.value);
      }
      return null;
    });
    const inputSize = computed(() => ndvStore.ndvInputDataWithPinnedData.length);
    const isTriggerNode = computed(
      () => !!activeNodeType.value && (activeNodeType.value.group.includes("trigger") || activeNodeType.value.name === START_NODE_TYPE)
    );
    const showTriggerPanel = computed(() => {
      const override = !!activeNodeType.value?.triggerPanel;
      if (typeof activeNodeType.value?.triggerPanel === "boolean") {
        return override;
      }
      const isWebhookBasedNode = !!activeNodeType.value?.webhooks?.length;
      const isPollingNode = activeNodeType.value?.polling;
      return !props.readOnly && isTriggerNode.value && (isWebhookBasedNode || isPollingNode || override);
    });
    const hasOutputConnection = computed(() => {
      if (!activeNode.value) return false;
      const outgoingConnections = workflowsStore.outgoingConnectionsByNodeName(activeNode.value.name);
      return (Object.values(outgoingConnections)?.[0]?.[0] ?? []).length > 0;
    });
    const isExecutableTriggerNode = computed(() => {
      if (!activeNodeType.value) return false;
      return EXECUTABLE_TRIGGER_NODE_TYPES.includes(activeNodeType.value.name);
    });
    const isActiveStickyNode = computed(
      () => !!ndvStore.activeNode && ndvStore.activeNode.type === STICKY_NODE_TYPE
    );
    const workflowExecution = computed(() => workflowsStore.getWorkflowExecution);
    const maxOutputRun = computed(() => {
      if (activeNode.value === null) {
        return 0;
      }
      const runData = workflowRunData.value;
      if (!runData?.[activeNode.value.name]) {
        return 0;
      }
      if (runData[activeNode.value.name].length) {
        return runData[activeNode.value.name].length - 1;
      }
      return 0;
    });
    const outputRun = computed(
      () => runOutputIndex.value === -1 ? maxOutputRun.value : Math.min(runOutputIndex.value, maxOutputRun.value)
    );
    const maxInputRun = computed(() => {
      if (inputNode.value === null || activeNode.value === null) {
        return 0;
      }
      const workflowNode = props.workflowObject.getNode(activeNode.value.name);
      if (!workflowNode || !activeNodeType.value) {
        return 0;
      }
      const outputs2 = getNodeOutputs(
        props.workflowObject,
        workflowNode,
        activeNodeType.value
      );
      let node2 = inputNode.value;
      const runData = workflowRunData.value;
      if (outputs2.some((output) => output !== NodeConnectionTypes.Main)) {
        node2 = activeNode.value;
      }
      if (!node2 || !runData || !runData.hasOwnProperty(node2.name)) {
        return 0;
      }
      if (runData[node2.name].length) {
        return runData[node2.name].length - 1;
      }
      return 0;
    });
    const inputRun = computed(() => {
      if (isLinkingEnabled.value && maxOutputRun.value === maxInputRun.value) {
        return outputRun.value;
      }
      if (runInputIndex.value === -1) {
        return maxInputRun.value;
      }
      return Math.min(runInputIndex.value, maxInputRun.value);
    });
    const canLinkRuns = computed(
      () => maxOutputRun.value > 0 && maxOutputRun.value === maxInputRun.value
    );
    const linked = computed(() => isLinkingEnabled.value && canLinkRuns.value);
    const featureRequestUrl = computed(() => {
      if (!activeNodeType.value) {
        return "";
      }
      return `${BASE_NODE_SURVEY_URL}${activeNodeType.value.name}`;
    });
    const outputPanelEditMode = computed(() => ndvStore.outputPanelEditMode);
    const isWorkflowRunning = computed(() => uiStore.isActionActive.workflowRunning);
    const isExecutionWaitingForWebhook = computed(() => workflowsStore.executionWaitingForWebhook);
    const blockUi = computed(() => isWorkflowRunning.value || isExecutionWaitingForWebhook.value);
    const foreignCredentials = computed(() => {
      const credentials = activeNode.value?.credentials;
      const usedCredentials = workflowsStore.usedCredentials;
      const foreignCredentialsArray = [];
      if (credentials && settingsStore.isEnterpriseFeatureEnabled[EnterpriseEditionFeature.Sharing]) {
        Object.values(credentials).forEach((credential) => {
          if (credential.id && usedCredentials[credential.id] && !usedCredentials[credential.id].currentUserHasAccess) {
            foreignCredentialsArray.push(credential.id);
          }
        });
      }
      return foreignCredentialsArray;
    });
    const hasForeignCredential = computed(() => foreignCredentials.value.length > 0);
    const setIsTooltipVisible = ({ isTooltipVisible }) => {
      pinDataDiscoveryTooltipVisible.value = isTooltipVisible;
    };
    const onKeyDown = (e) => {
      if (e.key === "s" && deviceSupport.isCtrlKeyPressed(e)) {
        onSaveWorkflow(e);
      }
    };
    const onSaveWorkflow = (e) => {
      e.stopPropagation();
      e.preventDefault();
      if (props.readOnly) return;
      emit("saveKeyboardShortcut", e);
    };
    const onInputItemHover = (e) => {
      if (e === null || !inputNodeName.value || !isPairedItemHoveringEnabled.value) {
        ndvStore.setHoveringItem(null);
        return;
      }
      const item = {
        nodeName: inputNodeName.value,
        runIndex: inputRun.value,
        outputIndex: e.outputIndex,
        itemIndex: e.itemIndex
      };
      ndvStore.setHoveringItem(item);
    };
    const onInputTableMounted = (e) => {
      avgInputRowHeight.value = e.avgRowHeight;
    };
    const onWorkflowActivate = () => {
      ndvStore.activeNodeName = null;
      setTimeout(() => {
        void workflowActivate.activateCurrentWorkflow("ndv");
      }, 1e3);
    };
    const onOutputItemHover = (e) => {
      if (e === null || !activeNode.value || !isPairedItemHoveringEnabled.value) {
        ndvStore.setHoveringItem(null);
        return;
      }
      const item = {
        nodeName: activeNode.value?.name,
        runIndex: outputRun.value,
        outputIndex: e.outputIndex,
        itemIndex: e.itemIndex
      };
      ndvStore.setHoveringItem(item);
    };
    const onFeatureRequestClick = () => {
      window.open(featureRequestUrl.value, "_blank");
      if (activeNode.value) {
        telemetry.track("User clicked ndv link", {
          node_type: activeNode.value.type,
          workflow_id: workflowsStore.workflowId,
          push_ref: pushRef.value,
          pane: NodeConnectionTypes.Main,
          type: "i-wish-this-node-would"
        });
      }
    };
    const onDragEnd = (e) => {
      isDragging.value = false;
      telemetry.track("User moved parameters pane", {
        // example method for tracking
        window_width: e.windowWidth,
        start_position: mainPanelPosition.value,
        end_position: e.position,
        node_type: activeNodeType.value ? activeNodeType.value.name : "",
        push_ref: pushRef.value,
        workflow_id: workflowsStore.workflowId
      });
      mainPanelPosition.value = e.position;
    };
    const onDragStart = (e) => {
      isDragging.value = true;
      mainPanelPosition.value = e.position;
    };
    const onPanelsInit = (e) => {
      mainPanelPosition.value = e.position;
    };
    const onLinkRunToOutput = () => {
      isLinkingEnabled.value = true;
      trackLinking("output");
    };
    const onUnlinkRun = (pane) => {
      runInputIndex.value = runOutputIndex.value;
      isLinkingEnabled.value = false;
      trackLinking(pane);
    };
    const onNodeExecute = () => {
      setTimeout(() => {
        if (!activeNode.value || !workflowRunning.value) {
          return;
        }
        triggerWaitingWarningEnabled.value = true;
      }, 1e3);
    };
    const openSettings = () => {
      settingsEventBus.emit("openSettings");
    };
    const trackLinking = (pane) => {
      telemetry.track("User changed ndv run linking", {
        node_type: activeNodeType.value ? activeNodeType.value.name : "",
        push_ref: pushRef.value,
        linked: linked.value,
        pane
      });
    };
    const onLinkRunToInput = () => {
      runOutputIndex.value = runInputIndex.value;
      isLinkingEnabled.value = true;
      trackLinking("input");
    };
    const valueChanged = (parameterData) => {
      emit("valueChanged", parameterData);
    };
    const onSwitchSelectedNode = (nodeTypeName) => {
      emit("switchSelectedNode", nodeTypeName);
    };
    const onOpenConnectionNodeCreator = (nodeTypeName, connectionType2) => {
      emit("openConnectionNodeCreator", nodeTypeName, connectionType2);
    };
    const close = async () => {
      if (isDragging.value) {
        return;
      }
      if (activeNode.value && (typeof activeNodeType.value?.outputs === "string" || typeof activeNodeType.value?.inputs === "string" || redrawRequired.value)) {
        const nodeName = activeNode.value.name;
        setTimeout(() => {
          emit("redrawNode", nodeName);
        }, 1);
      }
      if (outputPanelEditMode.value.enabled && activeNode.value) {
        const shouldPinDataBeforeClosing = await message.confirm(
          "",
          i18n.baseText("ndv.pinData.beforeClosing.title"),
          {
            confirmButtonText: i18n.baseText("ndv.pinData.beforeClosing.confirm"),
            cancelButtonText: i18n.baseText("ndv.pinData.beforeClosing.cancel")
          }
        );
        if (shouldPinDataBeforeClosing === MODAL_CONFIRM) {
          const { value } = outputPanelEditMode.value;
          try {
            pinnedData.setData(jsonParse(value), "on-ndv-close-modal");
          } catch (error) {
            console.error(error);
          }
        }
        ndvStore.setOutputPanelEditModeEnabled(false);
      }
      await externalHooks.run("dataDisplay.nodeEditingFinished");
      telemetry.track("User closed node modal", {
        node_type: activeNodeType.value ? activeNodeType.value?.name : "",
        push_ref: pushRef.value,
        workflow_id: workflowsStore.workflowId
      });
      triggerWaitingWarningEnabled.value = false;
      ndvStore.activeNodeName = null;
      ndvStore.resetNDVPushRef();
    };
    const trackRunChange = (run, pane) => {
      telemetry.track("User changed ndv run dropdown", {
        push_ref: pushRef.value,
        run_index: run,
        node_type: activeNodeType.value ? activeNodeType.value?.name : "",
        pane
      });
    };
    const onRunOutputIndexChange = (run) => {
      runOutputIndex.value = run;
      trackRunChange(run, "output");
    };
    const onRunInputIndexChange = (run) => {
      runInputIndex.value = run;
      if (linked.value) {
        runOutputIndex.value = run;
      }
      trackRunChange(run, "input");
    };
    const onOutputTableMounted = (e) => {
      avgOutputRowHeight.value = e.avgRowHeight;
    };
    const onInputNodeChange = (value, index) => {
      runInputIndex.value = -1;
      isLinkingEnabled.value = true;
      selectedInput.value = value;
      telemetry.track("User changed ndv input dropdown", {
        node_type: activeNode.value ? activeNode.value.type : "",
        push_ref: pushRef.value,
        workflow_id: workflowsStore.workflowId,
        selection_value: index,
        input_node_type: inputNode.value ? inputNode.value.type : ""
      });
    };
    const onStopExecution = () => {
      emit("stopExecution");
    };
    const activateInputPane = () => {
      isInputPaneActive.value = true;
      isOutputPaneActive.value = false;
    };
    const activateOutputPane = () => {
      isInputPaneActive.value = false;
      isOutputPaneActive.value = true;
    };
    const onSearch = (search2) => {
      isPairedItemHoveringEnabled.value = !search2;
    };
    const registerKeyboardListener = () => {
      document.addEventListener("keydown", onKeyDown, true);
    };
    const unregisterKeyboardListener = () => {
      document.removeEventListener("keydown", onKeyDown, true);
    };
    watch(
      activeNode,
      (node2, oldNode) => {
        if (node2 && !oldNode) {
          registerKeyboardListener();
        } else if (!node2) {
          unregisterKeyboardListener();
        }
        if (node2 && node2.name !== oldNode?.name && !isActiveStickyNode.value) {
          runInputIndex.value = -1;
          runOutputIndex.value = -1;
          isLinkingEnabled.value = true;
          selectedInput.value = void 0;
          triggerWaitingWarningEnabled.value = false;
          avgOutputRowHeight.value = 0;
          avgInputRowHeight.value = 0;
          setTimeout(() => ndvStore.setNDVPushRef(), 0);
          if (!activeNodeType.value) {
            return;
          }
          void externalHooks.run("dataDisplay.nodeTypeChanged", {
            nodeSubtitle: nodeHelpers.getNodeSubtitle(node2, activeNodeType.value, props.workflowObject)
          });
          setTimeout(() => {
            if (activeNode.value) {
              const outgoingConnections = workflowsStore.outgoingConnectionsByNodeName(
                activeNode.value?.name
              );
              telemetry.track("User opened node modal", {
                node_id: activeNode.value?.id,
                node_type: activeNodeType.value ? activeNodeType.value?.name : "",
                workflow_id: workflowsStore.workflowId,
                push_ref: pushRef.value,
                is_editable: !hasForeignCredential.value,
                parameters_pane_position: mainPanelPosition.value,
                input_first_connector_runs: maxInputRun.value,
                output_first_connector_runs: maxOutputRun.value,
                selected_view_inputs: isTriggerNode.value ? "trigger" : ndvStore.inputPanelDisplayMode,
                selected_view_outputs: ndvStore.outputPanelDisplayMode,
                input_connectors: parentNodes.value.length,
                output_connectors: outgoingConnections?.main?.length,
                input_displayed_run_index: inputRun.value,
                output_displayed_run_index: outputRun.value,
                data_pinning_tooltip_presented: pinDataDiscoveryTooltipVisible.value,
                input_displayed_row_height_avg: avgInputRowHeight.value,
                output_displayed_row_height_avg: avgOutputRowHeight.value
              });
            }
          }, 2e3);
        }
        if (window.top && !isActiveStickyNode.value) {
          window.top.postMessage(JSON.stringify({ command: node2 ? "openNDV" : "closeNDV" }), "*");
        }
      },
      { immediate: true }
    );
    watch(maxOutputRun, () => {
      runOutputIndex.value = -1;
    });
    watch(maxInputRun, () => {
      runInputIndex.value = -1;
    });
    watch(inputNodeName, (nodeName) => {
      setTimeout(() => {
        ndvStore.setInputNodeName(nodeName);
      }, 0);
    });
    watch(inputRun, (inputRun2) => {
      setTimeout(() => {
        ndvStore.setInputRunIndex(inputRun2);
      }, 0);
    });
    onMounted(() => {
      dataPinningEventBus.on("data-pinning-discovery", setIsTooltipVisible);
    });
    onBeforeUnmount(() => {
      dataPinningEventBus.off("data-pinning-discovery", setIsTooltipVisible);
      unregisterKeyboardListener();
    });
    return (_ctx, _cache) => {
      const _component_n8n_icon = resolveComponent("n8n-icon");
      const _component_n8n_text = resolveComponent("n8n-text");
      const _component_n8n_tooltip = resolveComponent("n8n-tooltip");
      const _component_font_awesome_icon = resolveComponent("font-awesome-icon");
      const _component_el_dialog = resolveComponent("el-dialog");
      return openBlock(), createBlock(_component_el_dialog, {
        id: "ndv",
        "model-value": (!!unref(activeNode) || _ctx.renaming) && !isActiveStickyNode.value,
        "before-close": close,
        "show-close": false,
        class: "data-display-wrapper ndv-wrapper",
        "overlay-class": "data-display-overlay",
        width: "auto",
        "append-to": `#${unref(APP_MODALS_ELEMENT_ID)}`,
        "data-test-id": "ndv",
        "z-index": unref(APP_Z_INDEXES).NDV,
        "data-has-output-connection": hasOutputConnection.value
      }, {
        default: withCtx(() => [
          createVNode(_component_n8n_tooltip, {
            placement: "bottom-start",
            visible: showTriggerWaitingWarning.value,
            disabled: !showTriggerWaitingWarning.value
          }, {
            content: withCtx(() => [
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.triggerWarning)
              }, toDisplayString(unref(i18n).baseText("ndv.backToCanvas.waitingForTriggerWarning")), 3)
            ]),
            default: withCtx(() => [
              createBaseVNode("div", {
                class: normalizeClass(_ctx.$style.backToCanvas),
                "data-test-id": "back-to-canvas",
                onClick: close
              }, [
                createVNode(_component_n8n_icon, {
                  icon: "arrow-left",
                  color: "text-xlight",
                  size: "medium"
                }),
                createVNode(_component_n8n_text, {
                  color: "text-xlight",
                  size: "medium",
                  bold: true
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(i18n).baseText("ndv.backToCanvas")), 1)
                  ]),
                  _: 1
                })
              ], 2)
            ]),
            _: 1
          }, 8, ["visible", "disabled"]),
          unref(activeNode) ? (openBlock(), createElementBlock("div", _hoisted_1, [
            createBaseVNode("div", {
              class: normalizeClass(_ctx.$style.modalBackground),
              onClick: close
            }, null, 2),
            (openBlock(), createBlock(NDVDraggablePanels, {
              key: unref(activeNode).name,
              "is-trigger-node": isTriggerNode.value,
              "hide-input-and-output": activeNodeType.value === null,
              position: isTriggerNode.value && !showTriggerPanel.value ? 0 : void 0,
              "is-draggable": !isTriggerNode.value,
              "has-double-width": activeNodeType.value?.parameterPane === "wide",
              "node-type": activeNodeType.value,
              onSwitchSelectedNode,
              onOpenConnectionNodeCreator,
              onClose: close,
              onInit: onPanelsInit,
              onDragstart: onDragStart,
              onDragend: onDragEnd
            }, createSlots({
              output: withCtx(() => [
                createVNode(OutputPanel, {
                  "data-test-id": "output-panel",
                  workflow: _ctx.workflowObject,
                  "can-link-runs": canLinkRuns.value,
                  "run-index": outputRun.value,
                  "linked-runs": linked.value,
                  "push-ref": pushRef.value,
                  "is-read-only": _ctx.readOnly || hasForeignCredential.value,
                  "block-u-i": blockUi.value && isTriggerNode.value && !isExecutableTriggerNode.value,
                  "is-production-execution-preview": _ctx.isProductionExecutionPreview,
                  "is-pane-active": isOutputPaneActive.value,
                  onActivatePane: activateOutputPane,
                  onLinkRun: onLinkRunToOutput,
                  onUnlinkRun: _cache[1] || (_cache[1] = () => onUnlinkRun("output")),
                  onRunChange: onRunOutputIndexChange,
                  onOpenSettings: openSettings,
                  onTableMounted: onOutputTableMounted,
                  onItemHover: onOutputItemHover,
                  onSearch
                }, null, 8, ["workflow", "can-link-runs", "run-index", "linked-runs", "push-ref", "is-read-only", "block-u-i", "is-production-execution-preview", "is-pane-active"])
              ]),
              main: withCtx(() => [
                createVNode(NodeSettings, {
                  "event-bus": unref(settingsEventBus),
                  dragging: isDragging.value,
                  "push-ref": pushRef.value,
                  "node-type": activeNodeType.value,
                  "foreign-credentials": foreignCredentials.value,
                  "read-only": _ctx.readOnly,
                  "block-u-i": blockUi.value && showTriggerPanel.value,
                  executable: !_ctx.readOnly,
                  "input-size": inputSize.value,
                  onValueChanged: valueChanged,
                  onExecute: onNodeExecute,
                  onStopExecution,
                  onRedrawRequired: _cache[2] || (_cache[2] = ($event) => redrawRequired.value = true),
                  onActivate: onWorkflowActivate,
                  onSwitchSelectedNode,
                  onOpenConnectionNodeCreator
                }, null, 8, ["event-bus", "dragging", "push-ref", "node-type", "foreign-credentials", "read-only", "block-u-i", "executable", "input-size"]),
                featureRequestUrl.value ? (openBlock(), createElementBlock("a", {
                  key: 0,
                  class: normalizeClass(_ctx.$style.featureRequest),
                  target: "_blank",
                  onClick: onFeatureRequestClick
                }, [
                  createVNode(_component_font_awesome_icon, { icon: "lightbulb" }),
                  createTextVNode(" " + toDisplayString(unref(i18n).baseText("ndv.featureRequest")), 1)
                ], 2)) : createCommentVNode("", true)
              ]),
              _: 2
            }, [
              showTriggerPanel.value || !isTriggerNode.value ? {
                name: "input",
                fn: withCtx(() => [
                  showTriggerPanel.value ? (openBlock(), createBlock(TriggerPanel, {
                    key: 0,
                    "node-name": unref(activeNode).name,
                    "push-ref": pushRef.value,
                    onExecute: onNodeExecute,
                    onActivate: onWorkflowActivate
                  }, null, 8, ["node-name", "push-ref"])) : !isTriggerNode.value ? (openBlock(), createBlock(InputPanel, {
                    key: 1,
                    workflow: _ctx.workflowObject,
                    "can-link-runs": canLinkRuns.value,
                    "run-index": inputRun.value,
                    "linked-runs": linked.value,
                    "current-node-name": inputNodeName.value,
                    "push-ref": pushRef.value,
                    "read-only": _ctx.readOnly || hasForeignCredential.value,
                    "is-production-execution-preview": _ctx.isProductionExecutionPreview,
                    "is-pane-active": isInputPaneActive.value,
                    onActivatePane: activateInputPane,
                    onLinkRun: onLinkRunToInput,
                    onUnlinkRun: _cache[0] || (_cache[0] = () => onUnlinkRun("input")),
                    onRunChange: onRunInputIndexChange,
                    onOpenSettings: openSettings,
                    onChangeInputNode: onInputNodeChange,
                    onExecute: onNodeExecute,
                    onTableMounted: onInputTableMounted,
                    onItemHover: onInputItemHover,
                    onSearch
                  }, null, 8, ["workflow", "can-link-runs", "run-index", "linked-runs", "current-node-name", "push-ref", "read-only", "is-production-execution-preview", "is-pane-active"])) : createCommentVNode("", true)
                ]),
                key: "0"
              } : void 0
            ]), 1032, ["is-trigger-node", "hide-input-and-output", "position", "is-draggable", "has-double-width", "node-type"]))
          ], 512)) : createCommentVNode("", true)
        ]),
        _: 1
      }, 8, ["model-value", "append-to", "z-index", "data-has-output-connection"]);
    };
  }
});
const modalBackground = "_modalBackground_176ah_123";
const triggerWarning = "_triggerWarning_176ah_128";
const backToCanvas = "_backToCanvas_176ah_132";
const featureRequest = "_featureRequest_176ah_153";
const style1 = {
  modalBackground,
  triggerWarning,
  backToCanvas,
  featureRequest
};
const cssModules = {
  "$style": style1
};
const NodeDetailsView = /* @__PURE__ */ _export_sfc(_sfc_main, [["__cssModules", cssModules]]);
const NodeDetailsView$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: NodeDetailsView
}, Symbol.toStringTag, { value: "Module" }));
export {
  NodeDetailsView$1 as N,
  VueJsonPretty as V
};
