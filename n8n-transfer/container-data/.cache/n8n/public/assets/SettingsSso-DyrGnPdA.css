/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._top_1u4m7_123 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2xl) 0 var(--spacing-xl);
}
._switch_1u4m7_130 span {
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-light);
}
._buttons_1u4m7_136 {
  display: flex;
  justify-content: flex-start;
  padding: var(--spacing-2xl) 0 var(--spacing-2xs);
}
._buttons_1u4m7_136 button {
  margin: 0 var(--spacing-s) 0 0;
}
._group_1u4m7_145 {
  padding: var(--spacing-xl) 0 0;
}
._group_1u4m7_145 > label {
  display: inline-block;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  padding: 0 0 var(--spacing-2xs);
}
._group_1u4m7_145 small {
  display: block;
  padding: var(--spacing-2xs) 0 0;
  font-size: var(--font-size-2xs);
  color: var(--color-text-base);
}
._actionBox_1u4m7_161 {
  margin: var(--spacing-2xl) 0 0;
}
._footer_1u4m7_165 {
  color: var(--color-text-base);
  font-size: var(--font-size-2xs);
}