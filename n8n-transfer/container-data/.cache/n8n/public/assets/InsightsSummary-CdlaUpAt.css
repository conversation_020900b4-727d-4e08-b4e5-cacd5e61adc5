/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._insights_syjwy_123 {
  display: grid;
  grid-template-rows: auto 1fr;
  padding: var(--spacing-xs) 0 var(--spacing-2xl);
}
._insights_syjwy_123 ul {
  display: flex;
  height: 101px;
  align-items: stretch;
  justify-content: space-evenly;
  border: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
  border-radius: 6px;
  list-style: none;
  overflow-x: auto;
}
._insights_syjwy_123 ul li {
  display: flex;
  justify-content: stretch;
  align-items: stretch;
  flex: 1 0;
  border-left: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
}
._insights_syjwy_123 ul li:first-child {
  border-left: 0;
}
._insights_syjwy_123 ul a {
  display: grid;
  align-items: center;
  align-content: center;
  width: 100%;
  height: 100%;
  padding: var(--spacing-3xs) var(--spacing-l) 0;
  border-bottom: 3px solid transparent;
}
._insights_syjwy_123 ul a:hover {
  background-color: var(--color-background-xlight);
  transition: background-color 0.3s;
}
._insights_syjwy_123 ul a._activeTab_syjwy_161 {
  background-color: var(--color-background-xlight);
  border-color: var(--color-primary);
  transition: background-color 0.3s ease-in-out;
}
._insights_syjwy_123 ul a strong {
  justify-self: flex-start;
  color: var(--color-text-dark);
  font-size: var(--font-size-s);
  font-weight: 400;
  white-space: nowrap;
  margin-bottom: var(--spacing-3xs);
}
._insights_syjwy_123 ul a ._days_syjwy_174 {
  padding: 0;
  margin: 0 0 var(--spacing-xs);
  color: var(--color-text-light);
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-normal);
}
._insights_syjwy_123 ul a span {
  display: flex;
  align-items: baseline;
}
._insights_syjwy_123 ul a span._empty_syjwy_185 em {
  color: var(--color-text-lighter);
}
._insights_syjwy_123 ul a span._empty_syjwy_185 small {
  padding: 0;
  height: 21px;
  font-weight: var(--font-weight-bold);
}
._insights_syjwy_123 ul a span._empty_syjwy_185 small ._icon_syjwy_193 {
  height: 20px;
  width: 8px;
  top: 5px;
  transform: translateY(0);
  color: var(--color-text-light);
}
._insights_syjwy_123 ul a em {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  color: var(--color-text-dark);
  font-size: 24px;
  line-height: 100%;
  font-weight: 600;
  font-style: normal;
  gap: var(--spacing-5xs);
}
._insights_syjwy_123 ul a em i {
  font-size: 22px;
  font-style: normal;
}
._insights_syjwy_123 ul a small {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 0 0 14px;
  margin: 0 0 0 var(--spacing-xs);
  font-size: var(--font-size-2xs);
  font-weight: var(--font-weight-bold);
  white-space: nowrap;
}
._insights_syjwy_123 ._noData_syjwy_225 em {
  color: var(--color-text-light);
  font-size: var(--font-size-m);
}
._positive_syjwy_230 {
  color: var(--color-success);
}
._negative_syjwy_234 {
  color: var(--color-danger);
}
._neutral_syjwy_238 {
  color: var(--color-text-light);
}
._neutral_syjwy_238 ._icon_syjwy_193 {
  font-size: 17px;
}
._icon_syjwy_193 {
  position: absolute;
  font-size: 17px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
._loading_syjwy_253 {
  display: flex;
  align-self: stretch;
  align-items: stretch;
}
._loading_syjwy_253 > div {
  margin: 0;
  height: auto;
  border-radius: inherit;
}