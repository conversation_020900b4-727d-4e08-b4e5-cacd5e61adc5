import { _ as _export_sfc, i as createElementBlock, g as openBlock, gj as createStaticVNode } from "./index-CN4JmOoA.js";
const _sfc_main = {};
const _hoisted_1 = {
  width: "14",
  height: "14",
  viewBox: "0 0 14 14",
  fill: "none",
  xmlns: "http://www.w3.org/2000/svg"
};
function _sfc_render(_ctx, _cache) {
  return openBlock(), createElementBlock("svg", _hoisted_1, _cache[0] || (_cache[0] = [
    createStaticVNode('<g data-v-7870f87e><path d="M7 0L7 3" stroke="currentColor" stroke-width="2" class="line one" data-v-7870f87e></path><path d="M11.9497 2.05031L9.82837 4.17163" stroke="currentColor" stroke-width="2" class="line two" data-v-7870f87e></path><path d="M14 7L11 7" stroke="currentColor" stroke-width="2" class="line three" data-v-7870f87e></path><path d="M11.9497 11.9497L9.82839 9.82839" stroke="currentColor" stroke-width="2" class="line four" data-v-7870f87e></path><path d="M7 14L7 11" stroke="currentColor" stroke-width="2" class="line five" data-v-7870f87e></path><path d="M0 7L3 7" stroke="currentColor" stroke-width="2" class="line seven" data-v-7870f87e></path><path d="M2.05031 2.05031L4.17163 4.17163" stroke="currentColor" stroke-width="2" class="line eight" data-v-7870f87e></path><path d="M2.05029 11.9497L4.17161 9.82839" stroke="currentColor" stroke-width="2" class="line six" data-v-7870f87e></path></g>', 1)
  ]));
}
const __unplugin_components_0 = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7870f87e"]]);
export {
  __unplugin_components_0 as _
};
