/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_1ii5r_123 > * {
  margin-bottom: var(--spacing-2xl);
}
._syncTable_1ii5r_127 {
  margin-bottom: var(--spacing-2xl);
}
._header_1ii5r_131 {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
._header_1ii5r_131 *:first-child {
  flex-grow: 1;
}
._enableFeatureContainer_1ii5r_140 {
  margin-bottom: var(--spacing-1xl);
}
._enableFeatureContainer_1ii5r_140 > span {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-bold);
  padding: 0;
}
._enableFeatureContainer_1ii5r_140 > * {
  padding: 0.5em;
}
._sectionHeader_1ii5r_154 {
  margin-bottom: var(--spacing-s);
}
._settingsForm_1ii5r_158 .form-text {
  margin-top: var(--spacing-xl);
}
._docsInfoTip_1ii5r_162, ._docsInfoTip_1ii5r_162 > div {
  margin-bottom: var(--spacing-xl);
}