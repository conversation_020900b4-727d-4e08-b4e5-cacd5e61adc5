/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_1ni3x_123 {
  padding-bottom: 100px;
}
._container_1ni3x_123 > * {
  margin-bottom: var(--spacing-2xl);
}
._header_1ni3x_130 {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
._header_1ni3x_130 *:first-child {
  flex-grow: 1;
}
._user_1ni3x_139 {
  display: flex;
  align-items: center;
}
@media (max-width: 600px) {
._user_1ni3x_139 {
    display: none;
}
}
._username_1ni3x_149 {
  margin-right: var(--spacing-s);
  text-align: right;
}
@media (max-width: 992px) {
._username_1ni3x_149 {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
}
}
._disableMfaButton_1ni3x_161 {
  --button-color: var(--color-danger);
}
._disableMfaButton_1ni3x_161 > span {
  font-weight: var(--font-weight-bold);
}
._button_1ni3x_168 {
  font-size: var(--spacing-xs);
}
._button_1ni3x_168 > span {
  font-weight: var(--font-weight-bold);
}
._infoText_1ni3x_175 {
  font-size: var(--font-size-2xs);
  color: var(--color-text-light);
}
._themeSelect_1ni3x_180 {
  max-width: 50%;
}