/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._iconButton_fx9xi_123 {
  padding-left: 0;
  padding-right: 0;
}
._iconButton_fx9xi_123 svg {
  width: 16px;
  height: 16px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.vue-flow__controls {
  display: flex;
  gap: var(--spacing-xs);
  box-shadow: none;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._edge_cbk94_123 {
  transition-property: stroke, opacity;
  transition-duration: 300ms;
  transition-timing-function: ease;
  opacity: 0;
}
._edge_cbk94_123._visible_cbk94_129 {
  opacity: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._contextMenu_12fz3_123 {
  position: fixed;
}
._activator_12fz3_127 {
  pointer-events: none;
  opacity: 0;
}
path[data-v-161512f5] {
	stroke: var(--color-canvas-read-only-line);
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvasEdgeToolbar_8p9ym_123 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2xs);
  pointer-events: all;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
@media (prefers-color-scheme: dark) {
body:not([data-theme]) .canvas-edge-toolbar-button {
    --button-background-color: var(--color-background-base);
    --button-hover-background-color: var(--color-background-light);
}
}
[data-theme=dark] .canvas-edge-toolbar-button {
  --button-background-color: var(--color-background-base);
  --button-hover-background-color: var(--color-background-light);
}
.canvas-edge-toolbar-button {
  border-width: 2px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._edge_fyzmo_123 {
  transition: stroke 0.3s ease, fill 0.3s ease;
}
._edgeLabelWrapper_fyzmo_127 {
  transform: translateY(calc(var(--spacing-xs) * -1));
  position: absolute;
}
._edgeLabel_fyzmo_127 {
  font-size: var(--font-size-xs);
  background-color: hsla(var(--color-canvas-background-h), var(--color-canvas-background-s), var(--color-canvas-background-l), 0.85);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._popover_3lp7u_123 {
  min-width: 208px;
  margin-bottom: -8px;
  margin-left: -2px;
}
._content_3lp7u_129 {
  display: flex;
  flex-direction: row;
  width: -moz-fit-content;
  width: fit-content;
  gap: var(--spacing-2xs);
}
._color_3lp7u_136 {
  width: 20px;
  height: 20px;
  border-width: 1px;
  border-style: solid;
  border-color: var(--color-foreground-xdark);
  border-radius: 50%;
  background: var(--color-sticky-background);
}
._color_3lp7u_136:hover {
  cursor: pointer;
}
._color_3lp7u_136._selected_3lp7u_148 {
  box-shadow: 0 0 0 1px var(--color-sticky-background);
}
._color_3lp7u_136._sticky-color-1_3lp7u_151 {
  --color-sticky-background: var(--color-sticky-background-1);
}
._color_3lp7u_136._sticky-color-2_3lp7u_154 {
  --color-sticky-background: var(--color-sticky-background-2);
}
._color_3lp7u_136._sticky-color-3_3lp7u_157 {
  --color-sticky-background: var(--color-sticky-background-3);
}
._color_3lp7u_136._sticky-color-4_3lp7u_160 {
  --color-sticky-background: var(--color-sticky-background-4);
}
._color_3lp7u_136._sticky-color-5_3lp7u_163 {
  --color-sticky-background: var(--color-sticky-background-5);
}
._color_3lp7u_136._sticky-color-6_3lp7u_166 {
  --color-sticky-background: var(--color-sticky-background-6);
}
._color_3lp7u_136._sticky-color-7_3lp7u_169 {
  --color-sticky-background: var(--color-sticky-background-7);
}
._option_3lp7u_173 {
  display: inline-block;
  padding: var(--spacing-3xs);
  color: var(--color-text-light);
}
._option_3lp7u_173 svg {
  width: var(--font-size-s) !important;
}
._option_3lp7u_173:hover {
  color: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvasNodeToolbar_104hp_123 {
  padding-bottom: var(--spacing-xs);
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
._canvasNodeToolbarItems_104hp_130 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-canvas-background);
  border-radius: var(--border-radius-base);
}
._canvasNodeToolbarItems_104hp_130 .button {
  --button-font-color: var(--color-text-light);
}
._forceVisible_104hp_141 {
  opacity: 1 !important;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._disabledStrikeThrough_nl2g7_123 {
  border: 1px solid var(--color-foreground-dark);
  position: absolute;
  top: calc(var(--canvas-node--height) / 2 - 1px);
  left: -4px;
  width: calc(100% + 12px);
  pointer-events: none;
}
._success_nl2g7_132 {
  border-color: var(--color-success-light);
}
._warning_nl2g7_136 {
  border-color: var(--color-warning-tint-1);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._status_93vv6_123 {
  display: flex;
  align-items: center;
  gap: var(--spacing-5xs);
  font-weight: var(--font-weight-bold);
}
._runData_93vv6_130 {
  color: var(--color-success);
}
._waiting_93vv6_134 {
  color: var(--color-secondary);
}
._pinnedData_93vv6_138 {
  color: var(--color-secondary);
}
._running_93vv6_142 {
  width: calc(100% - 2 * var(--canvas-node--status-icons-offset));
  height: calc(100% - 2 * var(--canvas-node--status-icons-offset));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.75em;
  color: hsla(var(--color-primary-h), var(--color-primary-s), var(--color-primary-l), 0.7);
}
._node-waiting-spinner_93vv6_152 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.75em;
  color: hsla(var(--color-primary-h), var(--color-primary-s), var(--color-primary-l), 0.7);
  width: 100%;
  height: 100%;
  position: absolute;
  left: -34px;
  top: -34px;
}
._issues_93vv6_165 {
  color: var(--color-danger);
  cursor: default;
}
._count_93vv6_170 {
  font-size: var(--font-size-s);
}
._warning_93vv6_174 {
  color: var(--color-warning);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._tooltipTrigger_dauyi_123 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
._popper_dauyi_131 {
  white-space: nowrap;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._node_1d2zc_123 {
  --canvas-node--max-vertical-handles: max(
  	var(--canvas-node--main-input-count),
  	var(--canvas-node--main-output-count),
  	1
  );
  --canvas-node--height: calc(100px + max(0, var(--canvas-node--max-vertical-handles) - 3) * 42px);
  --canvas-node--width: 100px;
  --canvas-node-border-width: 2px;
  --configurable-node--min-input-count: 4;
  --configurable-node--input-width: 64px;
  --configurable-node--icon-offset: 30px;
  --configurable-node--icon-size: 30px;
  --trigger-node--border-radius: 36px;
  --canvas-node--status-icons-offset: var(--spacing-3xs);
  --node-icon-color: var(--color-foreground-dark);
  position: relative;
  height: var(--canvas-node--height);
  width: var(--canvas-node--width);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--canvas-node--background, var(--color-node-background));
  border: var(--canvas-node-border-width) solid var(--canvas-node--border-color, var(--color-foreground-xdark));
  border-radius: var(--border-radius-large);
  /**
   * Node types
   */
  /**
   * State classes
   * The reverse order defines the priority in case multiple states are active
   */
}
._node_1d2zc_123._trigger_1d2zc_156 {
  border-radius: var(--trigger-node--border-radius) var(--border-radius-large) var(--border-radius-large) var(--trigger-node--border-radius);
}
._node_1d2zc_123._configuration_1d2zc_159 {
  --canvas-node--width: 80px;
  --canvas-node--height: 80px;
  background: var(--canvas-node--background, var(--node-type-supplemental-background));
  border: var(--canvas-node-border-width) solid var(--canvas-node--border-color, var(--color-foreground-dark));
  border-radius: 50px;
}
._node_1d2zc_123._configuration_1d2zc_159 ._statusIcons_1d2zc_166 {
  right: unset;
}
._node_1d2zc_123._configurable_1d2zc_169 {
  --canvas-node--height: 100px;
  --canvas-node--width: calc(
  	max(var(--configurable-node--input-count, 4), var(--configurable-node--min-input-count)) *
  		var(--configurable-node--input-width)
  );
  justify-content: flex-start;
}
._node_1d2zc_123._configurable_1d2zc_169 .n8n-node-icon {
  margin-left: var(--configurable-node--icon-offset);
}
._node_1d2zc_123._configurable_1d2zc_169 ._description_1d2zc_180 {
  top: unset;
  position: relative;
  margin-top: 0;
  margin-left: var(--spacing-s);
  margin-right: var(--spacing-s);
  width: auto;
  min-width: unset;
  max-width: calc(var(--canvas-node--width) - var(--configurable-node--icon-offset) - var(--configurable-node--icon-size) - 2 * var(--spacing-s));
}
._node_1d2zc_123._configurable_1d2zc_169 ._label_1d2zc_190 {
  text-align: left;
}
._node_1d2zc_123._configurable_1d2zc_169 ._subtitle_1d2zc_193 {
  text-align: left;
}
._node_1d2zc_123._configurable_1d2zc_169._configuration_1d2zc_159 {
  --canvas-node--height: 75px;
}
._node_1d2zc_123._configurable_1d2zc_169._configuration_1d2zc_159 ._statusIcons_1d2zc_166 {
  right: calc(-1 * var(--spacing-2xs));
  bottom: 0;
}
._node_1d2zc_123._selected_1d2zc_203 {
  box-shadow: 0 0 0 8px var(--color-canvas-selected-transparent);
}
._node_1d2zc_123._success_1d2zc_206 {
  border-color: var(--color-canvas-node-success-border-color, var(--color-success));
}
._node_1d2zc_123._warning_1d2zc_209 {
  border-color: var(--color-warning);
}
._node_1d2zc_123._error_1d2zc_212 {
  border-color: var(--color-canvas-node-error-border-color, var(--color-danger));
}
._node_1d2zc_123._pinned_1d2zc_215 {
  border-color: var(--color-canvas-node-pinned-border-color, var(--color-node-pinned-border));
}
._node_1d2zc_123._disabled_1d2zc_218 {
  border-color: var(--color-canvas-node-disabled-border-color, var(--color-foreground-base));
}
._node_1d2zc_123._running_1d2zc_221 {
  background-color: var(--color-node-executing-background);
  border-color: var(--color-canvas-node-running-border-color, var(--color-node-running-border));
}
._node_1d2zc_123._waiting_1d2zc_225 {
  border-color: var(--color-canvas-node-waiting-border-color, var(--color-secondary));
}
._description_1d2zc_180 {
  top: 100%;
  position: absolute;
  width: 100%;
  min-width: calc(var(--canvas-node--width) * 2);
  margin-top: var(--spacing-2xs);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xs);
  align-items: center;
}
._label_1d2zc_190,
._disabledLabel_1d2zc_242 {
  font-size: var(--font-size-m);
  text-align: center;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  overflow-wrap: anywhere;
  font-weight: var(--font-weight-medium);
  line-height: var(--font-line-height-compact);
}
._subtitle_1d2zc_193 {
  width: 100%;
  text-align: center;
  color: var(--color-text-light);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: var(--font-line-height-compact);
  font-weight: var(--font-weight-regular);
}
._statusIcons_1d2zc_166 {
  position: absolute;
  bottom: var(--canvas-node--status-icons-offset);
  right: var(--canvas-node--status-icons-offset);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._sticky_1jyj2_123 {
  position: relative;
  /**
   * State classes
   * The reverse order defines the priority in case multiple states are active
   */
}
._sticky_1jyj2_123._selected_1jyj2_130 {
  box-shadow: 0 0 0 4px var(--color-canvas-selected);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._addNodes_1qfrq_123 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
}
._addNodes_1qfrq_123:hover ._button_1qfrq_131 svg path {
  fill: var(--color-primary);
}
._button_1qfrq_131 {
  background: var(--color-foreground-xlight);
  border: 2px dashed var(--color-foreground-xdark);
  border-radius: 8px;
  padding: 0;
  min-width: 100px;
  min-height: 100px;
  cursor: pointer;
}
._button_1qfrq_131 svg {
  width: 26px !important;
  height: 40px;
}
._button_1qfrq_131 svg path {
  fill: var(--color-foreground-xdark);
}
._label_1qfrq_152 {
  width: max-content;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-m);
  line-height: var(--font-line-height-xloose);
  color: var(--color-text-dark);
  margin-top: var(--spacing-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_145fr_123 {
  display: flex;
  flex-direction: row;
}
._promptContainer_145fr_128 {
  --width: 620px;
  --height: 150px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xs);
  width: var(--width);
  height: var(--height);
  padding: 0;
  border: 1px solid var(--color-foreground-dark);
  background-color: var(--color-background-xlight);
  border-radius: var(--border-radius-base);
  overflow: hidden;
}
._promptContainer_145fr_128._focused_145fr_142 {
  border: 1px solid var(--color-primary);
}
._form_145fr_146 {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}
._form_textarea_145fr_153 {
  display: flex;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  border: 0;
}
._form_textarea_145fr_153 .el-textarea__inner {
  height: 100%;
  min-height: 0;
  overflow-y: auto;
  border: 0;
  background: transparent;
  resize: none;
  font-family: var(--font-family);
}
._form_footer_145fr_170 {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-2xs);
}
._or_145fr_176 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 100px;
  cursor: auto;
}
._or_text_145fr_185 {
  font-size: var(--font-size-m);
  color: var(--color-text-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._rectangle_rd2w6_123 {
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  background: var(--color-foreground-xdark);
}
._rectangle_rd2w6_123:hover {
  background: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_1v80z_123 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
._label_1v80z_130 {
  position: absolute;
  top: 50%;
  left: calc(var(--spacing-xs) * -1);
  transform: translate(-100%, -50%);
  font-size: var(--font-size-2xs);
  color: var(--color-foreground-xdark);
  background: var(--color-canvas-label-background);
  z-index: 1;
  text-align: center;
  white-space: nowrap;
}
._required_1v80z_143 ._label_1v80z_130::after {
  content: "*";
  color: var(--color-danger);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._wrapper_hyidj_123 {
  position: relative;
}
._wrapper_hyidj_123._secondary_hyidj_126 ._line_hyidj_126 {
  stroke: var(--node-type-supplemental-color);
}
._wrapper_hyidj_123._secondary_hyidj_126 ._plus_hyidj_129 path {
  fill: var(--node-type-supplemental-color);
}
._wrapper_hyidj_123._secondary_hyidj_126 ._plus_hyidj_129 rect {
  stroke: var(--node-type-supplemental-color);
}
._wrapper_hyidj_123._success_hyidj_135 ._line_hyidj_126 {
  stroke: var(--color-success);
}
._wrapper_hyidj_123 ._plus_hyidj_129:hover {
  cursor: pointer;
}
._wrapper_hyidj_123 ._plus_hyidj_129:hover path {
  fill: var(--color-primary);
}
._wrapper_hyidj_123 ._plus_hyidj_129:hover rect {
  stroke: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._dot_gdjps_123 {
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  border-radius: 50%;
  background: var(--color-foreground-xdark);
}
._dot_gdjps_123:hover {
  background: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_1u904_123 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
._handle_1u904_123._connected_1u904_129 ._label_1u904_129 {
  max-width: 96px;
}
._label_1u904_129 {
  position: absolute;
  background: var(--color-canvas-label-background);
  z-index: 1;
  max-width: calc(100% - var(--spacing-m) - 24px);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
._required_1u904_143 ._label_1u904_129::after {
  content: "*";
  color: var(--color-danger);
}
._outputLabel_1u904_148 {
  top: 50%;
  left: var(--spacing-m);
  transform: translate(0, -50%);
  font-size: var(--font-size-2xs);
  color: var(--color-foreground-xdark);
}
._runDataLabel_1u904_156 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -150%);
  font-size: var(--font-size-xs);
  color: var(--color-text-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.canvas-node-handle-main-output-enter-active,
.canvas-node-handle-main-output-leave-active {
  transform-origin: 0 center;
  transition-property: transform, opacity;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}
.canvas-node-handle-main-output-enter-from,
.canvas-node-handle-main-output-leave-to {
  transform: scale(0);
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._diamond_kuwdo_123 {
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  background: var(--node-type-supplemental-color);
  transform: rotate(45deg) scale(0.8);
}
._diamond_kuwdo_123:hover {
  background: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_3v1rt_123 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
._label_3v1rt_130 {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: var(--font-size-2xs);
  color: var(--node-type-supplemental-color);
  background: var(--color-canvas-label-background);
  z-index: 1;
  text-align: center;
  white-space: nowrap;
}
._required_3v1rt_143 ._label_3v1rt_130::after {
  content: "*";
  color: var(--color-danger);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.canvas-node-handle-non-main-input-enter-active,
.canvas-node-handle-non-main-input-leave-active {
  transform-origin: center 0;
  transition-property: transform, opacity;
  transition-duration: 0.2s;
  transition-timing-function: ease;
}
.canvas-node-handle-non-main-input-enter-from,
.canvas-node-handle-non-main-input-leave-to {
  transform: scale(0);
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_1x1ua_123 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
._label_1x1ua_130 {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: var(--font-size-2xs);
  color: var(--node-type-supplemental-color);
  background: var(--color-canvas-label-background);
  z-index: 0;
  white-space: nowrap;
}
._required_1x1ua_142 ._label_1x1ua_130::after {
  content: "*";
  color: var(--color-danger);
}
.vue-flow__handle:not(.connectionindicator) ._plus_1x1ua_147 {
  display: none;
  position: absolute;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._handle_5ceyz_123 {
  --handle--indicator--width: 16px;
  --handle--indicator--height: 16px;
  width: var(--handle--indicator--width);
  height: var(--handle--indicator--height);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 0;
  z-index: 1;
  background: transparent;
  border-radius: 0;
}
._handle_5ceyz_123._inputs_5ceyz_136._main_5ceyz_136 {
  --handle--indicator--width: 8px;
}
._renderType_5ceyz_140._top_5ceyz_140 {
  margin-bottom: calc(-1 * var(--handle--indicator--height));
  transform: translate(0%, -50%);
}
._renderType_5ceyz_140._right_5ceyz_144 {
  margin-left: calc(-1 * var(--handle--indicator--width));
  transform: translate(50%, 0%);
}
._renderType_5ceyz_140._left_5ceyz_148 {
  margin-right: calc(-1 * var(--handle--indicator--width));
  transform: translate(-50%, 0%);
}
._renderType_5ceyz_140._bottom_5ceyz_152 {
  margin-top: calc(-1 * var(--handle--indicator--height));
  transform: translate(0%, 50%);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_ze6b9_123 {
  z-index: -1;
  position: absolute;
  display: flex;
  align-items: center;
  height: 100%;
  right: 100%;
  top: 0;
  pointer-events: none;
}
._container_ze6b9_123 > div {
  position: relative;
  display: flex;
  align-items: center;
}
._container_ze6b9_123 button {
  margin-right: var(--spacing-s);
  opacity: 0;
  translate: -12px 0;
  transition: translate 0.1s ease-in, opacity 0.1s ease-in;
}
._container_ze6b9_123._interactive_ze6b9_144._hovered_ze6b9_144 button {
  opacity: 1;
  translate: 0 0;
  pointer-events: all;
}
._bolt_ze6b9_150 {
  position: absolute;
  right: 0;
  color: var(--color-primary);
  padding: var(--spacing-s);
  opacity: 1;
  translate: 0 0;
  transition: translate 0.1s ease-in, opacity 0.1s ease-in;
}
._container_ze6b9_123._interactive_ze6b9_144._hovered_ze6b9_144 ._bolt_ze6b9_150 {
  translate: -12px 0;
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvasNode_1ftik_123:hover:not(:has(> ._trigger_1ftik_123:hover)) ._canvasNodeToolbar_1ftik_123, ._canvasNode_1ftik_123:focus-within ._canvasNodeToolbar_1ftik_123, ._canvasNode_1ftik_123._showToolbar_1ftik_123 ._canvasNodeToolbar_1ftik_123 {
  opacity: 1;
}
._canvasNodeToolbar_1ftik_123 {
  transition: opacity 0.1s ease-in;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -100%);
  opacity: 0;
  z-index: 1;
}
._canvasNodeToolbar_1ftik_123:focus-within, ._canvasNodeToolbar_1ftik_123:hover {
  opacity: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._canvas_1fjji_123 {
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 300ms ease;
}
._canvas_1fjji_123._ready_1fjji_129 {
  opacity: 1;
}
._canvas_1fjji_123 .vue-flow__pane {
  cursor: grab;
}
._canvas_1fjji_123 .vue-flow__pane.selection {
  cursor: default;
}
._canvas_1fjji_123 .vue-flow__pane.dragging {
  cursor: grabbing;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.minimap-enter-active[data-v-af44edae],
.minimap-leave-active[data-v-af44edae] {
  transition: opacity 0.3s ease;
}
.minimap-enter-from[data-v-af44edae],
.minimap-leave-to[data-v-af44edae] {
  opacity: 0;
}