
.perks-list {
&[data-v-481d45c1] {
	margin: 0;
	padding: 0;
	list-style: none;
	display: flex;
	flex-direction: column;
	gap: var(--spacing-s);
}
> li[data-v-481d45c1] {
		display: flex;
		align-items: center;
		gap: var(--spacing-2xs);
}
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._insightsView_1j6u6_123 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
  overflow: auto;
}
._insightsContainer_1j6u6_131 {
  width: 100%;
  max-width: var(--content-container-width);
  padding: var(--spacing-l) var(--spacing-2xl);
  margin: 0 auto;
}
._insightsBanner_1j6u6_138 {
  padding-bottom: 0;
}
._insightsBanner_1j6u6_138 ul {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
._insightsContent_1j6u6_146 {
  padding: var(--spacing-l) 0;
  border: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
  border-top: 0;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  background: var(--color-background-xlight);
}
._insightsChartWrapper_1j6u6_155 {
  height: 292px;
  padding: 0 var(--spacing-l);
}
._insightsTableWrapper_1j6u6_160 {
  padding: var(--spacing-l) var(--spacing-l) 0;
}
._chartLoader_1j6u6_164 {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 9px;
}