/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._block_wafm4_123 {
  padding: var(--spacing-s) 0 var(--spacing-2xs) var(--spacing-2xs);
  border: 1px solid var(--color-foreground-light);
  margin-top: var(--spacing-s);
  border-radius: var(--border-radius-base);
}
:root ._blockContent_wafm4_130 {
  height: 0;
  overflow: hidden;
}
:root ._blockContent_wafm4_130._blockContentExpanded_wafm4_134 {
  height: auto;
}
._rawSwitch_wafm4_138 {
  opacity: 0;
  height: -moz-fit-content;
  height: fit-content;
  margin-left: auto;
  margin-right: var(--spacing-2xs);
}
._block_wafm4_123:hover ._rawSwitch_wafm4_138 {
  opacity: 1;
}
._blockHeader_wafm4_148 {
  display: flex;
  gap: var(--spacing-xs);
  cursor: pointer;
  /* This hack is needed to make the whole surface of header clickable  */
  margin: calc(-1 * var(--spacing-xs));
  padding: var(--spacing-2xs) var(--spacing-xs);
  align-items: center;
}
._blockHeader_wafm4_148 * {
  -webkit-user-select: none;
          user-select: none;
}
._blockTitle_wafm4_161 {
  font-size: var(--font-size-s);
  color: var(--color-text-dark);
  margin: 0;
  padding-bottom: var(--spacing-4xs);
}
._blockToggle_wafm4_168 {
  border: none;
  background: none;
  padding: 0;
  color: var(--color-text-base);
  margin-top: calc(-1 * var(--spacing-3xs));
}
._error_wafm4_176 {
  padding: var(--spacing-s) 0;
}
._container_dypaw_2 {
	padding: 0 var(--spacing-s) var(--spacing-s);
}
._nodeIcon_dypaw_5 {
	margin-top: calc(var(--spacing-3xs) * -1);
}
._header_dypaw_8 {
	display: flex;
	align-items: center;
	gap: var(--spacing-3xs);
	margin-bottom: var(--spacing-s);
}
._headerWrap_dypaw_14 {
	display: flex;
	flex-direction: column;
}
._title_dypaw_18 {
	display: flex;
	align-items: center;
	font-size: var(--font-size-s);
	gap: var(--spacing-3xs);
	color: var(--color-text-dark);
}
._meta_dypaw_25 {
	list-style: none;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	font-size: var(--font-size-xs);
& > li:not(:last-child) {
		border-right: 1px solid var(--color-text-base);
		padding-right: var(--spacing-3xs);
}
& > li:not(:first-child) {
		padding-left: var(--spacing-3xs);
}
}
._tokensUsage_dypaw_41 {
	display: flex;
	align-items: center;
	gap: var(--spacing-3xs);
}
/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._treeToggle_1qaq4_123 {
  border: none;
  background-color: transparent;
  padding: 0 var(--spacing-3xs);
  margin: 0 calc(-1 * var(--spacing-3xs));
  cursor: pointer;
}
._leafLabel_1qaq4_131 {
  display: flex;
  align-items: center;
  gap: var(--spacing-3xs);
}
._noData_1qaq4_137 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--color-text-light);
}
._empty_1qaq4_145 {
  padding: var(--spacing-l);
}
._title_1qaq4_149 {
  font-size: var(--font-size-s);
  margin-bottom: var(--spacing-xs);
}
._tree_1qaq4_123 {
  flex-shrink: 0;
  min-width: 8rem;
  height: 100%;
  padding-right: var(--spacing-xs);
  padding-left: var(--spacing-2xs);
}
._tree_1qaq4_123._slim_1qaq4_161 {
  min-width: auto;
}
._runData_1qaq4_165 {
  width: 100%;
  height: 100%;
  overflow: auto;
}
._container_1qaq4_171 {
  height: 100%;
  padding: 0 var(--spacing-xs);
  display: flex;
}
._container_1qaq4_171 .el-tree > .el-tree-node {
  position: relative;
}
._container_1qaq4_171 .el-tree > .el-tree-node:after {
  content: "";
  position: absolute;
  top: 2rem;
  bottom: 1.2rem;
  left: 0.75rem;
  width: 0.125rem;
  background-color: var(--color-foreground-base);
}
._container_1qaq4_171 .el-tree-node__expand-icon {
  display: none;
}
._container_1qaq4_171 .el-tree {
  margin-left: calc(-1 * var(--spacing-xs));
}
._container_1qaq4_171 .el-tree-node__content {
  margin-left: var(--spacing-xs);
}
._nodeIcon_1qaq4_198 {
  padding: var(--spacing-3xs) var(--spacing-3xs);
  border-radius: var(--border-radius-base);
  margin-right: var(--spacing-4xs);
}
._isSelected_1qaq4_204 {
  background-color: var(--color-foreground-base);
}
._treeNode_1qaq4_208 {
  display: inline-flex;
  border-radius: var(--border-radius-base);
  align-items: center;
  padding-right: var(--spacing-3xs);
  margin: var(--spacing-4xs) 0;
  font-size: var(--font-size-2xs);
  color: var(--color-text-dark);
  margin-bottom: var(--spacing-3xs);
  cursor: pointer;
}
._treeNode_1qaq4_208._isSelected_1qaq4_204 {
  font-weight: var(--font-weight-bold);
}
._treeNode_1qaq4_208:hover {
  background-color: var(--color-foreground-base);
}
._treeNode_1qaq4_208[data-tree-depth="0"] {
  margin-left: calc(-1 * var(--spacing-2xs));
}
._treeNode_1qaq4_208:after {
  content: "";
  position: absolute;
  margin: auto;
  background-color: var(--color-foreground-base);
  height: 0.125rem;
  left: 0.75rem;
  width: calc(var(--item-depth) * 0.625rem);
  margin-top: var(--spacing-3xs);
}