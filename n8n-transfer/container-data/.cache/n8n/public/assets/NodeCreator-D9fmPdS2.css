/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._searchContainer_1knv8_123 {
  display: flex;
  height: 40px;
  padding: 0 var(--spacing-xs);
  align-items: center;
  margin: var(--search-margin, var(--spacing-s));
  filter: drop-shadow(0px 2px 5px rgba(46, 46, 50, 0.04));
  border: 1px solid var(--color-foreground-base);
  background-color: var(--color-background-xlight);
  color: var(--color-text-light);
  border-radius: 4px;
}
._searchContainer_1knv8_123:focus-within {
  border-color: var(--color-secondary);
}
._prefix_1knv8_139 {
  text-align: center;
  font-size: var(--font-size-m);
  margin-right: var(--spacing-xs);
}
._prefix_1knv8_139._active_1knv8_144 {
  color: var(--color-primary) !important;
}
._text_1knv8_148 {
  flex-grow: 1;
}
._text_1knv8_148 input {
  width: 100%;
  border: none;
  outline: none;
  font-size: var(--font-size-s);
  -webkit-appearance: none;
          appearance: none;
  background-color: var(--color-background-xlight);
  color: var(--color-text-dark);
}
._text_1knv8_148 input::placeholder, ._text_1knv8_148 input::-webkit-input-placeholder {
  color: var(--color-text-light);
}
._suffix_1knv8_164 {
  min-width: 20px;
  text-align: right;
  display: inline-block;
}
._clear_1knv8_170 {
  background-color: transparent;
  padding: 0;
  border: none;
  cursor: pointer;
}
._clear_1knv8_170 svg path {
  fill: var(--color-text-light);
}
._clear_1knv8_170:hover svg path {
  fill: var(--color-text-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._nodeItem_1107w_123 {
  --trigger-icon-background-color: var(--color-background-xlight);
  --trigger-icon-border-color: var(--color-text-lighter);
  margin-left: 15px;
  margin-right: 12px;
  -webkit-user-select: none;
          user-select: none;
}
._nodeIcon_1107w_131 {
  z-index: 2;
}
._subNodeBackground_1107w_135 {
  background-color: var(--node-type-supplemental-background);
  border-radius: 50%;
  height: 40px;
  position: absolute;
  transform: translate(-7px, -7px);
  width: 40px;
  z-index: 1;
}
._communityNodeIcon_1107w_145 {
  vertical-align: top;
}
._draggable_1107w_149 {
  width: 100px;
  height: 100px;
  position: fixed;
  z-index: 1;
  opacity: 0.66;
  border: 2px solid var(--color-foreground-xdark);
  border-radius: var(--border-radius-large);
  background-color: var(--color-background-xlight);
  display: flex;
  justify-content: center;
  align-items: center;
}
._draggableDataTransfer_1107w_163 {
  width: 1px;
  height: 1px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._subCategory_1x5a1_123 {
  --action-arrow-color: var(--color-text-light);
  margin-left: 15px;
  margin-right: 12px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._label_p81gr_123 {
  margin-left: var(--spacing-s);
  margin-right: var(--spacing-s);
  margin-bottom: var(--spacing-4xs);
  letter-spacing: 1px;
  padding-top: var(--spacing-s);
  font-style: normal;
  font-weight: var(--font-weight-bold);
  font-size: 10px;
  line-height: 12px;
  text-transform: uppercase;
  color: var(--color-text-base);
  cursor: default;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._action_1owvg_123 {
  --node-creator-name-size: var(--font-size-2xs);
  --node-creator-name-weight: var(--font-weight-normal);
  --trigger-icon-background-color: var(--color-background-xlight);
  --trigger-icon-border-color: var(--color-text-lighter);
  --node-icon-size: 20px;
  --node-icon-margin-right: var(--spacing-xs);
  margin-left: var(--spacing-s);
  margin-right: var(--spacing-s);
  padding: var(--spacing-2xs) 0;
}
._nodeIcon_1owvg_135 {
  margin-right: var(--spacing-xs);
}
._draggable_1owvg_139 {
  width: 100px;
  height: 100px;
  position: fixed;
  z-index: 1;
  opacity: 0.66;
  border: 2px solid var(--color-foreground-xdark);
  border-radius: var(--border-radius-large);
  background-color: var(--color-background-xlight);
  display: flex;
  justify-content: center;
  align-items: center;
}
._draggableDataTransfer_1owvg_153 {
  width: 1px;
  height: 1px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._view_17lj9_123 {
  --action-arrow-color: var(--color-text-light);
  margin-left: var(--spacing-s);
  margin-right: var(--spacing-xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._creatorLink_cptk0_123 {
  --action-arrow-color: var(--color-text-light);
  margin-left: var(--spacing-s);
  margin-right: var(--spacing-xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._triggerIcon_1rqhf_123 {
  color: var(--color-primary);
  margin-left: var(--spacing-3xs);
}
._category_1rqhf_128 {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-bold);
  line-height: var(--font-line-height-compact);
  padding: var(--spacing-2xs) var(--spacing-s);
  border-bottom: 1px solid var(--color-foreground-base);
  display: flex;
  cursor: pointer;
  position: relative;
}
._category_1rqhf_128::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  border-left: 2px solid transparent;
}
._category_1rqhf_128:hover::before {
  border-color: var(--color-text-light);
}
._category_1rqhf_128._active_1rqhf_149::before {
  border-color: var(--color-primary);
}
._name_1rqhf_153 {
  flex-grow: 1;
  color: var(--color-text-dark);
}
._arrow_1rqhf_158 {
  font-size: var(--font-size-2xs);
  width: 12px;
  color: var(--color-text-light);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._mouseOverTooltip_5wyeb_123 {
  opacity: 0;
  margin-left: var(--spacing-3xs);
  color: var(--color-foreground-xdark);
}
._mouseOverTooltip_5wyeb_123:hover {
  color: var(--color-primary);
}
._categorizedItemsRenderer_5wyeb_131:hover ._mouseOverTooltip_5wyeb_123 {
  opacity: 1;
}
._tooltipPopper_5wyeb_135 {
  max-width: 260px;
}
._contentSlot_5wyeb_139 {
  padding: 0 var(--spacing-s) var(--spacing-3xs);
  margin-top: var(--spacing-xs);
}
._categorizedItemsRenderer_5wyeb_131 {
  padding-bottom: var(--spacing-s);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._itemSkeleton_e346c_123 {
  height: 50px;
}
._iteratorItem_e346c_127 {
  margin-left: 1px;
  position: relative;
}
._iteratorItem_e346c_127::before {
  content: "";
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  border-left: 2px solid transparent;
}
._iteratorItem_e346c_127:not(._label_e346c_139):not(._category_e346c_139):hover::before {
  border-color: var(--color-text-light);
}
._iteratorItem_e346c_127._active_e346c_142:not(._category_e346c_139)::before {
  border-color: var(--color-primary);
}
._empty_e346c_146 [role="alert"] {
  margin: var(--spacing-xs) var(--spacing-s);
}
._itemsRenderer_e346c_150 {
  display: flex;
  flex-direction: column;
  scrollbar-width: none; /* Firefox 64 */
}
._itemsRenderer_e346c_150 > *::-webkit-scrollbar {
  display: none;
}
._view_e346c_159 {
  position: relative;
}
._view_e346c_159:last-child {
  margin-top: var(--spacing-s);
  padding-top: var(--spacing-xs);
}
._view_e346c_159:last-child:after {
  content: "";
  position: absolute;
  left: var(--spacing-s);
  right: var(--spacing-s);
  top: 0;
  margin: auto;
  bottom: 0;
  border-top: 1px solid var(--color-foreground-base);
}
._link_e346c_177 {
  position: relative;
}
._link_e346c_177:last-child {
  margin-bottom: var(--spacing-s);
  padding-bottom: var(--spacing-xs);
}
._link_e346c_177:last-child:after {
  content: "";
  position: absolute;
  left: var(--spacing-s);
  right: var(--spacing-s);
  top: 0;
  margin: auto;
  bottom: 0;
  border-bottom: 1px solid var(--color-foreground-base);
}
._borderless_e346c_195:last-child {
  margin-top: 0;
  padding-top: 0;
}
._borderless_e346c_195:last-child:after {
  content: none;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_169mw_123 {
  display: flex;
  flex-direction: column;
  padding-bottom: var(--spacing-3xl);
}
._resetSearch_169mw_129 {
  cursor: pointer;
  line-height: var(--font-line-height-regular);
  font-weight: var(--font-weight-regular);
  font-size: var(--font-size-2xs);
  padding: var(--spacing-2xs) var(--spacing-s) 0;
  color: var(--color-text-base);
}
._resetSearch_169mw_129 i {
  font-weight: var(--font-weight-bold);
  font-style: normal;
  text-decoration: underline;
}
._actionsEmpty_169mw_143 {
  padding: var(--spacing-2xs) var(--spacing-xs) var(--spacing-s);
  font-weight: var(--font-weight-regular);
}
._actionsEmpty_169mw_143 strong {
  font-weight: var(--font-weight-bold);
}
._apiHint_169mw_151 {
  padding: 0 var(--spacing-s) var(--spacing-xl);
  font-size: var(--font-size-2xs);
  color: var(--color-text-base);
  line-height: var(--font-line-height-regular);
  z-index: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._noResults_17xen_123 {
  background-color: var(--color-background-xlight);
  text-align: center;
  height: 100%;
  border-left: 1px solid var(--color-foreground-base);
  flex-direction: column;
  font-weight: var(--font-weight-regular);
  display: flex;
  align-items: center;
  align-content: center;
  padding: 0 var(--spacing-2xl);
}
._title_17xen_136 {
  font-size: var(--font-size-m);
  line-height: var(--font-line-height-regular);
  margin-top: var(--spacing-xs);
}
._title_17xen_136 div {
  margin-bottom: var(--spacing-s);
}
._action_17xen_145 p,
._request_17xen_146 p {
  font-size: var(--font-size-s);
  line-height: var(--font-line-height-xloose);
}
._request_17xen_146 {
  position: fixed;
  bottom: var(--spacing-m);
  display: none;
}
@media (min-height: 550px) {
._request_17xen_146 {
    display: block;
}
}
._icon_17xen_162 {
  margin-top: var(--spacing-2xl);
  min-height: 67px;
  opacity: 0.6;
}
._external_17xen_168 {
  font-size: var(--font-size-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._items_1i9xd_123 {
  margin-bottom: var(--spacing-s);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.panel-slide-in-leave-active,
.panel-slide-in-enter-active,
.panel-slide-out-leave-active,
.panel-slide-out-enter-active {
  transition: transform 200ms ease;
  position: absolute;
  left: 0;
  right: 0;
}
.panel-slide-out-enter-from,
.panel-slide-in-leave-to {
  transform: translateX(0);
  z-index: -1;
}
.panel-slide-out-leave-to,
.panel-slide-in-enter-from {
  transform: translateX(100%);
  z-index: 1;
}
._info_16r9i_145 {
  margin: var(--spacing-2xs) var(--spacing-s);
}
._backButton_16r9i_149 {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0 var(--spacing-xs) 0 0;
}
._backButtonIcon_16r9i_156 {
  color: var(--color-text-light);
  height: 16px;
  padding: 0;
}
._nodeIcon_16r9i_162 {
  --node-icon-size: 20px;
  margin-right: var(--spacing-s);
}
._renderedItems_16r9i_167 {
  overflow: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  scrollbar-width: none; /* Firefox 64 */
  padding-bottom: var(--spacing-xl);
}
._renderedItems_16r9i_167::-webkit-scrollbar {
  display: none;
}
._searchBar_16r9i_179 {
  flex-shrink: 0;
}
._nodesListPanel_16r9i_183 {
  background: var(--color-background-xlight);
  height: 100%;
  background-color: var(--color-background-xlight);
  --color-background-node-icon-badge: var(--color-background-xlight);
  width: var(--node-creator-width);
  display: flex;
  flex-direction: column;
}
._nodesListPanel_16r9i_183:before {
  box-sizing: border-box;
  content: "";
  border-left: 1px solid var(--color-foreground-base);
  width: 1px;
  position: absolute;
  height: 100%;
}
._footer_16r9i_201 {
  font-size: var(--font-size-2xs);
  color: var(--color-text-base);
  margin: 0 var(--spacing-xs) 0;
  padding: var(--spacing-4xs) 0;
  line-height: var(--font-line-height-regular);
  border-top: 1px solid var(--color-foreground-base);
  z-index: 1;
  margin-top: -1px;
}
._top_16r9i_212 {
  display: flex;
  align-items: center;
}
._header_16r9i_217 {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  line-height: var(--font-line-height-compact);
  padding: var(--spacing-s) var(--spacing-s);
}
._header_16r9i_217._hasBg_16r9i_223 {
  border-bottom: var(--color-foreground-base) solid 1px;
  background-color: var(--color-background-base);
}
._title_16r9i_228 {
  line-height: 24px;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-l);
  margin: 0;
}
._hasBg_16r9i_223 ._title_16r9i_228 {
  font-size: var(--font-size-s-m);
  line-height: 22px;
}
._subtitle_16r9i_239 {
  margin-top: var(--spacing-4xs);
  font-size: var(--font-size-s);
  line-height: 19px;
  color: var(--color-text-base);
  font-weight: var(--font-weight-regular);
}
._offsetSubtitle_16r9i_247 {
  margin-left: calc(var(--spacing-xl) + var(--spacing-4xs));
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.nodes-list-panel-ai_chain .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_chain-color);
}
.nodes-list-panel-ai_document .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_document-color);
}
.nodes-list-panel-ai_embedding .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_embedding-color);
}
.nodes-list-panel-ai_languageModel .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_languageModel-color);
}
.nodes-list-panel-ai_memory .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_memory-color);
}
.nodes-list-panel-ai_outputParser .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_outputParser-color);
}
.nodes-list-panel-ai_tool .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_tool-color);
}
.nodes-list-panel-ai_retriever .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_retriever-color);
}
.nodes-list-panel-ai_textSplitter .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_textSplitter-color);
}
.nodes-list-panel-ai_vectorRetriever .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_vectorRetriever-color);
}
.nodes-list-panel-ai_vectorStore .nodes-list-panel-header .n8n-node-icon svg {
  color: var(--node-type-ai_vectorStore-color);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
strong {
  font-weight: var(--font-weight-bold);
}
._nodeCreator_xjjfv_127 {
  --node-creator-width: 385px;
  --node-icon-color: var(--color-text-base);
  position: fixed;
  top: calc(var(--header-height) * 1px);
  bottom: 0;
  right: 0;
  z-index: var(--z-index-node-creator);
  width: var(--node-creator-width);
  color: var(--color-text-dark);
}
._nodeCreatorScrim_xjjfv_139 {
  position: fixed;
  top: calc(var(--header-height) * 1px);
  right: 0;
  bottom: 0;
  left: 65px;
  opacity: 0;
  z-index: 1;
  background: var(--color-dialog-overlay-background);
  pointer-events: none;
  transition: opacity 200ms ease-in-out;
}
._nodeCreatorScrim_xjjfv_139._active_xjjfv_151 {
  opacity: 0.7;
}
._close_xjjfv_155 {
  position: absolute;
  z-index: calc(var(--z-index-node-creator) + 1);
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  background: transparent;
  border: 0;
  display: none;
}
@media screen and (max-width: 450px) {
._nodeCreator_xjjfv_127 {
    --node-creator-width: calc(100vw - 65px);
}
._close_xjjfv_155 {
    display: inline-flex;
}
}