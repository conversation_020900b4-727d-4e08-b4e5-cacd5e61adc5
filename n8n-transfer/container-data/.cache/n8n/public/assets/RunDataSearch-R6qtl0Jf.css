/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._ioSearch_vnmep_123 {
  transition: max-width 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}
._ioSearch_vnmep_123 ._ioSearchIcon_vnmep_126 {
  color: var(--color-foreground-xdark);
  cursor: pointer;
}
._ioSearch_vnmep_123 .el-input__prefix {
  left: 8px;
}
._ioSearch_vnmep_123.el-input--prefix .el-input__inner {
  padding-left: 30px;
}
._ioSearch_vnmep_123 input {
  border: 0;
  opacity: 0;
  background: transparent;
  cursor: pointer;
}
._ioSearchOpened_vnmep_143 ._ioSearchIcon_vnmep_126 {
  cursor: default;
}
._ioSearchOpened_vnmep_143 input {
  border: var(--input-border-color, var(--border-color-base)) var(--input-border-style, var(--border-style-base)) var(--border-width-base);
  background: var(--input-background-color, var(--color-foreground-xlight));
  opacity: 1;
  cursor: text;
}