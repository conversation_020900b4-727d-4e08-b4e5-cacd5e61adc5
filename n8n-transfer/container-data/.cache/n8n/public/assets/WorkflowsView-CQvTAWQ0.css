/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._card_pt4ir_123 {
  transition: box-shadow 0.3s ease;
  cursor: pointer;
}
._card_pt4ir_123:hover {
  box-shadow: 0 2px 8px rgba(68, 28, 23, 0.1);
}
._folder-icon_pt4ir_131 {
  width: var(--spacing-xl);
  height: var(--spacing-xl);
  flex-shrink: 0;
  color: var(--color-text-base);
  align-content: center;
  text-align: center;
}
._card-header_pt4ir_140 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: var(--spacing-xs);
  margin-bottom: var(--spacing-5xs);
}
._card-footer_pt4ir_148 {
  display: flex;
}
._info-cell_pt4ir_152 + ._info-cell_pt4ir_152::before {
  content: "|";
  margin: 0 var(--spacing-4xs);
}
._cardBadge_pt4ir_157._with-breadcrumbs_pt4ir_157 .n8n-badge {
  padding-right: 0;
}
._cardBadge_pt4ir_157._with-breadcrumbs_pt4ir_157 .n8n-breadcrumbs {
  padding-left: var(--spacing-5xs);
}
._card-actions_pt4ir_164 {
  display: flex;
  gap: var(--spacing-xs);
}
@media screen and (max-width: 991px) {
._card_pt4ir_123 {
    flex-wrap: wrap;
}
._card_pt4ir_123 .n8n-card-append {
    width: 100%;
    margin-top: var(--spacing-3xs);
    padding-left: 40px;
}
._card_pt4ir_123 ._card-actions_pt4ir_164 {
    width: 100%;
    justify-content: space-between;
}
._info-cell--created_pt4ir_182 {
    display: none;
}
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._cardLink_15m1t_123 {
  transition: box-shadow 0.3s ease;
  cursor: pointer;
  padding: 0;
  align-items: stretch;
}
._cardLink_15m1t_123:hover {
  box-shadow: 0 2px 8px rgba(68, 28, 23, 0.1);
}
._cardHeading_15m1t_133 {
  font-size: var(--font-size-s);
  word-break: break-word;
  padding: var(--spacing-s) 0 0 var(--spacing-s);
}
._cardHeading_15m1t_133 span {
  color: var(--color-text-light);
}
._cardHeadingArchived_15m1t_142 {
  color: var(--color-text-light);
}
._cardDescription_15m1t_146 {
  min-height: 19px;
  display: flex;
  align-items: center;
  padding: 0 0 var(--spacing-s) var(--spacing-s);
}
._cardActions_15m1t_153 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  padding: 0 var(--spacing-s) 0 0;
  cursor: default;
}
._cardBadge_15m1t_163 {
  background-color: var(--color-background-xlight);
}
._cardBadge_15m1t_163._with-breadcrumbs_15m1t_167 .n8n-badge {
  padding-right: 0;
}
._cardBadge_15m1t_163._with-breadcrumbs_15m1t_167 .n8n-breadcrumbs {
  padding-left: var(--spacing-5xs);
}
._cardArchived_15m1t_174 {
  background-color: var(--color-background-light);
  border-color: var(--color-foreground-light);
  color: var(--color-text-base);
}
@media screen and (max-width: 991px) {
._cardLink_15m1t_123 {
    --card--padding: 0 var(--spacing-s) var(--spacing-s);
    --card--append--width: 100%;
    flex-direction: column;
}
._cardActions_15m1t_153 {
    width: 100%;
    padding: 0 var(--spacing-s) var(--spacing-s);
}
._cardBadge_15m1t_163,
  ._breadcrumbs_15m1t_191 {
    margin-right: auto;
}
}
@media screen and (max-width: 767px) {
._breadcrumbs_15m1t_191 > div {
    flex-direction: column;
}
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._actionsContainer_6309i_123 {
  display: flex;
  justify-content: center;
}
._easy-ai-workflow-callout_6309i_128 {
  margin-top: var(--spacing-xs);
  padding-left: var(--spacing-s);
  padding-right: var(--spacing-m);
}
._easy-ai-workflow-callout_6309i_128 ._callout-trailing-content_6309i_133 {
  display: flex;
  align-items: center;
  gap: var(--spacing-m);
}
._emptyStateCard_6309i_139 {
  width: 192px;
  text-align: center;
  display: inline-flex;
  height: 230px;
}
._emptyStateCard_6309i_139 + ._emptyStateCard_6309i_139 {
  margin-left: var(--spacing-s);
}
._emptyStateCard_6309i_139:hover svg {
  color: var(--color-primary);
}
._emptyStateCardIcon_6309i_152 {
  font-size: 48px;
}
._emptyStateCardIcon_6309i_152 svg {
  width: 48px !important;
  color: var(--color-foreground-dark);
  transition: color 0.3s ease;
}
._add-folder-button_6309i_161 {
  width: 30px;
  height: 30px;
}
._breadcrumbs-container_6309i_166 {
  display: flex;
  align-items: center;
  align-self: flex-end;
}
._breadcrumbs-loading_6309i_172 .el-skeleton__item {
  margin: 0;
  height: 40px;
  width: 400px;
}
._empty-folder-container_6309i_178 button {
  margin-top: var(--spacing-2xs);
}
._drag-active_6309i_182 *,
._drag-active_6309i_182 .action-toggle {
  cursor: grabbing !important;
}
._dragging_6309i_187 {
  transition: opacity 0.3s ease;
  opacity: 0.3;
  border-style: dashed;
  pointer-events: none;
}
._drop-active_6309i_194 .card {
  border-color: var(--color-secondary);
  background-color: var(--color-callout-secondary-background);
}
._path-separator_6309i_199 {
  font-size: var(--font-size-xl);
  color: var(--color-foreground-base);
  margin: var(--spacing-4xs);
}
._name_6309i_205 {
  color: var(--color-text-dark);
  font-size: var(--font-size-s);
}
._pointer-disabled_6309i_210 {
  pointer-events: none;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.add-folder-modal {
  width: 500px;
  padding-bottom: 0;
}
.add-folder-modal .el-message-box__message {
  font-size: var(--font-size-xl);
}
.add-folder-modal .el-message-box__btns {
  padding: 0 var(--spacing-l) var(--spacing-l);
}
.add-folder-modal .el-message-box__content {
  padding: var(--spacing-l);
}