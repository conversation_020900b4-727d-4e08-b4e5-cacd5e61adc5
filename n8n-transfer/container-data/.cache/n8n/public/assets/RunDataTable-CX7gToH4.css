/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._dataDisplay_1pj10_123 {
  position: absolute;
  top: 0;
  left: 0;
  padding-left: var(--spacing-s);
  right: 0;
  overflow-y: auto;
  line-height: 1.5;
  word-break: normal;
  height: 100%;
  padding-bottom: var(--spacing-3xl);
}
._table_1pj10_136 {
  border-collapse: separate;
  text-align: left;
  width: 100%;
  font-size: var(--font-size-s);
}
._table_1pj10_136 th {
  background-color: var(--color-background-base);
  border-top: var(--border-base);
  border-bottom: var(--border-base);
  border-left: var(--border-base);
  position: sticky;
  top: 0;
  color: var(--color-text-dark);
  z-index: 1;
}
._table_1pj10_136 td {
  vertical-align: top;
  padding: var(--spacing-2xs) var(--spacing-2xs) var(--spacing-2xs) var(--spacing-3xs);
  border-bottom: var(--border-base);
  border-left: var(--border-base);
  overflow-wrap: break-word;
  white-space: pre-wrap;
  vertical-align: top;
}
._table_1pj10_136 td:first-child,
._table_1pj10_136 td:nth-last-child(2) {
  position: relative;
  z-index: 0;
}
._table_1pj10_136 td:first-child:after,
._table_1pj10_136 td:nth-last-child(2):after {
  content: "";
  position: absolute;
  height: 100%;
  width: 2px;
  top: 0;
}
._table_1pj10_136 td:nth-last-child(2):after {
  right: -1px;
}
._table_1pj10_136 td:first-child:after {
  left: -1px;
}
._table_1pj10_136 th:last-child,
._table_1pj10_136 td:last-child {
  border-right: var(--border-base);
}
._nodeClass_1pj10_185 {
  margin-bottom: var(--spacing-5xs);
}
._emptyCell_1pj10_189 {
  height: 32px;
}
._header_1pj10_193 {
  display: flex;
  align-items: center;
  padding: var(--spacing-2xs);
}
._header_1pj10_193 span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  flex-grow: 1;
}
._draggableHeader_1pj10_205:hover {
  cursor: grab;
  background-color: var(--color-foreground-base);
}
._draggableHeader_1pj10_205:hover ._dragButton_1pj10_209 {
  opacity: 1;
}
._highlight_1pj10_213 ._draggableHeader_1pj10_205 {
  color: var(--color-primary);
}
._draggingHeader_1pj10_217 {
  color: var(--color-primary);
  background-color: var(--color-primary-tint-2);
}
._activeHeader_1pj10_222 ._dragButton_1pj10_209 {
  opacity: 1;
}
._dragButton_1pj10_209 {
  opacity: 0;
  margin-left: var(--spacing-2xs);
}
._dataKey_1pj10_231 {
  color: var(--color-text-dark);
  line-height: 1.7;
  font-weight: var(--font-weight-bold);
  border-radius: var(--border-radius-base);
  padding: 0 var(--spacing-5xs) 0 var(--spacing-5xs);
  margin-right: var(--spacing-5xs);
}
._value_1pj10_240 {
  line-height: var(--font-line-height-regular);
}
._nestedValue_1pj10_244 {
  margin-left: var(--spacing-4xs);
}
._mappable_1pj10_249 {
  cursor: grab;
}
._empty_1pj10_189 {
  color: var(--color-danger);
}
._limitColWidth_1pj10_257 {
  max-width: 300px;
}
._minColWidth_1pj10_261 {
  min-width: 240px;
}
._hoveringKey_1pj10_265 {
  background-color: var(--color-foreground-base);
}
._draggingKey_1pj10_269 {
  background-color: var(--color-primary-tint-2);
}
._tableRightMargin_1pj10_273 {
  background-color: var(--color-background-base) !important;
  width: var(--spacing-s);
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
}
._hoveringRow_1pj10_281 td:first-child:after,
._hoveringRow_1pj10_281 td:nth-last-child(2):after {
  background-color: var(--color-secondary);
}
._warningTooltip_1pj10_286 {
  color: var(--color-warning);
}
._executionLinkCell_1pj10_290 {
  padding: var(--spacing-3xs) !important;
}
._executionLinkRowHeader_1pj10_294 {
  width: var(--spacing-m);
}