/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._jsonDisplay_1rfof_123 {
  position: absolute;
  top: 0;
  left: 0;
  padding-left: var(--spacing-s);
  right: 0;
  overflow-y: hidden;
  line-height: 1.5;
  word-break: normal;
  height: 100%;
}
._jsonDisplay_1rfof_123:hover {
  /* Shows .actionsGroup element from <run-data-json-actions /> child component */
}
._jsonDisplay_1rfof_123:hover > div:first-child {
  opacity: 1;
}
._jsonDisplay_1rfof_123 ._mappable_1rfof_140 {
  cursor: grab;
}
._jsonDisplay_1rfof_123 ._mappable_1rfof_140:hover {
  background-color: var(--color-json-highlight);
}
._jsonDisplay_1rfof_123._highlight_1rfof_146 ._mappable_1rfof_140, ._jsonDisplay_1rfof_123._highlight_1rfof_146 ._mappable_1rfof_140:hover,
._jsonDisplay_1rfof_123 ._dragged_1rfof_147,
._jsonDisplay_1rfof_123 ._dragged_1rfof_147:hover {
  background-color: var(--color-primary-tint-2);
  color: var(--color-primary);
}
._jsonDisplay_1rfof_123._compact_1rfof_152 {
  padding-left: var(--spacing-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.vjs-tree {
  color: var(--color-json-default);
  --color-line-break: var(--color-code-line-break);
  font-size: var(--font-size-2xs);
}
.vjs-tree-node:hover {
  background-color: transparent;
}
.vjs-tree-node.is-highlight {
  background-color: var(--color-json-highlight);
}
.vjs-key > span,
.vjs-value > span {
  color: var(--color-text-dark);
  line-height: 1.7;
  border-radius: var(--border-radius-base);
}
.vjs-value > span {
  padding: 0 var(--spacing-5xs) 0 var(--spacing-5xs);
  margin-left: var(--spacing-5xs);
  white-space: pre-wrap;
}
.vjs-tree .vjs-value-null,
.vjs-tree .vjs-value-null span {
  color: var(--color-json-null);
}
.vjs-tree .vjs-value-boolean,
.vjs-tree .vjs-value-boolean span {
  color: var(--color-json-boolean);
}
.vjs-tree .vjs-value-number,
.vjs-tree .vjs-value-number span {
  color: var(--color-json-number);
}
.vjs-tree .vjs-value-string,
.vjs-tree .vjs-value-string span {
  color: var(--color-json-string);
}
.vjs-tree .vjs-key {
  color: var(--color-json-key);
}
.vjs-tree .vjs-tree__brackets {
  color: var(--color-json-brackets);
}
.vjs-tree .vjs-tree__brackets:hover {
  color: var(--color-json-brackets-hover);
}
.vjs-tree .vjs-tree__content.has-line {
  border-left: 1px dotted var(--color-json-line);
}
.vjs-tree .vjs-tree-list-holder-inner {
  padding-bottom: var(--spacing-3xl);
}