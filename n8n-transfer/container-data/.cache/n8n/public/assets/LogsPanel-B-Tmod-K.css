/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_1uwbw_123 {
  font-size: var(--font-size-2xs);
  text-align: left;
  padding-inline-start: var(--spacing-s);
  padding-inline-end: var(--spacing-2xs);
  padding-block: var(--spacing-2xs);
  background-color: var(--color-foreground-xlight);
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: var(--font-line-height-compact);
}
._container_1uwbw_123:last-child {
  /** Panel collapsed */
  cursor: pointer;
}
._container_1uwbw_123:not(:last-child) {
  /** Panel open */
  border-bottom: var(--border-base);
}
._title_1uwbw_144 {
  flex-grow: 1;
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._actions_1uwbw_152 {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  color: var(--color-text-base);
  max-width: 70%;
  /* Let button heights not affect the header height */
  margin-block: calc(-1 * var(--spacing-s));
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._name_1t0q3_123 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._name_1t0q3_123 del:not(:last-child) {
  margin-right: var(--spacing-4xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_6ygvb_123 {
  display: flex;
  align-items: center;
  justify-content: stretch;
  overflow: hidden;
  position: relative;
  z-index: 1;
  padding-inline-end: var(--spacing-5xs);
  cursor: pointer;
}
._container_6ygvb_123 > * {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: var(--spacing-2xs);
}
._background_6ygvb_140 {
  position: absolute;
  left: calc(var(--indent-depth) * 32px);
  top: 0;
  width: calc(100% - var(--indent-depth) * 32px);
  height: 100%;
  border-radius: var(--border-radius-base);
  z-index: -1;
}
._selected_6ygvb_149 ._background_6ygvb_140 {
  background-color: var(--color-foreground-base);
}
._container_6ygvb_123:hover:not(._selected_6ygvb_149) ._background_6ygvb_140 {
  background-color: var(--color-background-light-base);
}
._selected_6ygvb_149:not(:hover)._error_6ygvb_155 ._background_6ygvb_140 {
  background-color: var(--color-callout-danger-background);
}
._indent_6ygvb_159 {
  flex-grow: 0;
  flex-shrink: 0;
  width: var(--spacing-xl);
  align-self: stretch;
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}
._indent_6ygvb_159._connectorCurved_6ygvb_168:before {
  content: "";
  position: absolute;
  left: var(--spacing-s);
  bottom: var(--spacing-s);
  border: 2px solid var(--color-canvas-dot);
  width: var(--spacing-l);
  height: var(--spacing-l);
  border-radius: var(--border-radius-large);
}
._indent_6ygvb_159._connectorStraight_6ygvb_178:after {
  content: "";
  position: absolute;
  left: var(--spacing-s);
  top: 0;
  border-left: 2px solid var(--color-canvas-dot);
  height: 100%;
}
._icon_6ygvb_187 {
  margin-left: var(--row-gap-thickness);
  flex-grow: 0;
  flex-shrink: 0;
}
._name_6ygvb_193 {
  flex-basis: 0;
  flex-grow: 1;
  padding-inline-start: 0;
}
._timeTook_6ygvb_199 {
  flex-grow: 0;
  flex-shrink: 0;
  width: 20%;
}
._timeTook_6ygvb_199 ._statusTextIcon_6ygvb_204 {
  margin-right: var(--spacing-5xs);
  vertical-align: text-bottom;
}
._startedAt_6ygvb_209 {
  flex-grow: 0;
  flex-shrink: 0;
  width: 25%;
}
._consumedTokens_6ygvb_215 {
  flex-grow: 0;
  flex-shrink: 0;
  width: 15%;
  text-align: right;
}
._compactErrorIcon_6ygvb_222 {
  flex-grow: 0;
  flex-shrink: 0;
  width: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}
._container_6ygvb_123:hover ._compactErrorIcon_6ygvb_222 {
  display: none;
}
._partialExecutionButton_6ygvb_234,
._openNdvButton_6ygvb_235 {
  transition: none;
  /* By default, take space but keep invisible */
  visibility: hidden;
}
._container_6ygvb_123._compact_6ygvb_222 ._partialExecutionButton_6ygvb_234,
._container_6ygvb_123._compact_6ygvb_222 ._openNdvButton_6ygvb_235 {
  /* When compact, collapse to save space */
  display: none;
}
._container_6ygvb_123:hover ._partialExecutionButton_6ygvb_234:not(._unavailable_6ygvb_245),
._container_6ygvb_123:hover ._openNdvButton_6ygvb_235:not(._unavailable_6ygvb_245) {
  visibility: visible;
  display: inline-flex;
}
._partialExecutionButton_6ygvb_234,
._openNdvButton_6ygvb_235,
._toggleButton_6ygvb_253 {
  flex-grow: 0;
  flex-shrink: 0;
  border: none;
  background: transparent;
  color: var(--color-text-base);
  align-items: center;
  justify-content: center;
}
._partialExecutionButton_6ygvb_234:hover,
._openNdvButton_6ygvb_235:hover,
._toggleButton_6ygvb_253:hover {
  background: transparent;
}
._partialExecutionButton_6ygvb_234:disabled,
._openNdvButton_6ygvb_235:disabled,
._toggleButton_6ygvb_253:disabled {
  visibility: hidden !important;
}
._toggleButton_6ygvb_253 {
  display: inline-flex;
}
._statusIcon_6ygvb_277 {
  color: var(--color-text-light);
  flex-grow: 0;
  flex-shrink: 0;
  width: 26px;
  height: 26px;
  padding: var(--spacing-3xs);
}
._statusIcon_6ygvb_277._placeholder_6ygvb_285 {
  color: transparent;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_pt5hk_123 {
  display: flex;
  align-items: center;
}
._container_pt5hk_123 > * {
  padding-inline: var(--spacing-2xs);
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
._container_pt5hk_123 > *:not(:last-child) {
  border-right: var(--border-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_pydb1_123 {
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
  background-color: var(--color-foreground-xlight);
}
._clearButton_pydb1_133 {
  border: none;
  color: var(--color-text-light);
  gap: var(--spacing-5xs);
}
._content_pydb1_139 {
  position: relative;
  flex-grow: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: stretch;
  padding-right: var(--spacing-5xs);
}
._content_pydb1_139._empty_pydb1_149 {
  align-items: center;
  justify-content: center;
}
._emptyText_pydb1_154 {
  max-width: 20em;
  text-align: center;
}
._summary_pydb1_159 {
  padding: var(--spacing-2xs);
}
._tree_pydb1_163 {
  padding: 0 var(--spacing-2xs) var(--spacing-2xs) var(--spacing-2xs);
  /* For programmatically triggered scroll in useVirtualList to animate, make it scroll smoothly */
  scroll-behavior: smooth;
}
._container_pydb1_123:not(._staticScrollBar_pydb1_168) ._tree_pydb1_163 {
  scroll-padding-block: var(--spacing-3xs);
}
@supports not (selector(::-webkit-scrollbar)) {
._container_pydb1_123:not(._staticScrollBar_pydb1_168) ._tree_pydb1_163 {
    scrollbar-width: thin;
}
}
@supports selector(::-webkit-scrollbar) {
._container_pydb1_123:not(._staticScrollBar_pydb1_168) ._tree_pydb1_163 {
    padding-right: var(--spacing-5xs);
    scrollbar-gutter: stable;
}
._container_pydb1_123:not(._staticScrollBar_pydb1_168) ._tree_pydb1_163::-webkit-scrollbar {
    width: var(--spacing-4xs);
}
._container_pydb1_123:not(._staticScrollBar_pydb1_168) ._tree_pydb1_163::-webkit-scrollbar-thumb {
    border-radius: var(--spacing-4xs);
    background: var(--color-foreground-dark);
}
}
._tree_pydb1_163 .el-icon {
  display: none;
}
._switchViewButtons_pydb1_193 {
  position: absolute;
  z-index: 10; /* higher than log entry rows background */
  right: 0;
  top: 0;
  margin: var(--spacing-4xs) var(--spacing-2xs);
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}
._content_pydb1_139:hover ._switchViewButtons_pydb1_193 {
  visibility: visible;
  opacity: 1;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-file[data-v-70b9370d] {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 15rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  gap: 0.25rem;
  font-size: 0.75rem;
  background: white;
  color: var(--chat--color-dark);
  border: 1px solid var(--chat--color-dark);
  cursor: pointer;
}
.chat-file-name-tooltip[data-v-70b9370d] {
  overflow: hidden;
}
.chat-file-name[data-v-70b9370d] {
  overflow: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
}
.chat-file-delete[data-v-70b9370d],
.chat-file-preview[data-v-70b9370d] {
  background: none;
  border: none;
  display: block;
  cursor: pointer;
  flex-shrink: 0;
}
.chat-file-delete[data-v-70b9370d] {
  position: relative;
  /* Increase hit area for better clickability */
}
.chat-file-delete[data-v-70b9370d]:hover {
  color: red;
}
.chat-file-delete[data-v-70b9370d]:before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  bottom: -10px;
  left: -10px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-message {
  display: block;
  position: relative;
  max-width: -moz-fit-content;
  max-width: fit-content;
  font-size: var(--chat--message--font-size);
  padding: var(--chat--message--padding);
  border-radius: var(--chat--message--border-radius);
  scroll-margin: 3rem;
}
.chat-message .chat-message-actions {
  position: absolute;
  bottom: calc(100% - 0.5rem);
  left: 0;
  opacity: 0;
  transform: translateY(-0.25rem);
  display: flex;
  gap: 1rem;
}
.chat-message.chat-message-from-user .chat-message-actions {
  left: auto;
  right: 0;
}
.chat-message:hover .chat-message-actions {
  opacity: 1;
}
.chat-message p {
  line-height: var(--chat--message-line-height);
  word-wrap: break-word;
}
.chat-message + .chat-message {
  margin-top: var(--chat--message--margin-bottom);
}
.chat-message.chat-message-from-user + .chat-message.chat-message-from-bot, .chat-message.chat-message-from-bot + .chat-message.chat-message-from-user {
  margin-top: var(--chat--spacing);
}
.chat-message.chat-message-from-bot {
  color: var(--chat--message--bot--color);
  border-bottom-left-radius: 0;
}
.chat-message.chat-message-from-bot:not(.chat-message-transparent) {
  background-color: var(--chat--message--bot--background);
  border: var(--chat--message--bot--border);
}
.chat-message.chat-message-from-user {
  color: var(--chat--message--user--color);
  margin-left: auto;
  border-bottom-right-radius: 0;
}
.chat-message.chat-message-from-user:not(.chat-message-transparent) {
  background-color: var(--chat--message--user--background);
  border: var(--chat--message--user--border);
}
.chat-message > .chat-message-markdown {
  display: block;
  box-sizing: border-box;
  font-size: inherit;
}
.chat-message > .chat-message-markdown > *:first-child {
  margin-top: 0;
}
.chat-message > .chat-message-markdown > *:last-child {
  margin-bottom: 0;
}
.chat-message > .chat-message-markdown pre {
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre-wrap;
  box-sizing: border-box;
  padding: var(--chat--spacing);
  background: var(--chat--message--pre--background);
  border-radius: var(--chat--border-radius);
}
.chat-message .chat-message-files {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  padding-top: 0.5rem;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-button {
  display: inline-flex;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--chat--button--color);
  background-color: var(--chat--button--background);
  border: 1px solid transparent;
  padding: var(--chat--button--padding);
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--chat--button--border-radius);
  transition: color var(--chat--transition-duration) ease-in-out, background-color var(--chat--transition-duration) ease-in-out, border-color var(--chat--transition-duration) ease-in-out, box-shadow var(--chat--transition-duration) ease-in-out;
  cursor: pointer;
}
.chat-button:hover {
  color: var(--chat--button--hover--color);
  background-color: var(--chat--button--hover--background);
  text-decoration: none;
}
.chat-button:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
.chat-button:disabled {
  opacity: 0.65;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-get-started {
  padding-top: var(--chat--spacing);
  padding-bottom: var(--chat--spacing);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-powered-by {
  text-align: center;
}
.chat-powered-by a {
  color: var(--chat--color-primary);
  text-decoration: none;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-get-started-footer {
  padding: var(--chat--spacing);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-input[data-v-b2a5be81] {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-direction: column;
  position: relative;
}
.chat-input[data-v-b2a5be81] * {
  box-sizing: border-box;
}
.chat-inputs[data-v-b2a5be81] {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.chat-inputs textarea[data-v-b2a5be81] {
  font-family: inherit;
  font-size: var(--chat--input--font-size);
  width: 100%;
  border: var(--chat--input--border, 0);
  border-radius: var(--chat--input--border-radius);
  padding: var(--chat--input--padding);
  min-height: var(--chat--textarea--height, 2.5rem);
  max-height: var(--chat--textarea--max-height);
  height: var(--chat--textarea--height, 2.5rem);
  resize: none;
  overflow-y: auto;
  background: var(--chat--input--background, white);
  color: var(--chat--input--text-color, initial);
  outline: none;
  line-height: var(--chat--input--line-height, 1.5);
}
.chat-inputs textarea[data-v-b2a5be81]::-moz-placeholder {
  font-size: var(--chat--input--placeholder--font-size, var(--chat--input--font-size));
}
.chat-inputs textarea[data-v-b2a5be81]::placeholder {
  font-size: var(--chat--input--placeholder--font-size, var(--chat--input--font-size));
}
.chat-inputs textarea[data-v-b2a5be81]:focus, .chat-inputs textarea[data-v-b2a5be81]:hover {
  border-color: var(--chat--input--border-active, 0);
}
.chat-inputs-controls[data-v-b2a5be81] {
  display: flex;
}
.chat-input-send-button[data-v-b2a5be81],
.chat-input-file-button[data-v-b2a5be81] {
  height: var(--chat--textarea--height);
  width: var(--chat--textarea--height);
  background: var(--chat--input--send--button--background, white);
  cursor: pointer;
  color: var(--chat--input--send--button--color, var(--chat--color-secondary));
  border: 0;
  font-size: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: color var(--chat--transition-duration) ease;
}
.chat-input-send-button svg[data-v-b2a5be81],
.chat-input-file-button svg[data-v-b2a5be81] {
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.chat-input-send-button[disabled][data-v-b2a5be81],
.chat-input-file-button[disabled][data-v-b2a5be81] {
  cursor: no-drop;
  color: var(--chat--color-disabled);
}
.chat-input-send-button .chat-input-send-button[data-v-b2a5be81]:hover, .chat-input-send-button .chat-input-send-button[data-v-b2a5be81]:focus,
.chat-input-file-button .chat-input-send-button[data-v-b2a5be81]:hover,
.chat-input-file-button .chat-input-send-button[data-v-b2a5be81]:focus {
  background: var(--chat--input--send--button--background-hover, var(--chat--input--send--button--background));
  color: var(--chat--input--send--button--color-hover);
}
.chat-input-file-button[data-v-b2a5be81] {
  background: var(--chat--input--file--button--background, white);
  color: var(--chat--input--file--button--color);
}
.chat-input-file-button[data-v-b2a5be81]:hover {
  background: var(--chat--input--file--button--background-hover);
  color: var(--chat--input--file--button--color-hover);
}
.chat-files[data-v-b2a5be81] {
  display: flex;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: var(--chat--files-spacing);
}
.chat-input-left-panel[data-v-b2a5be81] {
  width: var(--chat--input--left--panel--width);
  margin-left: 0.4rem;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-layout {
  width: 100%;
  height: 100%;
  display: flex;
  overflow-y: auto;
  flex-direction: column;
  font-family: var(--chat--font-family);
}
.chat-layout .chat-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1em;
  height: var(--chat--header-height);
  padding: var(--chat--header--padding);
  background: var(--chat--header--background);
  color: var(--chat--header--color);
  border-top: var(--chat--header--border-top);
  border-bottom: var(--chat--header--border-bottom);
  border-left: var(--chat--header--border-left);
  border-right: var(--chat--header--border-right);
}
.chat-layout .chat-header h1 {
  font-size: var(--chat--heading--font-size);
  color: var(--chat--header--color);
}
.chat-layout .chat-header p {
  font-size: var(--chat--subtitle--font-size);
  line-height: var(--chat--subtitle--line-height);
}
.chat-layout .chat-body {
  background: var(--chat--body--background);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  position: relative;
  min-height: 100px;
}
.chat-layout .chat-footer {
  border-top: 1px solid var(--chat--color-light-shade-100);
  background: var(--chat--footer--background);
  color: var(--chat--footer--color);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.chat-close-button {
  display: flex;
  border: none;
  background: none;
  cursor: pointer;
}
.chat-close-button:hover {
  color: var(--chat--close--button--color-hover, var(--chat--color-primary));
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-window-wrapper {
  position: fixed;
  display: flex;
  flex-direction: column;
  bottom: var(--chat--window--bottom);
  right: var(--chat--window--right);
  z-index: var(--chat--window--z-index);
  max-width: calc(100% - var(--chat--window--right, var(--chat--spacing)) * 2);
  max-height: calc(100% - var(--chat--window--bottom, var(--chat--spacing)) * 2);
}
.chat-window-wrapper .chat-window {
  display: flex;
  width: var(--chat--window--width);
  height: var(--chat--window--height);
  max-width: 100%;
  max-height: 100%;
  border: var(--chat--window--border, 1px solid var(--chat--color-light-shade-100));
  border-radius: var(--chat--window--border-radius, var(--chat--border-radius));
  margin-bottom: var(--chat--window--margin-bottom, var(--chat--spacing));
  overflow: hidden;
  transform-origin: bottom right;
}
.chat-window-wrapper .chat-window .chat-layout {
  width: auto;
  height: auto;
  flex: 1;
}
.chat-window-wrapper .chat-window-toggle {
  flex: 0 0 auto;
  background: var(--chat--toggle--background);
  color: var(--chat--toggle--color);
  cursor: pointer;
  width: var(--chat--toggle--width);
  height: var(--chat--toggle--height);
  border-radius: var(--chat--toggle--border-radius, 50%);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  transition: transform var(--chat--transition-duration) ease, background var(--chat--transition-duration) ease;
}
.chat-window-wrapper .chat-window-toggle:hover, .chat-window-wrapper .chat-window-toggle:focus {
  transform: scale(1.05);
  background: var(--chat--toggle--hover--background);
}
.chat-window-wrapper .chat-window-toggle:active {
  transform: scale(0.95);
  background: var(--chat--toggle--active--background);
}
.chat-window-transition-enter-active, .chat-window-transition-leave-active {
  transition: transform var(--chat--transition-duration) ease, opacity var(--chat--transition-duration) ease;
}
.chat-window-transition-enter-from, .chat-window-transition-leave-to {
  transform: scale(0);
  opacity: 0;
}
.chat-window-toggle-transition-enter-active, .chat-window-toggle-transition-leave-active {
  transition: opacity var(--chat--transition-duration) ease;
}
.chat-window-toggle-transition-enter-from, .chat-window-toggle-transition-leave-to {
  opacity: 0;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-message-typing {
  max-width: 80px;
}
.chat-message-typing.chat-message-typing-animation-scaling .chat-message-typing-circle {
  animation: chat-message-typing-animation-scaling 800ms ease-in-out infinite;
  animation-delay: 3600ms;
}
.chat-message-typing.chat-message-typing-animation-bouncing .chat-message-typing-circle {
  animation: chat-message-typing-animation-bouncing 800ms ease-in-out infinite;
  animation-delay: 3600ms;
}
.chat-message-typing .chat-message-typing-body {
  display: flex;
  justify-content: center;
  align-items: center;
}
.chat-message-typing .chat-message-typing-circle {
  display: block;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: var(--chat--color-typing);
  margin: 3px;
}
.chat-message-typing .chat-message-typing-circle:nth-child(1) {
  animation-delay: 0ms;
}
.chat-message-typing .chat-message-typing-circle:nth-child(2) {
  animation-delay: 333ms;
}
.chat-message-typing .chat-message-typing-circle:nth-child(3) {
  animation-delay: 666ms;
}
@keyframes chat-message-typing-animation-scaling {
0% {
    transform: scale(1);
}
33% {
    transform: scale(1);
}
50% {
    transform: scale(1.4);
}
100% {
    transform: scale(1);
}
}
@keyframes chat-message-typing-animation-bouncing {
0% {
    transform: translateY(0);
}
33% {
    transform: translateY(0);
}
50% {
    transform: translateY(-10px);
}
100% {
    transform: translateY(0);
}
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.chat-messages-list {
  margin-top: auto;
  display: block;
  padding: var(--chat--messages-list--padding);
}
.empty-container {
  container-type: size;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-container p {
  max-width: 16em;
  margin: 0;
}
.empty {
  text-align: center;
  color: var(--color-text-base);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding-inline: var(--spacing-m);
  padding-bottom: var(--spacing-l);
  overflow: hidden;
}
.emptyIcon {
  zoom: 2.5;
  color: var(--color-button-secondary-border);
}
@container (height < 150px) {
.empty {
    flex-direction: row;
    text-align: left;
}
.emptyIcon {
    zoom: 1.5;
}
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_pqtqf_123 {
  display: inline-flex;
  align-items: center;
  margin: 0 var(--spacing-4xs);
}
._icon_pqtqf_129 {
  color: var(--color-foreground-dark);
  cursor: help;
}
._icon_pqtqf_129:hover {
  color: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_u1r1u_123 {
  display: inline-flex;
  align-items: center;
  margin: 0 var(--spacing-4xs);
}
._icon_u1r1u_129 {
  color: var(--color-foreground-dark);
  cursor: pointer;
}
._icon_u1r1u_129:hover {
  color: var(--color-primary);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._chat_oezi2_123 {
  --chat--spacing: var(--spacing-xs);
  --chat--message--padding: var(--spacing-2xs);
  --chat--message--font-size: var(--font-size-2xs);
  --chat--input--font-size: var(--font-size-s);
  --chat--input--placeholder--font-size: var(--font-size-xs);
  --chat--message--bot--background: transparent;
  --chat--message--user--background: var(--color-text-lighter);
  --chat--message--bot--color: var(--color-text-dark);
  --chat--message--user--color: var(--color-text-dark);
  --chat--message--bot--border: none;
  --chat--message--user--border: none;
  --chat--message--user--border: none;
  --chat--input--padding: var(--spacing-xs);
  --chat--color-typing: var(--color-text-light);
  --chat--textarea--max-height: calc(var(--panel-height) * 0.3);
  --chat--message--pre--background: var(--color-foreground-light);
  --chat--textarea--height: calc(
  	var(--chat--input--padding) * 2 + var(--chat--input--font-size) *
  		var(--chat--input--line-height)
  );
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--color-background-light);
}
._chatHeader_oezi2_151 {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-regular);
  line-height: 18px;
  text-align: left;
  border-bottom: 1px solid var(--color-foreground-base);
  padding: var(--chat--spacing);
  background-color: var(--color-foreground-xlight);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
._chatTitle_oezi2_164 {
  font-weight: var(--font-weight-medium);
}
._session_oezi2_168 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xs);
  color: var(--color-text-base);
  max-width: 70%;
}
._sessionId_oezi2_176 {
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
._sessionId_oezi2_176._copyable_oezi2_182 {
  cursor: pointer;
}
._headerButton_oezi2_186 {
  max-height: 1.1rem;
  border: none;
}
._newHeaderButton_oezi2_191 {
  border: none;
  color: var(--color-text-light);
}
._chatBody_oezi2_196 {
  display: flex;
  height: 100%;
  overflow: auto;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
._messages_oezi2_205 {
  border-radius: var(--border-radius-base);
  height: 100%;
  width: 100%;
  overflow: auto;
  padding-top: var(--spacing-l);
}
._messages_oezi2_205:not(:last-child) {
  margin-right: 1em;
}
._messagesInput_oezi2_216 {
  --input-border-color: var(--border-color-base);
  --chat--input--border: none;
  --chat--input--border-radius: 0.5rem;
  --chat--input--send--button--background: transparent;
  --chat--input--send--button--color: var(--color-primary);
  --chat--input--file--button--background: transparent;
  --chat--input--file--button--color: var(--color-primary);
  --chat--input--border-active: var(--input-focus-border-color, var(--color-secondary));
  --chat--files-spacing: var(--spacing-2xs);
  --chat--input--background: transparent;
  --chat--input--file--button--color: var(--color-button-secondary-font);
  --chat--input--file--button--color-hover: var(--color-primary);
  padding: var(--spacing-5xs);
  margin: 0 var(--chat--spacing) var(--chat--spacing);
  flex-grow: 1;
  display: flex;
  background: var(--color-lm-chat-bot-background);
  border-radius: var(--chat--input--border-radius);
  transition: border-color 200ms ease-in-out;
  border: var(--input-border-color, var(--border-color-base)) var(--input-border-style, var(--border-style-base)) var(--input-border-width, var(--border-width-base));
}
[data-theme=dark] ._messagesInput_oezi2_216 {
  --chat--input--text-color: var(--input-font-color, var(--color-text-dark));
}
@media (prefers-color-scheme: dark) {
._messagesInput_oezi2_216 {
    --chat--input--text-color: var(--input-font-color, var(--color-text-dark));
}
}
._messagesInput_oezi2_216:focus-within {
  --input-border-color: #4538a3;
}
._messagesHistory_oezi2_250 {
  height: var(--chat--textarea--height);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._component_qlocg_123 {
  --color-run-data-background: var(--color-background-light);
}
._title_qlocg_127 {
  text-transform: uppercase;
  letter-spacing: 3px;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_tdw6t_123 {
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
}
._header_tdw6t_132 {
  padding: var(--spacing-2xs);
}
._actions_tdw6t_136 {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xs);
  padding-inline-end: var(--spacing-2xs);
}
._actions_tdw6t_136 ._pressed_tdw6t_142 {
  background-color: var(--color-button-secondary-focus-outline);
}
._title_tdw6t_146 {
  display: flex;
  align-items: center;
  flex-shrink: 1;
}
._icon_tdw6t_152 {
  margin-right: var(--spacing-2xs);
}
._executionSummary_tdw6t_156 {
  flex-shrink: 1;
}
._content_tdw6t_160 {
  flex-shrink: 1;
  flex-grow: 1;
  display: flex;
  align-items: stretch;
  overflow: hidden;
}
._outputPanel_tdw6t_168 {
  width: 0;
  flex-grow: 1;
}
._inputResizer_tdw6t_173 {
  overflow: hidden;
  flex-shrink: 0;
}
._inputResizer_tdw6t_173:not(:is(:last-child, ._collapsed_tdw6t_177, ._full_tdw6t_177)) {
  border-right: var(--border-base);
}
._placeholder_tdw6t_181 {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._container_xdwf0_123 {
  display: flex;
}
._container_xdwf0_123 button:hover {
  background-color: var(--color-background-base);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
@media all and (display-mode: picture-in-picture) {
._resizeWrapper_19m2p_124 {
    height: 100% !important;
    max-height: 100vh !important;
}
}
._pipContent_19m2p_129 {
  height: 100%;
  position: relative;
  overflow: hidden;
}
._resizeWrapper_19m2p_124 {
  height: 100%;
  min-height: 0;
  flex-basis: 0;
  border-top: var(--border-base);
  background-color: var(--color-background-light);
}
._container_19m2p_143 {
  height: 100%;
  display: flex;
  flex-grow: 1;
}
._container_19m2p_143 > *:not(:last-child) {
  border-right: var(--border-base);
}
._chat_19m2p_152 {
  flex-shrink: 0;
}
._logsContainer_19m2p_156 {
  width: 0;
  flex-grow: 1;
  display: flex;
  align-items: stretch;
}
._logsContainer_19m2p_156 > *:not(:last-child) {
  border-right: var(--border-base);
}
._overviewResizer_19m2p_166 {
  flex-grow: 0;
  flex-shrink: 0;
}
._overviewResizer_19m2p_166:last-child {
  flex-grow: 1;
}
._logsOverview_19m2p_174 {
  height: 100%;
}
._logsDetails_19m2p_178 {
  width: 0;
  flex-grow: 1;
}