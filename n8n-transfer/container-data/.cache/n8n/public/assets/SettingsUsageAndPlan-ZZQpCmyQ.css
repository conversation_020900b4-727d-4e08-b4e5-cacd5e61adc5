/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
._center_eht1q_123 > div {
  justify-content: center;
}
._actionBox_eht1q_127 {
  margin: var(--spacing-2xl) 0 0;
}
._spacedFlex_eht1q_131 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
._title_eht1q_137 {
  display: flex;
  align-items: center;
  padding: var(--spacing-2xl) 0 var(--spacing-m);
}
._quota_eht1q_143 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 54px;
  padding: 0 var(--spacing-s);
  margin: 0 0 var(--spacing-xs);
  background: var(--color-background-xlight);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--color-foreground-base);
  white-space: nowrap;
}
._quota_eht1q_143 ._count_eht1q_155 {
  text-transform: lowercase;
  font-size: var(--font-size-s);
}
._buttons_eht1q_160 {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-xl) 0 0;
}
._buttons_eht1q_160 button {
  margin-left: var(--spacing-xs);
}
._buttons_eht1q_160 button a {
  display: inline-block;
  color: inherit;
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-m);
  margin: calc(var(--spacing-xs) * -1) calc(var(--spacing-m) * -1);
}
._chart_eht1q_176 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 1;
}
._chartLine_eht1q_183 {
  display: block;
  height: 10px;
  width: 100%;
  max-width: 260px;
  margin: 0 var(--spacing-m);
  border-radius: 10px;
  background: var(--color-background-base);
}
._chartBar_eht1q_193 {
  float: left;
  height: 100%;
  max-width: 100%;
  background: var(--color-secondary);
  border-radius: 10px;
  transition: width 0.2s cubic-bezier(0.19, 1, 0.22, 1);
}
div[class*=info] > span > span:last-child {
  line-height: 1.4;
  padding: 0 0 0 var(--spacing-4xs);
}
._titleTooltip_eht1q_207 {
  display: flex;
  align-items: center;
  margin: 0 0 0 var(--spacing-2xs);
}/* BEM support Func
 -------------------------- */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Outline
-------------------------- */
/* Box shadow
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
.settings-usage-and-plan[data-v-6aff6da3] .el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.settings-usage-and-plan[data-v-6aff6da3] .el-dialog__wrapper .el-dialog {
  margin: 0;
}
.settings-usage-and-plan[data-v-6aff6da3] .el-dialog__wrapper .el-dialog .el-dialog__footer button {
  margin-left: var(--spacing-xs);
}