[{"name": "actionNetworkApi", "displayName": "Action Network API", "documentationUrl": "actionNetwork", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://actionnetwork.org/api/v2", "url": "/events?per_page=1"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/ActionNetwork/actionNetwork.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.actionNetwork"]}, {"name": "activeCampaignApi", "displayName": "ActiveCampaign API", "documentationUrl": "activeCampaign", "properties": [{"displayName": "API URL", "name": "apiUrl", "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Api-Token": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.apiUrl}}", "url": "/api/3/fields"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/ActiveCampaign/activeCampaign.svg", "dark": "icons/n8n-nodes-base/dist/nodes/ActiveCampaign/activeCampaign.dark.svg"}, "supportedNodes": ["n8n-nodes-base.activeCampaign", "n8n-nodes-base.activeCampaignTrigger"]}, {"name": "acuitySchedulingApi", "displayName": "Acuity Scheduling API", "documentationUrl": "acuityScheduling", "properties": [{"displayName": "User ID", "name": "userId", "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/AcuityScheduling/acuityScheduling.png", "supportedNodes": ["n8n-nodes-base.acuitySchedulingTrigger"]}, {"name": "acuitySchedulingOAuth2Api", "extends": ["oAuth2Api"], "displayName": "AcuityScheduling OAuth2 API", "documentationUrl": "acuityScheduling", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://acuityscheduling.com/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://acuityscheduling.com/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "api-v1", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/AcuityScheduling/acuityScheduling.png", "supportedNodes": ["n8n-nodes-base.acuitySchedulingTrigger"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Adalo API", "documentationUrl": "adalo", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The Adalo API is available on paid Adalo plans, find more information <a href=\"https://help.adalo.com/integrations/the-adalo-api\" target=\"_blank\">here</a>"}, {"displayName": "App ID", "name": "appId", "type": "string", "default": "", "description": "You can get App ID from the URL of your app. For example, if your app URL is <strong>https://app.adalo.com/apps/1234567890/screens</strong>, then your App ID is <strong>1234567890</strong>."}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Adalo/adalo.svg", "supportedNodes": ["n8n-nodes-base.adalo"]}, {"name": "affinityApi", "displayName": "Affinity API", "documentationUrl": "affinity", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Affinity/affinity.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Affinity/affinity.dark.svg"}, "supportedNodes": ["n8n-nodes-base.affinity", "n8n-nodes-base.affinityTrigger"]}, {"name": "agileCrmApi", "displayName": "AgileCRM API", "documentationUrl": "agileCrm", "properties": [{"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": "", "placeholder": "example", "description": "If the domain is https://example.agilecrm.com \"example\" would have to be entered"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/AgileCrm/agilecrm.png", "supportedNodes": ["n8n-nodes-base.agileCrm"]}, {"name": "airtableApi", "displayName": "Airtable API", "documentationUrl": "airtable", "properties": [{"displayName": "This type of connection (API Key) was deprecated and can't be used anymore. Please create a new credential of type 'Access Token' instead.", "name": "deprecated", "type": "notice", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"api_key": "={{$credentials.apiKey}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Airtable/airtable.svg", "supportedNodes": ["n8n-nodes-base.airtable", "n8n-nodes-base.airtableTrigger"]}, {"name": "airtableOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Airtable OAuth2 API", "documentationUrl": "airtable", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "pkce"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://airtable.com/oauth2/v1/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://airtable.com/oauth2/v1/token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "schema.bases:read data.records:read data.records:write"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Airtable/airtable.svg", "supportedNodes": ["n8n-nodes-base.airtable", "n8n-nodes-base.airtableTrigger"]}, {"name": "airtableTokenApi", "displayName": "Airtable Personal Access Token API", "documentationUrl": "airtable", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Make sure you enabled the following scopes for your token:<br>\n\t\t\t\t<code>data.records:read</code><br>\n\t\t\t\t<code>data.records:write</code><br>\n\t\t\t\t<code>schema.bases:read</code><br>\n\t\t\t\t", "name": "notice", "type": "notice", "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.airtable.com/v0/meta/whoami"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Airtable/airtable.svg", "supportedNodes": ["n8n-nodes-base.airtable", "n8n-nodes-base.airtableTrigger"]}, {"name": "airtopApi", "displayName": "Airtop API", "documentationUrl": "airtop", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "default": "", "description": "The Airtop API key. You can create one at <a href=\"https://portal.airtop.ai/api-keys\" target=\"_blank\">Airtop</a> for free.", "required": true, "typeOptions": {"password": true}, "noDataExpression": true}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}", "api-key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"method": "GET", "baseURL": "https://api.airtop.ai/api/v1", "url": "/sessions", "qs": {"limit": 10}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Airtop/airtop.svg", "supportedNodes": ["n8n-nodes-base.airtop"]}, {"name": "alienVaultApi", "displayName": "AlienVault API", "documentationUrl": "alienvault", "httpRequestNode": {"name": "<PERSON><PERSON><PERSON>", "docsUrl": "https://otx.alienvault.com/api", "apiBaseUrl": "https://otx.alienvault.com/api/v1/"}, "properties": [{"displayName": "OTX Key", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-OTX-API-KEY": "={{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://otx.alienvault.com", "url": "/api/v1/user/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/AlienVault.png", "supportedNodes": []}, {"name": "amqp", "displayName": "AMQP", "documentationUrl": "amqp", "properties": [{"displayName": "Hostname", "name": "hostname", "type": "string", "placeholder": "e.g. localhost", "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 5672}, {"displayName": "User", "name": "username", "type": "string", "placeholder": "e.g. guest", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "placeholder": "e.g. guest", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Transport Type", "name": "transportType", "type": "string", "placeholder": "e.g. tcp", "default": "", "hint": "Optional transport type to use, either tcp or tls"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Amqp/amqp.svg", "supportedNodes": ["n8n-nodes-base.amqp", "n8n-nodes-base.amqpTrigger"]}, {"name": "apiTemplateIoApi", "displayName": "APITemplate.io API", "documentationUrl": "apiTemplateIo", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-API-KEY": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.apitemplate.io/v1", "url": "/list-templates"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/ApiTemplateIo/apiTemplateIo.svg", "supportedNodes": ["n8n-nodes-base.apiTemplateIo"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Asana API", "documentationUrl": "asana", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Asana/asana.svg", "supportedNodes": ["n8n-nodes-base.asana", "n8n-nodes-base.asana<PERSON><PERSON><PERSON>"]}, {"name": "asanaOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Asana OAuth2 API", "documentationUrl": "asana", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.asana.com/-/oauth_authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://app.asana.com/-/oauth_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Asana/asana.svg", "supportedNodes": ["n8n-nodes-base.asana", "n8n-nodes-base.asana<PERSON><PERSON><PERSON>"]}, {"name": "auth0ManagementApi", "displayName": "Auth0 Management API", "documentationUrl": "auth0management", "httpRequestNode": {"name": "Auth0", "docsUrl": "https://auth0.com/docs/api/management/v2", "apiBaseUrlPlaceholder": "https://your-tenant.auth0.com/api/v2/users/"}, "properties": [{"displayName": "Session Token", "name": "sessionToken", "type": "hidden", "typeOptions": {"expirable": true, "password": true}, "default": ""}, {"displayName": "Auth0 Domain", "name": "domain", "type": "string", "required": true, "default": "your-domain.eu.auth0.com"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "required": true, "default": ""}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.sessionToken}}"}}}, "test": {"request": {"baseURL": "=https://{{$credentials.domain}}", "url": "/api/v2/clients"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Auth0.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Auth0.dark.svg"}, "supportedNodes": []}, {"name": "automizyApi", "displayName": "Automizy API", "documentationUrl": "automizy", "properties": [{"displayName": "This service may no longer exist and will be removed from n8n in a future release.", "name": "deprecated", "type": "notice", "default": ""}, {"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Automizy/automizy.png", "supportedNodes": ["n8n-nodes-base.automizy"]}, {"name": "autopilotApi", "displayName": "Autopilot API", "documentationUrl": "autopilot", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Autopilot/autopilot.svg", "supportedNodes": ["n8n-nodes-base.autopilot", "n8n-nodes-base.autopilotTrigger"]}, {"name": "aws", "displayName": "AWS", "documentationUrl": "aws", "properties": [{"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "Africa (Cape Town) - af-south-1", "value": "af-south-1"}, {"name": "Asia Pacific (Hong Kong) - ap-east-1", "value": "ap-east-1"}, {"name": "Asia Pacific (Mumbai) - ap-south-1", "value": "ap-south-1"}, {"name": "Asia Pacific (Hyderabad) - ap-south-2", "value": "ap-south-2"}, {"name": "Asia Pacific (Singapore) - ap-southeast-1", "value": "ap-southeast-1"}, {"name": "Asia Pacific (Sydney) - ap-southeast-2", "value": "ap-southeast-2"}, {"name": "Asia Pacific (Jakarta) - ap-southeast-3", "value": "ap-southeast-3"}, {"name": "Asia Pacific (Melbourne) - ap-southeast-4", "value": "ap-southeast-4"}, {"name": "Asia Pacific (Malaysia) - ap-southeast-5", "value": "ap-southeast-5"}, {"name": "Asia Pacific (Thailand) - ap-southeast-7", "value": "ap-southeast-7"}, {"name": "Asia Pacific (Tokyo) - ap-northeast-1", "value": "ap-northeast-1"}, {"name": "Asia Pacific (Seoul) - ap-northeast-2", "value": "ap-northeast-2"}, {"name": "Asia Pacific (Osaka) - ap-northeast-3", "value": "ap-northeast-3"}, {"name": "Canada (Central) - ca-central-1", "value": "ca-central-1"}, {"name": "Canada West (Calgary) - ca-west-1", "value": "ca-west-1"}, {"name": "China (Beijing) - cn-north-1", "value": "cn-north-1"}, {"name": "China (Ningxia) - cn-northwest-1", "value": "cn-northwest-1"}, {"name": "Europe (Frankfurt) - eu-central-1", "value": "eu-central-1"}, {"name": "Europe (Zurich) - eu-central-2", "value": "eu-central-2"}, {"name": "Europe (Stockholm) - eu-north-1", "value": "eu-north-1"}, {"name": "Europe (Milan) - eu-south-1", "value": "eu-south-1"}, {"name": "Europe (Spain) - eu-south-2", "value": "eu-south-2"}, {"name": "Europe (Ireland) - eu-west-1", "value": "eu-west-1"}, {"name": "Europe (London) - eu-west-2", "value": "eu-west-2"}, {"name": "Europe (Paris) - eu-west-3", "value": "eu-west-3"}, {"name": "Israel (Tel Aviv) - il-central-1", "value": "il-central-1"}, {"name": "Middle East (UAE) - me-central-1", "value": "me-central-1"}, {"name": "Middle East (Bahrain) - me-south-1", "value": "me-south-1"}, {"name": "Mexico (Central) - mx-central-1", "value": "mx-central-1"}, {"name": "South America (São Paulo) - sa-east-1", "value": "sa-east-1"}, {"name": "US East (N. Virginia) - us-east-1", "value": "us-east-1"}, {"name": "US East (Ohio) - us-east-2", "value": "us-east-2"}, {"name": "US East (GovCloud) - us-gov-east-1", "value": "us-gov-east-1"}, {"name": "US West (N. California) - us-west-1", "value": "us-west-1"}, {"name": "US West (Oregon) - us-west-2", "value": "us-west-2"}, {"name": "US West (GovCloud) - us-gov-west-1", "value": "us-gov-west-1"}], "default": "us-east-1"}, {"displayName": "Access Key ID", "name": "accessKeyId", "type": "string", "default": ""}, {"displayName": "Secret Access Key", "name": "secretAccessKey", "type": "string", "default": "", "typeOptions": {"password": true}}, {"displayName": "Temporary Security Credentials", "name": "temporaryCredentials", "description": "Support for temporary credentials from AWS STS", "type": "boolean", "default": false}, {"displayName": "Session Token", "name": "sessionToken", "type": "string", "displayOptions": {"show": {"temporaryCredentials": [true]}}, "default": "", "typeOptions": {"password": true}}, {"displayName": "Custom Endpoints", "name": "customEndpoints", "type": "boolean", "default": false}, {"displayName": "Rekognition Endpoint", "name": "rekognitionEndpoint", "description": "If you use Amazon VPC to host n8n, you can establish a connection between your VPC and Rekognition using a VPC endpoint. Leave blank to use the default endpoint.", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://rekognition.{region}.amazonaws.com"}, {"displayName": "Lambda Endpoint", "name": "lambdaEndpoint", "description": "If you use Amazon VPC to host n8n, you can establish a connection between your VPC and Lambda using a VPC endpoint. Leave blank to use the default endpoint.", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://lambda.{region}.amazonaws.com"}, {"displayName": "SNS Endpoint", "name": "snsEndpoint", "description": "If you use Amazon VPC to host n8n, you can establish a connection between your VPC and SNS using a VPC endpoint. Leave blank to use the default endpoint.", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://sns.{region}.amazonaws.com"}, {"displayName": "SES Endpoint", "name": "sesEndpoint", "description": "If you use Amazon VPC to host n8n, you can establish a connection between your VPC and SES using a VPC endpoint. Leave blank to use the default endpoint.", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://email.{region}.amazonaws.com"}, {"displayName": "SQS Endpoint", "name": "sqsEndpoint", "description": "If you use Amazon VPC to host n8n, you can establish a connection between your VPC and SQS using a VPC endpoint. Leave blank to use the default endpoint.", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://sqs.{region}.amazonaws.com"}, {"displayName": "S3 Endpoint", "name": "s3Endpoint", "description": "If you use Amazon VPC to host n8n, you can establish a connection between your VPC and S3 using a VPC endpoint. Leave blank to use the default endpoint.", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://s3.{region}.amazonaws.com"}, {"displayName": "SSM Endpoint", "name": "ssmEndpoint", "description": "Endpoint for AWS Systems Manager (SSM)", "type": "string", "displayOptions": {"show": {"customEndpoints": [true]}}, "default": "", "placeholder": "https://ssm.{region}.amazonaws.com"}], "test": {"request": {"baseURL": "=https://sts.{{$credentials.region}}.amazonaws.com", "url": "?Action=GetCallerIdentity&Version=2011-06-15", "method": "POST"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/AWS.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/AWS.dark.svg"}, "authenticate": {}, "supportedNodes": ["n8n-nodes-base.awsLambda", "n8n-nodes-base.awsSns", "n8n-nodes-base.awsSnsTrigger", "n8n-nodes-base.awsCertificateManager", "n8n-nodes-base.awsCognito", "n8n-nodes-base.awsComprehend", "n8n-nodes-base.awsDynamoDb", "n8n-nodes-base.awsElb", "n8n-nodes-base.awsRekognition", "n8n-nodes-base.awsS3", "n8n-nodes-base.awsSes", "n8n-nodes-base.awsSqs", "n8n-nodes-base.awsTextract", "n8n-nodes-base.awsTranscribe"]}, {"name": "azureStorageOAuth2Api", "displayName": "Azure Storage OAuth2 API", "extends": ["microsoftOAuth2Api"], "documentationUrl": "azurestorage", "properties": [{"displayName": "Account", "name": "account", "type": "string", "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "hidden", "default": "=https://{{ $self[\"account\"] }}.blob.core.windows.net"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://storage.azure.com/.default"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Storage/azureStorage.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Storage/azureStorage.dark.svg"}, "supportedNodes": ["n8n-nodes-base.azureStorage"]}, {"name": "azureStorageSharedKeyApi", "displayName": "Azure Storage Shared Key API", "documentationUrl": "azurestorage", "properties": [{"displayName": "Account", "name": "account", "description": "Account name", "type": "string", "default": ""}, {"displayName": "Key", "name": "key", "description": "Account key", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "hidden", "default": "=https://{{ $self[\"account\"] }}.blob.core.windows.net"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Storage/azureStorage.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Storage/azureStorage.dark.svg"}, "authenticate": {}, "supportedNodes": ["n8n-nodes-base.azureStorage"]}, {"name": "bambooHrApi", "displayName": "BambooHR API", "documentationUrl": "bambooHr", "properties": [{"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/BambooHr/bambooHr.png", "supportedNodes": ["n8n-nodes-base.bambooHr"]}, {"name": "bannerbearApi", "displayName": "Bannerbear API", "documentationUrl": "bannerbear", "properties": [{"displayName": "Project API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bannerbear/bannerbear.png", "supportedNodes": ["n8n-nodes-base.bannerbear"]}, {"name": "baserowApi", "displayName": "Baserow API", "documentationUrl": "baserow", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": "https://api.baserow.io"}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "default": "", "typeOptions": {"password": true}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Baserow/baserow.svg", "supportedNodes": ["n8n-nodes-base.baserow"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Beeminder API", "documentationUrl": "beeminder", "properties": [{"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "authToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"body": {"auth_token": "={{$credentials.authToken}}"}}}, "test": {"request": {"baseURL": "https://www.beeminder.com/api/v1", "url": "=/users/{{$credentials.user}}.json"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Beeminder/beeminder.png", "supportedNodes": ["n8n-nodes-base.beeminder"]}, {"name": "bitbucketApi", "displayName": "Bitbucket API", "documentationUrl": "bitbucket", "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "App Password", "name": "appPassword", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitbucket/bitbucket.svg", "supportedNodes": ["n8n-nodes-base.bitbucketTrigger"]}, {"name": "bitlyApi", "displayName": "Bitly API", "documentationUrl": "bitly", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitly/bitly.svg", "supportedNodes": ["n8n-nodes-base.bitly"]}, {"name": "bitlyOAuth2Api", "displayName": "Bitly OAuth2 API", "documentationUrl": "bitly", "extends": ["oAuth2Api"], "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://bitly.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api-ssl.bitly.com/oauth/access_token", "required": true}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "", "description": "For some services additional query parameters have to be set which can be defined here", "placeholder": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitly/bitly.svg", "supportedNodes": ["n8n-nodes-base.bitly"]}, {"name": "bitwardenApi", "displayName": "Bitwarden API", "documentationUrl": "bitwarden", "properties": [{"displayName": "Client ID", "name": "clientId", "type": "string", "default": ""}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Environment", "name": "environment", "type": "options", "default": "cloudHosted", "options": [{"name": "Cloud-Hosted", "value": "cloudHosted"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Self-Hosted Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://www.mydomain.com", "displayOptions": {"show": {"environment": ["selfHosted"]}}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitwarden/bitwarden.svg", "supportedNodes": ["n8n-nodes-base.bitwarden"]}, {"name": "boxOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Box OAuth2 API", "documentationUrl": "box", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://account.box.com/api/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.box.com/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Box/box.png", "supportedNodes": ["n8n-nodes-base.box", "n8n-nodes-base.boxTrigger"]}, {"name": "brandfetchApi", "displayName": "Brandfetch API", "documentationUrl": "brandfetch", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.brandfetch.io", "url": "/v2/brands/brandfetch.com"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Brandfetch/brandfetch.png", "supportedNodes": ["n8n-nodes-base.Brandfetch"]}, {"name": "bubbleApi", "displayName": "Bubble API", "documentationUrl": "bubble", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "App Name", "name": "appName", "type": "string", "default": ""}, {"displayName": "Environment", "name": "environment", "type": "options", "default": "live", "options": [{"name": "Development", "value": "development"}, {"name": "Live", "value": "live"}]}, {"displayName": "Hosting", "name": "hosting", "type": "options", "default": "bubbleHosted", "options": [{"name": "Bubble-Hosted", "value": "bubbleHosted"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Domain", "name": "domain", "type": "string", "placeholder": "mydomain.com", "default": "", "displayOptions": {"show": {"hosting": ["selfHosted"]}}}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Bubble/bubble.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Bubble/bubble.dark.svg"}, "supportedNodes": ["n8n-nodes-base.bubble"]}, {"name": "calApi", "displayName": "Cal API", "documentationUrl": "cal", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Host", "name": "host", "type": "string", "default": "https://api.cal.com"}], "authenticate": {"type": "generic", "properties": {"qs": {"apiKey": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.host}}", "url": "=/v1/memberships"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Cal/cal.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Cal/cal.dark.svg"}, "supportedNodes": ["n8n-nodes-base.calTrigger"]}, {"name": "calen<PERSON><PERSON><PERSON>", "displayName": "Calendly API", "documentationUrl": "calendly", "properties": [{"displayName": "API Key or Personal Access Token", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://calendly.com", "url": "/api/v1/users/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Calendly/calendly.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.calendlyTrigger"]}, {"name": "calendlyOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Calendly OAuth2 API", "documentationUrl": "calendly", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://auth.calendly.com/oauth/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://auth.calendly.com/oauth/token"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Calendly.svg", "supportedNodes": ["n8n-nodes-base.calendlyTrigger"]}, {"name": "carbonBlackApi", "displayName": "Carbon Black API", "documentationUrl": "carbonblack", "httpRequestNode": {"name": "Carbon Black", "docsUrl": "https://developer.carbonblack.com/reference", "apiBaseUrl": ""}, "properties": [{"displayName": "URL", "name": "apiUrl", "type": "string", "placeholder": "https://defense.conferdeploy.net/", "default": ""}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Auth-Token": "={{$credentials.accessToken}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/vmware.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/vmware.dark.svg"}, "supportedNodes": []}, {"name": "chargebeeApi", "displayName": "Chargebee API", "documentationUrl": "chargebee", "properties": [{"displayName": "Account Name", "name": "accountName", "type": "string", "default": ""}, {"displayName": "Api Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Chargebee/chargebee.png", "supportedNodes": ["n8n-nodes-base.chargebee"]}, {"name": "circleCiApi", "displayName": "CircleCI API", "documentationUrl": "circleCi", "properties": [{"displayName": "Personal API Token", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/CircleCi/circleCi.svg", "dark": "icons/n8n-nodes-base/dist/nodes/CircleCi/circleCi.dark.svg"}, "supportedNodes": ["n8n-nodes-base.circleCi"]}, {"name": "ciscoMerakiApi", "displayName": "Cisco Meraki API", "documentationUrl": "cisco<PERSON><PERSON>", "httpRequestNode": {"name": "<PERSON><PERSON>", "docsUrl": "https://developer.cisco.com/meraki/api/", "apiBaseUrl": "https://api.meraki.com/api/v1/"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Cisco-Meraki-API-Key": "={{$credentials.apiKey}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "supportedNodes": []}, {"name": "ciscoSecureEndpointApi", "displayName": "Cisco Secure Endpoint (AMP) API", "documentationUrl": "ciscosecureendpoint", "httpRequestNode": {"name": "Cisco Secure Endpoint", "docsUrl": "https://developer.cisco.com/docs/secure-endpoint/", "apiBaseUrl": ""}, "properties": [{"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "Asia Pacific, Japan, and China", "value": "apjc.amp"}, {"name": "Europe", "value": "eu.amp"}, {"name": "North America", "value": "amp"}], "default": "amp"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "test": {"request": {"baseURL": "=https://api.{{$credentials.region}}.cisco.com", "url": "/v3/organizations", "qs": {"size": 10}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "authenticate": {}, "supportedNodes": []}, {"name": "ciscoWebexOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Cisco Webex OAuth2 API", "documentationUrl": "ciscowebex", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://webexapis.com/v1/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://webexapis.com/v1/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "spark:memberships_read meeting:recordings_read spark:kms meeting:schedules_read spark:rooms_read spark:messages_write spark:memberships_write meeting:recordings_write meeting:preferences_read spark:messages_read meeting:schedules_write"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "supportedNodes": ["n8n-nodes-base.ciscoWebex", "n8n-nodes-base.ciscoWebexTrigger"]}, {"name": "ciscoUmbrellaApi", "displayName": "Cisco Umbrella API", "documentationUrl": "ciscoumbrella", "httpRequestNode": {"name": "<PERSON><PERSON>", "docsUrl": "https://developer.cisco.com/docs/cloud-security/", "apiBaseUrl": "https://api.umbrella.com/"}, "properties": [{"displayName": "Session Token", "name": "sessionToken", "type": "hidden", "typeOptions": {"expirable": true}, "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Secret", "name": "secret", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.sessionToken}}"}}}, "test": {"request": {"baseURL": "https://api.umbrella.com", "url": "/users"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "supportedNodes": []}, {"name": "cloudflareApi", "displayName": "Cloudflare API", "documentationUrl": "cloudflare", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiToken}}"}}}, "test": {"request": {"baseURL": "https://api.cloudflare.com/client/v4/user/tokens/verify"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Cloudflare/cloudflare.svg", "supportedNodes": ["n8n-nodes-base.cloudflare"]}, {"name": "clearbitApi", "displayName": "Clearbit API", "documentationUrl": "clearbit", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Clearbit/clearbit.svg", "supportedNodes": ["n8n-nodes-base.clearbit"]}, {"name": "clickUpApi", "displayName": "ClickUp API", "documentationUrl": "clickUp", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "={{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.clickup.com/api/v2", "url": "/team"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/ClickUp/clickup.svg", "supportedNodes": ["n8n-nodes-base.clickUp", "n8n-nodes-base.clickUpTrigger"]}, {"name": "clickUpOAuth2Api", "extends": ["oAuth2Api"], "displayName": "ClickUp OAuth2 API", "documentationUrl": "clickUp", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.clickup.com/api", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.clickup.com/api/v2/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ClickUp/clickup.svg", "supportedNodes": ["n8n-nodes-base.clickUp", "n8n-nodes-base.clickUpTrigger"]}, {"name": "clockifyApi", "displayName": "Clockify API", "documentationUrl": "clockify", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Api-Key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.clockify.me/api/v1", "url": "/workspaces"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Clockify/clockify.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Clockify/clockify.dark.svg"}, "supportedNodes": ["n8n-nodes-base.clockify", "n8n-nodes-base.clockifyTrigger"]}, {"name": "cockpitApi", "displayName": "Cockpit API", "documentationUrl": "cockpit", "properties": [{"displayName": "Cockpit URL", "name": "url", "type": "string", "default": "", "placeholder": "https://example.com"}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Cockpit/cockpit.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Cockpit/cockpit.dark.svg"}, "supportedNodes": ["n8n-nodes-base.cockpit"]}, {"name": "coda<PERSON><PERSON>", "displayName": "Coda API", "documentationUrl": "coda", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://coda.io/apis/v1/whoami", "headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Coda/coda.svg", "supportedNodes": ["n8n-nodes-base.coda"]}, {"name": "contentfulApi", "displayName": "Contentful API", "documentationUrl": "contentful", "properties": [{"displayName": "Space ID", "name": "spaceId", "type": "string", "default": "", "required": true, "description": "The ID for the Contentful space"}, {"displayName": "Content Delivery API Access Token", "name": "ContentDeliveryaccessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Access token that has access to the space. Can be left empty if only Delivery API should be used."}, {"displayName": "Content Preview API Access Token", "name": "ContentPreviewaccessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Access token that has access to the space. Can be left empty if only Preview API should be used."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Contentful/contentful.png", "supportedNodes": ["n8n-nodes-base.contentful"]}, {"name": "convertApi", "displayName": "ConvertAPI", "documentationUrl": "convertapi", "httpRequestNode": {"name": "ConvertAPI", "docsUrl": "https://docs.convertapi.com/docs/getting-started", "apiBaseUrl": "https://v2.convertapi.com/"}, "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiToken}}"}}}, "test": {"request": {"baseURL": "https://v2.convertapi.com", "url": "/convert/docx/to/pdf", "ignoreHttpStatusErrors": true}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "Code", "value": 4013, "message": "API Token or Secret is invalid."}}]}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/ConvertApi.png", "supportedNodes": []}, {"name": "convertKitApi", "displayName": "ConvertKit API", "documentationUrl": "convertKit", "properties": [{"displayName": "API Secret", "name": "apiSecret", "type": "string", "default": "", "typeOptions": {"password": true}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ConvertKit/convertKit.svg", "supportedNodes": ["n8n-nodes-base.convertKit", "n8n-nodes-base.convertKitTrigger"]}, {"name": "copperApi", "displayName": "Copper API", "documentationUrl": "copper", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Email", "name": "email", "required": true, "type": "string", "placeholder": "<EMAIL>", "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-PW-AccessToken": "={{$credentials.apiKey}}", "X-PW-Application": "developer_api", "X-PW-UserEmail": "={{$credentials.email}}"}}}, "test": {"request": {"baseURL": "https://api.copper.com/developer_api/v1/", "url": "users/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Copper/copper.svg", "supportedNodes": ["n8n-nodes-base.copper", "n8n-nodes-base.copperTrigger"]}, {"name": "cortex<PERSON><PERSON>", "displayName": "Cortex API", "documentationUrl": "cortex", "properties": [{"displayName": "API Key", "name": "cortex<PERSON><PERSON><PERSON>ey", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Cortex Instance", "name": "host", "type": "string", "description": "The URL of the Cortex instance", "default": "", "placeholder": "https://localhost:9001"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.cortex<PERSON><PERSON>ey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.host}}", "url": "/api/analyzer"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Cortex/cortex.svg", "supportedNodes": ["n8n-nodes-base.cortex"]}, {"name": "crateDb", "displayName": "CrateDB", "documentationUrl": "crateDb", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "doc"}, {"displayName": "User", "name": "user", "type": "string", "default": "crate"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "SSL", "name": "ssl", "type": "options", "options": [{"name": "Allow", "value": "allow"}, {"name": "Disable", "value": "disable"}, {"name": "Require", "value": "require"}], "default": "disable"}, {"displayName": "Port", "name": "port", "type": "number", "default": 5432}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/CrateDb/cratedb.png", "supportedNodes": ["n8n-nodes-base.crateDb"]}, {"name": "crowdStrikeOAuth2Api", "displayName": "CrowdStrike OAuth2 API", "documentationUrl": "crowdstrike", "httpRequestNode": {"name": "CrowdStrike", "docsUrl": "https://developer.crowdstrike.com/", "apiBaseUrl": ""}, "properties": [{"displayName": "Session Token", "name": "sessionToken", "type": "hidden", "typeOptions": {"expirable": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "required": true, "default": ""}, {"displayName": "Client ID", "name": "clientId", "type": "string", "required": true, "default": ""}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.sessionToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "user-management/queries/users/v1"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/CrowdStrike.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/CrowdStrike.dark.svg"}, "supportedNodes": []}, {"name": "crowdDevApi", "displayName": "crowd.dev API", "documentationUrl": "crowddev", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "https://app.crowd.dev"}, {"displayName": "Tenant ID", "name": "tenantId", "type": "string", "default": ""}, {"displayName": "Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "={{\"Bearer \" + $credentials.token}}"}}}, "test": {"request": {"method": "POST", "baseURL": "={{$credentials.url.replace(/\\/$/, \"\") + \"/api/tenant/\" + $credentials.tenantId}}", "url": "/member/query", "skipSslCertificateValidation": "={{ $credentials.allowUnauthorizedCerts }}", "body": {"limit": 1, "offset": 0}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/CrowdDev/crowdDev.svg", "dark": "icons/n8n-nodes-base/dist/nodes/CrowdDev/crowdDev.dark.svg"}, "supportedNodes": ["n8n-nodes-base.crowdDev", "n8n-nodes-base.crowdDevTrigger"]}, {"name": "customerIoApi", "displayName": "Customer.io API", "documentationUrl": "customerIo", "properties": [{"displayName": "Tracking API Key", "name": "trackingApiKey", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Required for tracking API", "required": true}, {"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "EU region", "value": "track-eu.customer.io"}, {"name": "Global region", "value": "track.customer.io"}], "default": "track.customer.io", "description": "Should be set based on your account region", "hint": "The region will be omitted when being used with the HTTP node", "required": true}, {"displayName": "Tracking Site ID", "name": "trackingSiteId", "type": "string", "default": "", "description": "Required for tracking API"}, {"displayName": "App API Key", "name": "appApiKey", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Required for App API"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/CustomerIo/customerio.svg", "dark": "icons/n8n-nodes-base/dist/nodes/CustomerIo/customerio.dark.svg"}, "authenticate": {}, "supportedNodes": ["n8n-nodes-base.customerIo", "n8n-nodes-base.customerIoTrigger"]}, {"name": "datadogApi", "displayName": "Datadog API", "documentationUrl": "datadog", "httpRequestNode": {"name": "Datadog", "docsUrl": "https://docs.datadoghq.com/api/latest/", "apiBaseUrlPlaceholder": "https://api.datadoghq.com/api/v1/metrics"}, "properties": [{"displayName": "URL", "name": "url", "required": true, "type": "string", "default": "https://api.datadoghq.com"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "APP Key", "name": "appKey", "required": false, "type": "string", "default": "", "typeOptions": {"password": true}, "description": "For some endpoints, you also need an Application key."}], "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/api/v1/validate", "method": "GET"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Datadog.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Datadog.svg"}, "authenticate": {}, "supportedNodes": []}, {"name": "deepLApi", "displayName": "DeepL API", "documentationUrl": "deepL", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "API Plan", "name": "apiPlan", "type": "options", "options": [{"name": "Pro Plan", "value": "pro"}, {"name": "Free Plan", "value": "free"}], "default": "pro"}], "authenticate": {"type": "generic", "properties": {"qs": {"auth_key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.apiPlan === \"pro\" ? \"https://api.deepl.com/v2\" : \"https://api-free.deepl.com/v2\" }}", "url": "/usage"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/DeepL/deepl.svg", "dark": "icons/n8n-nodes-base/dist/nodes/DeepL/deepL.dark.svg"}, "supportedNodes": ["n8n-nodes-base.deepL"]}, {"name": "demio<PERSON>pi", "displayName": "Demio API", "documentationUrl": "demio", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "API Secret", "name": "apiSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Demio/demio.svg", "supportedNodes": ["n8n-nodes-base.demio"]}, {"name": "dfir<PERSON><PERSON><PERSON><PERSON>", "displayName": "DFIR-IRIS API", "documentationUrl": "<PERSON><PERSON><PERSON><PERSON>", "httpRequestNode": {"name": "DFIR-IRIS", "docsUrl": "https://docs.dfir-iris.org/operations/api/", "apiBaseUrlPlaceholder": "http://<yourserver_ip>/manage/cases/list"}, "properties": [{"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "e.g. https://localhost", "description": "The API endpoints are reachable on the same Address and port as the web interface.", "required": true}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "skipSslCertificateValidation", "type": "boolean", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.baseUrl}}", "url": "/api/ping", "method": "GET", "skipSslCertificateValidation": "={{$credentials.skipSslCertificateValidation}}"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/DfirIris.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/DfirIris.svg"}, "supportedNodes": []}, {"name": "dhlApi", "displayName": "DHL API", "documentationUrl": "dhl", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dhl/dhl.svg", "supportedNodes": ["n8n-nodes-base.dhl"]}, {"name": "discordBotApi", "displayName": "Discord Bot API", "documentationUrl": "discord", "properties": [{"displayName": "Bot <PERSON>", "name": "botToken", "type": "string", "default": "", "required": true, "typeOptions": {"password": true}}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bot {{$credentials.botToken}}"}}}, "test": {"request": {"baseURL": "https://discord.com/api/v10/", "url": "/users/@me/guilds"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Discord/discord.svg", "supportedNodes": ["n8n-nodes-base.discord"]}, {"name": "discordOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Discord OAuth2 API", "documentationUrl": "discord", "properties": [{"displayName": "Bot <PERSON>", "name": "botToken", "type": "string", "default": "", "typeOptions": {"password": true}}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://discord.com/api/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://discord.com/api/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "identify guilds guilds.join bot", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "permissions=1642758929655"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Discord/discord.svg", "supportedNodes": ["n8n-nodes-base.discord"]}, {"name": "discordWebhookApi", "displayName": "Discord Webhook", "documentationUrl": "discord", "properties": [{"displayName": "Webhook URL", "name": "webhookUri", "type": "string", "required": true, "default": "", "placeholder": "https://discord.com/api/webhooks/ID/TOKEN", "typeOptions": {"password": true}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Discord/discord.svg", "supportedNodes": ["n8n-nodes-base.discord"]}, {"name": "discourseApi", "displayName": "Discourse API", "documentationUrl": "discourse", "properties": [{"displayName": "URL", "name": "url", "required": true, "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Username", "name": "username", "required": true, "type": "string", "default": ""}], "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/groups.json", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Discourse/discourse.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.discourse"]}, {"name": "disqus<PERSON><PERSON>", "displayName": "Disqus API", "documentationUrl": "disqus", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Visit your account details page, and grab the Access Token. See <a href=\"https://disqus.com/api/docs/auth/\">Disqus auth</a>."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Disqus/disqus.png", "supportedNodes": ["n8n-nodes-base.disqus"]}, {"name": "driftApi", "displayName": "Drift API", "documentationUrl": "drift", "properties": [{"displayName": "Personal Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Visit your account details page, and grab the Access Token. See <a href=\"https://devdocs.drift.com/docs/quick-start\">Drift auth</a>."}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Drift/drift.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Drift/drift.dark.svg"}, "supportedNodes": ["n8n-nodes-base.drift"]}, {"name": "driftOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Drift OAuth2 API", "documentationUrl": "drift", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://dev.drift.com/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://driftapi.com/oauth2/token", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Drift/drift.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Drift/drift.dark.svg"}, "supportedNodes": ["n8n-nodes-base.drift"]}, {"name": "dropboxApi", "displayName": "Dropbox API", "documentationUrl": "dropbox", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "APP Access Type", "name": "accessType", "type": "options", "options": [{"name": "App Folder", "value": "folder"}, {"name": "Full Dropbox", "value": "full"}], "default": "full"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.dropboxapi.com/2", "url": "/users/get_current_account", "method": "POST"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dropbox/dropbox.svg", "supportedNodes": ["n8n-nodes-base.dropbox"]}, {"name": "dropboxOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Dropbox OAuth2 API", "documentationUrl": "dropbox", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.dropbox.com/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.dropboxapi.com/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "files.content.write files.content.read sharing.read account_info.read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "token_access_type=offline&force_reapprove=true"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}, {"displayName": "APP Access Type", "name": "accessType", "type": "options", "options": [{"name": "App Folder", "value": "folder"}, {"name": "Full Dropbox", "value": "full"}], "default": "full"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dropbox/dropbox.svg", "supportedNodes": ["n8n-nodes-base.dropbox"]}, {"name": "dropcontactApi", "displayName": "Dropcontact API", "documentationUrl": "dropcontact", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"user-agent": "n8n", "X-Access-Token": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.dropcontact.io", "url": "/batch", "method": "POST", "body": {"data": [{"email": ""}]}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dropcontact/dropcontact.svg", "supportedNodes": ["n8n-nodes-base.dropcontact"]}, {"name": "dynatraceApi", "displayName": "DynatraceAPI", "documentationUrl": "dynatrace", "httpRequestNode": {"name": "Dynatrace", "docsUrl": "https://docs.dynatrace.com/docs/dynatrace-api", "apiBaseUrlPlaceholder": "https://{your-environment-id}.live.dynatrace.com/api/v2/events"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Api-Token {{$credentials.apiKey}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Dynatrace.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Dynatrace.svg"}, "supportedNodes": []}, {"name": "egoi<PERSON><PERSON>", "displayName": "E-Goi API", "documentationUrl": "egoi", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Egoi/egoi.svg", "supportedNodes": ["n8n-nodes-base.egoi"]}, {"name": "elasticsearchApi", "displayName": "Elasticsearch API", "documentationUrl": "elasticsearch", "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "https://mydeployment.es.us-central1.gcp.cloud.es.io:9243", "description": "Referred to as Elasticsearch 'endpoint' in the Elastic deployment dashboard"}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "ignoreSSLIssues", "type": "boolean", "default": false}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials.baseUrl}}", "url": "/_xpack?human=false", "skipSslCertificateValidation": "={{$credentials.ignoreSSLIssues}}"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Elastic/Elasticsearch/elasticsearch.svg", "supportedNodes": ["n8n-nodes-base.elasticsearch"]}, {"name": "elasticSecurityApi", "displayName": "Elastic Security API", "documentationUrl": "elasticSecurity", "properties": [{"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "e.g. https://mydeployment.kb.us-central1.gcp.cloud.es.io:9243", "description": "Referred to as Kibana 'endpoint' in the Elastic deployment dashboard", "required": true}, {"displayName": "Type", "name": "type", "type": "options", "options": [{"name": "API Key", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Basic Auth", "value": "basicAuth"}], "default": "basicAuth"}, {"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true, "displayOptions": {"show": {"type": ["basicAuth"]}}}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true, "displayOptions": {"show": {"type": ["basicAuth"]}}}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"type": ["<PERSON><PERSON><PERSON><PERSON>"]}}}], "test": {"request": {"baseURL": "={{$credentials.baseUrl}}", "url": "/api/endpoint/metadata", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Elastic/ElasticSecurity/elasticSecurity.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.elasticSecurity"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Emelia API", "documentationUrl": "emelia", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Emelia/emelia.svg", "supportedNodes": ["n8n-nodes-base.emelia", "n8n-nodes-base.em<PERSON><PERSON>"]}, {"name": "erpNextApi", "displayName": "ERPNext API", "documentationUrl": "erpnext", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "API Secret", "name": "apiSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Environment", "name": "environment", "type": "options", "default": "cloudHosted", "options": [{"name": "Cloud-Hosted", "value": "cloudHosted"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": "", "placeholder": "n8n", "description": "Subdomain of cloud-hosted ERPNext instance. For example, \"n8n\" is the subdomain in: <code>https://n8n.erpnext.com</code>", "displayOptions": {"show": {"environment": ["cloudHosted"]}}}, {"displayName": "Domain", "name": "domain", "type": "options", "default": "erpnext.com", "options": [{"name": "erpnext.com", "value": "erpnext.com"}, {"name": "frappe.cloud", "value": "frappe.cloud"}], "description": "Domain for your cloud hosted ERPNext instance.", "displayOptions": {"show": {"environment": ["cloudHosted"]}}}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://www.mydomain.com", "description": "Fully qualified domain name of self-hosted ERPNext instance", "displayOptions": {"show": {"environment": ["selfHosted"]}}}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=token {{$credentials.apiKey}}:{{$credentials.apiSecret}}"}}}, "test": {"request": {"baseURL": "={{$credentials.environment === \"cloudHosted\" ? \"https://\" + $credentials.subdomain + \".\" + $credentials.domain : $credentials.domain}}", "url": "/api/method/frappe.auth.get_logged_user", "skipSslCertificateValidation": "={{ $credentials.allowUnauthorizedCerts }}"}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "message", "message": "Unable to authenticate, Check the credentials and the url"}}]}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/ERPNext/erpnext.svg", "supportedNodes": ["n8n-nodes-base.erpNext"]}, {"name": "eventbriteApi", "displayName": "Eventbrite API", "documentationUrl": "eventbrite", "properties": [{"displayName": "Private Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Eventbrite/eventbrite.png", "supportedNodes": ["n8n-nodes-base.eventbriteTrigger"]}, {"name": "eventbriteOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Eventbrite OAuth2 API", "documentationUrl": "eventbrite", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.eventbrite.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://www.eventbrite.com/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Eventbrite/eventbrite.png", "supportedNodes": ["n8n-nodes-base.eventbriteTrigger"]}, {"name": "f5BigIpApi", "displayName": "F5 Big-IP API", "documentationUrl": "f5bigip", "httpRequestNode": {"name": "F5 Big-IP", "docsUrl": "https://clouddocs.f5.com/api/", "apiBaseUrl": ""}, "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/F5.svg", "supportedNodes": []}, {"name": "facebookGraphApi", "displayName": "Facebook Graph API", "documentationUrl": "facebookgraph", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"access_token": "={{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://graph.facebook.com/v8.0", "url": "/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Facebook/facebook.svg", "supportedNodes": ["n8n-nodes-base.facebookGraphApi"]}, {"name": "facebookGraphAppApi", "displayName": "Facebook Graph API (App)", "documentationUrl": "facebookapp", "extends": ["facebookGraphApi"], "properties": [{"displayName": "App Secret", "name": "appSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "(Optional) When the app secret is set the node will verify this signature to validate the integrity and origin of the payload"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Facebook/facebook.svg", "supportedNodes": ["n8n-nodes-base.facebookTrigger"]}, {"name": "facebookLeadAdsOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Facebook Lead Ads OAuth2 API", "documentationUrl": "facebookleadads", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.facebook.com/v17.0/dialog/oauth", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://graph.facebook.com/v17.0/oauth/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "leads_retrieval pages_show_list pages_manage_metadata pages_manage_ads business_management"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FacebookLeadAds/facebook.svg", "supportedNodes": ["n8n-nodes-base.facebookLeadAdsTrigger"]}, {"name": "figmaApi", "displayName": "Figma API", "documentationUrl": "figma", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Figma/figma.svg", "supportedNodes": ["n8n-nodes-base.figmaTrigger"]}, {"name": "fileMaker", "displayName": "FileMaker API", "documentationUrl": "fileMaker", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": ""}, {"displayName": "Database", "name": "db", "type": "string", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "login", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FileMaker/filemaker.png", "supportedNodes": ["n8n-nodes-base.filemaker"]}, {"name": "filescanApi", "displayName": "Filescan API", "documentationUrl": "filescan", "httpRequestNode": {"name": "Filescan", "docsUrl": "https://www.filescan.io/api/docs", "apiBaseUrlPlaceholder": "https://www.filescan.io/api/system/do-healthcheck"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Api-Key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://www.filescan.io/api", "url": "/system/do-healthcheck", "method": "GET"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Filescan.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Filescan.svg"}, "supportedNodes": []}, {"name": "flowApi", "displayName": "Flow API", "documentationUrl": "flow", "properties": [{"displayName": "Organization ID", "name": "organizationId", "type": "number", "default": 0}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Flow/flow.svg", "supportedNodes": ["n8n-nodes-base.flow", "n8n-nodes-base.flowTrigger"]}, {"name": "formIoApi", "displayName": "Form.io API", "documentationUrl": "formIoTrigger", "properties": [{"displayName": "Environment", "name": "environment", "type": "options", "default": "cloudHosted", "options": [{"name": "Cloud-Hosted", "value": "cloudHosted"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Self-Hosted Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://www.mydomain.com", "displayOptions": {"show": {"environment": ["selfHosted"]}}}, {"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FormIo/formio.svg", "supportedNodes": ["n8n-nodes-base.formIoTrigger"]}, {"name": "formstackApi", "displayName": "Formstack API", "documentationUrl": "formstackTrigger", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Formstack/formstack.svg", "supportedNodes": ["n8n-nodes-base.formstackTrigger"]}, {"name": "formstackOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Formstack OAuth2 API", "documentationUrl": "formstackTrigger", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.formstack.com/api/v2/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://www.formstack.com/api/v2/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Formstack/formstack.svg", "supportedNodes": ["n8n-nodes-base.formstackTrigger"]}, {"name": "fortiGateApi", "displayName": "Fortinet FortiGate API", "documentationUrl": "fortigate", "httpRequestNode": {"name": "Fortinet FortiGate", "docsUrl": "https://docs.fortinet.com/document/fortigate/7.4.1/administration-guide/940602/using-apis", "apiBaseUrl": ""}, "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"qs": {"access_token": "={{$credentials.accessToken}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Fortinet.svg", "supportedNodes": []}, {"name": "freshdeskApi", "displayName": "Freshdesk API", "documentationUrl": "freshdesk", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Domain", "name": "domain", "type": "string", "placeholder": "company", "description": "If the URL you get displayed on Freshdesk is \"https://company.freshdesk.com\" enter \"company\"", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Freshdesk/freshdesk.svg", "supportedNodes": ["n8n-nodes-base.freshdesk"]}, {"name": "freshserviceApi", "displayName": "Freshservice API", "documentationUrl": "freshservice", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "placeholder": "atuH3AbeH9HsKvgHuxg"}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "n8n", "description": "Domain in the Freshservice org URL. For example, in <code>https://n8n.freshservice.com</code>, the domain is <code>n8n</code>"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Freshservice/freshservice.svg", "supportedNodes": ["n8n-nodes-base.freshservice"]}, {"name": "freshworksCrmApi", "displayName": "Freshworks CRM API", "documentationUrl": "freshdesk", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "placeholder": "BDsTn15vHezBlt_XGp3Tig"}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "n8n-org", "description": "Domain in the Freshworks CRM org URL. For example, in <code>https://n8n-org.myfreshworks.com</code>, the domain is <code>n8n-org</code>."}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Token token={{$credentials?.apiKey}}"}}}, "test": {"request": {"baseURL": "=https://{{$credentials?.domain}}.myfreshworks.com/crm/sales/api", "url": "/tasks", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/FreshworksCrm/freshworksCrm.svg", "supportedNodes": ["n8n-nodes-base.freshworksCrm"]}, {"name": "ftp", "displayName": "FTP", "documentationUrl": "ftp", "properties": [{"displayName": "Host", "name": "host", "required": true, "type": "string", "default": "", "placeholder": "localhost"}, {"displayName": "Port", "name": "port", "required": true, "type": "number", "default": 21}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "icon": "fa:server", "iconColor": "dark-blue", "supportedNodes": ["n8n-nodes-base.ftp"]}, {"name": "getResponseApi", "displayName": "GetResponse API", "documentationUrl": "getResponse", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Auth-Token": "=api-key {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.getresponse.com/v3", "url": "/campaigns"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/GetResponse/getResponse.png", "supportedNodes": ["n8n-nodes-base.getResponse", "n8n-nodes-base.getResponseTrigger"]}, {"name": "getResponseOAuth2Api", "extends": ["oAuth2Api"], "displayName": "GetResponse OAuth2 API", "documentationUrl": "getresponse", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.getresponse.com/oauth2_authorize.html", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.getresponse.com/v3/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GetResponse/getResponse.png", "supportedNodes": ["n8n-nodes-base.getResponse", "n8n-nodes-base.getResponseTrigger"]}, {"name": "ghostAdminApi", "displayName": "Ghost Admin API", "documentationUrl": "ghost", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "http://localhost:3001"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/ghost/api/v2/admin/pages/"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Ghost/ghost.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.ghost"]}, {"name": "ghostContentApi", "displayName": "Ghost Content API", "documentationUrl": "ghost", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "http://localhost:3001"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/ghost/api/v3/content/settings/", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Ghost/ghost.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.ghost"]}, {"name": "githubApi", "displayName": "GitHub API", "documentationUrl": "github", "properties": [{"displayName": "Github Server", "name": "server", "type": "string", "default": "https://api.github.com", "description": "The server to connect to. Only has to be set if Github Enterprise is used."}, {"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=token {{$credentials?.accessToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.server}}", "url": "/user", "method": "GET"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Github/github.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Github/github.dark.svg"}, "supportedNodes": ["n8n-nodes-base.github", "n8n-nodes-base.githubTrigger"]}, {"name": "githubOAuth2Api", "extends": ["oAuth2Api"], "displayName": "GitHub OAuth2 API", "documentationUrl": "github", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Github Server", "name": "server", "type": "string", "default": "https://api.github.com", "description": "The server to connect to. Only has to be set if Github Enterprise is used."}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "={{$self[\"server\"] === \"https://api.github.com\" ? \"https://github.com\" : $self[\"server\"]}}/login/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "={{$self[\"server\"] === \"https://api.github.com\" ? \"https://github.com\" : $self[\"server\"]}}/login/oauth/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "repo,admin:repo_hook,admin:org,admin:org_hook,gist,notifications,user,write:packages,read:packages,delete:packages,workflow"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Github/github.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Github/github.dark.svg"}, "supportedNodes": ["n8n-nodes-base.github", "n8n-nodes-base.githubTrigger"]}, {"name": "gitlabApi", "displayName": "GitLab API", "documentationUrl": "gitlab", "properties": [{"displayName": "Gitlab Server", "name": "server", "type": "string", "default": "https://gitlab.com"}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Private-Token": "={{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials.server.replace(new RegExp(\"/$\"), \"\") + \"/api/v4\" }}", "url": "/personal_access_tokens/self"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gitlab/gitlab.svg", "supportedNodes": ["n8n-nodes-base.gitlab", "n8n-nodes-base.gitlabTrigger"]}, {"name": "gitlabOAuth2Api", "extends": ["oAuth2Api"], "displayName": "GitLab OAuth2 API", "documentationUrl": "gitlab", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Gitlab Server", "name": "server", "type": "string", "default": "https://gitlab.com"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "={{$self[\"server\"]}}/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "={{$self[\"server\"]}}/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "api"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gitlab/gitlab.svg", "supportedNodes": ["n8n-nodes-base.gitlab", "n8n-nodes-base.gitlabTrigger"]}, {"name": "gitPassword", "displayName": "Git", "documentationUrl": "git", "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": "", "description": "The username to authenticate with"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The password to use in combination with the user"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Git/git.svg", "supportedNodes": ["n8n-nodes-base.git"]}, {"name": "gmailOAuth2", "extends": ["googleOAuth2Api"], "displayName": "Gmail OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/gmail.labels https://www.googleapis.com/auth/gmail.addons.current.action.compose https://www.googleapis.com/auth/gmail.addons.current.message.action https://mail.google.com/ https://www.googleapis.com/auth/gmail.modify https://www.googleapis.com/auth/gmail.compose"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Gmail/gmail.svg", "supportedNodes": ["n8n-nodes-base.gmail", "n8n-nodes-base.gmailTrigger"]}, {"name": "gongApi", "displayName": "Gong API", "documentationUrl": "gong", "properties": [{"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "https://api.gong.io"}, {"displayName": "Access Key", "name": "accessKey", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Access Key Secret", "name": "accessKeySecret", "type": "string", "default": "", "typeOptions": {"password": true}}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{ $credentials.accessKey }}", "password": "={{ $credentials.accessKeySecret }}"}}}, "test": {"request": {"baseURL": "={{ $credentials.baseUrl.replace(new RegExp(\"/$\"), \"\") }}", "url": "/v2/users"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gong/gong.svg", "supportedNodes": ["n8n-nodes-base.gong"]}, {"name": "gongOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Gong OAuth2 API", "documentationUrl": "gong", "properties": [{"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "https://api.gong.io"}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.gong.io/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://app.gong.io/oauth2/generate-customer-token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "api:calls:read:transcript api:provisioning:read api:workspaces:read api:meetings:user:delete api:crm:get-objects api:data-privacy:delete api:crm:schema api:flows:write api:crm:upload api:meetings:integration:status api:calls:read:extensive api:meetings:user:update api:integration-settings:write api:settings:scorecards:read api:stats:scorecards api:stats:interaction api:stats:user-actions api:crm:integration:delete api:calls:read:basic api:calls:read:media-url api:digital-interactions:write api:crm:integrations:read api:library:read api:data-privacy:read api:users:read api:logs:read api:calls:create api:meetings:user:create api:stats:user-actions:detailed api:settings:trackers:read api:crm:integration:register api:provisioning:read-write api:engagement-data:write api:permission-profile:read api:permission-profile:write api:flows:read api:crm-calls:manual-association:read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gong/gong.svg", "supportedNodes": ["n8n-nodes-base.gong"]}, {"name": "googleAdsOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Ads OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "Developer Token", "name": "developerToken", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/adwords"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Ads/googleAds.svg", "supportedNodes": ["n8n-nodes-base.googleAds"]}, {"name": "googleAnalyticsOAuth2", "extends": ["googleOAuth2Api"], "displayName": "Google Analytics OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/analytics https://www.googleapis.com/auth/analytics.readonly"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Analytics/analytics.svg", "supportedNodes": ["n8n-nodes-base.googleAnalytics"]}, {"name": "googleApi", "displayName": "Google Service Account API", "documentationUrl": "google/service-account", "properties": [{"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "Africa (Johannesburg) - africa-south1", "value": "africa-south1"}, {"name": "Asia Pacific (Changhua County) - asia-east1", "value": "asia-east1"}, {"name": "Asia Pacific (Hong Kong) - asia-east2", "value": "asia-east2"}, {"name": "Asia Pacific (Tokyo) - asia-northeast1", "value": "asia-northeast1"}, {"name": "Asia Pacific (Osaka) - asia-northeast2", "value": "asia-northeast2"}, {"name": "Asia Pacific (Seoul) - asia-northeast3", "value": "asia-northeast3"}, {"name": "Asia Pacific (Mumbai) - asia-south1", "value": "asia-south1"}, {"name": "Asia Pacific (Delhi) - asia-south2", "value": "asia-south2"}, {"name": "Asia Pacific (Jurong West) - asia-southeast1", "value": "asia-southeast1"}, {"name": "Asia Pacific (Jakarta) - asia-southeast2", "value": "asia-southeast2"}, {"name": "Asia Pacific (Sydney) - australia-southeast1", "value": "australia-southeast1"}, {"name": "Asia Pacific (Melbourne) - australia-southeast2", "value": "australia-southeast2"}, {"name": "Europe (Warsaw) - europe-central2", "value": "europe-central2"}, {"name": "Europe (Hamina) - europe-north1", "value": "europe-north1"}, {"name": "Europe (Madrid) - europe-southwest1", "value": "europe-southwest1"}, {"name": "Europe (<PERSON><PERSON>) - europe-west1", "value": "europe-west1"}, {"name": "Europe (Berlin) - europe-west10", "value": "europe-west10"}, {"name": "Europe (Turin) - europe-west12", "value": "europe-west12"}, {"name": "Europe (London) - europe-west2", "value": "europe-west2"}, {"name": "Europe (Frankfurt) - europe-west3", "value": "europe-west3"}, {"name": "Europe (Eemshaven) - europe-west4", "value": "europe-west4"}, {"name": "Europe (Zurich) - europe-west6", "value": "europe-west6"}, {"name": "Europe (Milan) - europe-west8", "value": "europe-west8"}, {"name": "Europe (Paris) - europe-west9", "value": "europe-west9"}, {"name": "Middle East (Doha) - me-central1", "value": "me-central1"}, {"name": "Middle East (Dammam) - me-central2", "value": "me-central2"}, {"name": "Middle East (Tel Aviv) - me-west1", "value": "me-west1"}, {"name": "Americas (Montréal) - northamerica-northeast1", "value": "northamerica-northeast1"}, {"name": "Americas (Toronto) - northamerica-northeast2", "value": "northamerica-northeast2"}, {"name": "Americas (Queretaro) - northamerica-south1", "value": "northamerica-south1"}, {"name": "Americas (Osasco) - southamerica-east1", "value": "southamerica-east1"}, {"name": "Americas (Santiago) - southamerica-west1", "value": "southamerica-west1"}, {"name": "Americas (Council Bluffs) - us-central1", "value": "us-central1"}, {"name": "Americas (Moncks Corner) - us-east1", "value": "us-east1"}, {"name": "Americas (Ashburn) - us-east4", "value": "us-east4"}, {"name": "Americas (Columbus) - us-east5", "value": "us-east5"}, {"name": "Americas (Dallas) - us-south1", "value": "us-south1"}, {"name": "Americas (The Dalles) - us-west1", "value": "us-west1"}, {"name": "Americas (Los Angeles) - us-west2", "value": "us-west2"}, {"name": "Americas (Salt Lake City) - us-west3", "value": "us-west3"}, {"name": "Americas (Las Vegas) - us-west4", "value": "us-west4"}], "default": "us-central1", "description": "The region where the Google Cloud service is located. This applies only to specific nodes, like the Google Vertex Chat Model"}, {"displayName": "Service Account Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": "", "description": "The Google Service account <NAME_EMAIL>", "required": true}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "default": "", "placeholder": "-----B<PERSON>IN PRIVATE KEY-----\nXIYEvQIBADANBg<...>0IhA7TMoGYPQc=\n-----END PRIVATE KEY-----\n", "description": "Enter the private key located in the JSON file downloaded from Google Cloud Console", "required": true, "typeOptions": {"password": true}}, {"displayName": "Impersonate a User", "name": "inpersonate", "type": "boolean", "default": false}, {"displayName": "Email", "name": "delegatedEmail", "type": "string", "default": "", "displayOptions": {"show": {"inpersonate": [true]}}, "description": "The email address of the user for which the application is requesting delegated access"}, {"displayName": "Set up for use in HTTP Request node", "name": "httpNode", "type": "boolean", "default": false}, {"displayName": "When using the HTTP Request node, you must specify the scopes you want to send. In other nodes, they're added automatically", "name": "httpWarning", "type": "notice", "default": "", "displayOptions": {"show": {"httpNode": [true]}}}, {"displayName": "<PERSON>ope(s)", "name": "scopes", "type": "string", "default": "", "description": "You can find the scopes for services <a href=\"https://developers.google.com/identity/protocols/oauth2/scopes\" target=\"_blank\">here</a>", "displayOptions": {"show": {"httpNode": [true]}}}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Google.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.googleBigQuery", "n8n-nodes-base.googleBooks", "n8n-nodes-base.googleChat", "n8n-nodes-base.googleDocs", "n8n-nodes-base.googleDrive", "n8n-nodes-base.googleDriveTrigger", "n8n-nodes-base.googleFirebaseCloudFirestore", "n8n-nodes-base.gmail", "n8n-nodes-base.gmailTrigger", "n8n-nodes-base.googleSheets", "n8n-nodes-base.googleSlides", "n8n-nodes-base.googleTranslate"]}, {"name": "googleBigQueryOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google BigQuery OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/bigquery https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/drive"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/BigQuery/googleBigQuery.svg", "supportedNodes": ["n8n-nodes-base.googleBigQuery"]}, {"name": "googleBooksOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Books OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/books"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Books/googlebooks.svg", "supportedNodes": ["n8n-nodes-base.googleBooks"]}, {"name": "googleCalendarOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Calendar OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/calendar.events"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Calendar/googleCalendar.svg", "supportedNodes": ["n8n-nodes-base.googleCalendar", "n8n-nodes-base.googleCalendarTrigger"]}, {"name": "googleChatOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Chat OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/chat.spaces https://www.googleapis.com/auth/chat.messages https://www.googleapis.com/auth/chat.memberships"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Chat/googleChat.svg", "supportedNodes": ["n8n-nodes-base.googleChat"]}, {"name": "googleCloudNaturalLanguageOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Cloud Natural Language OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/cloud-language https://www.googleapis.com/auth/cloud-platform"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/CloudNaturalLanguage/googlecloudnaturallanguage.png", "supportedNodes": ["n8n-nodes-base.googleCloudNaturalLanguage"]}, {"name": "googleCloudStorageOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Cloud Storage OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/cloud-platform.read-only https://www.googleapis.com/auth/devstorage.full_control https://www.googleapis.com/auth/devstorage.read_only https://www.googleapis.com/auth/devstorage.read_write"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/CloudStorage/googleCloudStorage.svg", "supportedNodes": ["n8n-nodes-base.googleCloudStorage"]}, {"name": "googleContactsOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Contacts OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/contacts"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Contacts/googleContacts.png", "supportedNodes": ["n8n-nodes-base.googleContacts"]}, {"name": "googleDocsOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Docs OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/documents https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/drive.file"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Docs/googleDocs.svg", "supportedNodes": ["n8n-nodes-base.googleDocs"]}, {"name": "googleDriveOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Drive OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/drive.appdata https://www.googleapis.com/auth/drive.photos.readonly"}, {"displayName": "Make sure that you have enabled the Google Drive API in the Google Cloud Console. <a href=\"https://docs.n8n.io/integrations/builtin/credentials/google/oauth-generic/#scopes\" target=\"_blank\">More info</a>.", "name": "notice", "type": "notice", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Drive/googleDrive.svg", "supportedNodes": ["n8n-nodes-base.googleDrive", "n8n-nodes-base.googleDriveTrigger"]}, {"name": "googleFirebaseCloudFirestoreOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Firebase Cloud Firestore OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/datastore https://www.googleapis.com/auth/firebase"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Firebase/CloudFirestore/googleFirebaseCloudFirestore.png", "supportedNodes": ["n8n-nodes-base.googleFirebaseCloudFirestore"]}, {"name": "googleFirebaseRealtimeDatabaseOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Firebase Realtime Database OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/firebase.database https://www.googleapis.com/auth/firebase"}, {"displayName": "Region", "name": "region", "type": "options", "default": "firebaseio.com", "options": [{"name": "us-central1", "value": "firebaseio.com"}, {"name": "europe-west1", "value": "europe-west1.firebasedatabase.app"}, {"name": "asia-southeast1", "value": "asia-southeast1.firebasedatabase.app"}]}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Firebase/RealtimeDatabase/googleFirebaseRealtimeDatabase.svg", "supportedNodes": ["n8n-nodes-base.googleFirebaseRealtimeDatabase"]}, {"name": "googleBusinessProfileOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Business Profile OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/business.manage"}, {"displayName": "Make sure that you have fulfilled the prerequisites and requested access to Google Business Profile API. <a href=\"https://developers.google.com/my-business/content/prereqs\" target=\"_blank\">More info</a>. Also, make sure that you have enabled the following APIs & Services in the Google Cloud Console: Google My Business API, Google My Business Management API. <a href=\"https://docs.n8n.io/integrations/builtin/credentials/google/oauth-generic/#scopes\" target=\"_blank\">More info</a>.", "name": "notice", "type": "notice", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/BusinessProfile/googleBusinessProfile.svg", "supportedNodes": ["n8n-nodes-base.googleBusinessProfile", "n8n-nodes-base.googleBusinessProfileTrigger"]}, {"name": "googleOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Google OAuth2 API", "documentationUrl": "google/oauth-generic", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://accounts.google.com/o/oauth2/v2/auth"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://oauth2.googleapis.com/token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "access_type=offline&prompt=consent"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Google.svg", "supportedNodes": []}, {"name": "googlePerspectiveOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Perspective OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/userinfo.email"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Google/Perspective/googlePerspective.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Google/Perspective/googlePerspective.dark.svg"}, "supportedNodes": ["n8n-nodes-base.googlePerspective"]}, {"name": "googleSheetsOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Sheets OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/drive.file https://www.googleapis.com/auth/spreadsheets https://www.googleapis.com/auth/drive.metadata"}, {"displayName": "Make sure you enabled the following APIs & Services in the Google Cloud Console: Google Drive API, Google Sheets API. <a href=\"https://docs.n8n.io/integrations/builtin/credentials/google/oauth-generic/#scopes\" target=\"_blank\">More info</a>.", "name": "notice", "type": "notice", "default": "", "displayOptions": {"hideOnCloud": true}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Sheet/googleSheets.svg", "supportedNodes": ["n8n-nodes-base.googleSheets"]}, {"name": "googleSheetsTriggerOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Sheets Trigger OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/drive.file https://www.googleapis.com/auth/spreadsheets https://www.googleapis.com/auth/drive.metadata"}, {"displayName": "Make sure you have enabled the following APIs & Services in the Google Cloud Console: Google Drive API, Google Sheets API. <a href=\"https://docs.n8n.io/integrations/builtin/credentials/google/oauth-generic/#scopes\" target=\"_blank\">More info</a>.", "name": "notice", "type": "notice", "default": "", "displayOptions": {"hideOnCloud": true}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Sheet/googleSheets.svg", "supportedNodes": ["n8n-nodes-base.googleSheetsTrigger"]}, {"name": "googleSlidesOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Slides OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/drive.file https://www.googleapis.com/auth/presentations"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Slides/googleslides.svg", "supportedNodes": ["n8n-nodes-base.googleSlides"]}, {"name": "googleTasksOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Tasks OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/tasks"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Task/googleTasks.svg", "supportedNodes": ["n8n-nodes-base.googleTasks"]}, {"name": "googleTranslateOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Translate OAuth2 API", "documentationUrl": "google/oauth-single-service", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/cloud-translation"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Translate/googletranslate.png", "supportedNodes": ["n8n-nodes-base.googleTranslate"]}, {"name": "gotifyApi", "displayName": "Gotify API", "documentationUrl": "gotify", "properties": [{"displayName": "App API Token", "name": "appApiToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "(Optional) Needed for message creation"}, {"displayName": "Client API Token", "name": "clientApiToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "(Optional) Needed for everything (delete, getAll) but message creation"}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "description": "The URL of the Gotify host"}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "ignoreSSLIssues", "type": "boolean", "default": false, "description": "Whether to connect even if SSL certificate validation is not possible"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gotify/gotify.png", "supportedNodes": ["n8n-nodes-base.gotify"]}, {"name": "goToWebinarOAuth2Api", "extends": ["oAuth2Api"], "displayName": "GoToWebinar OAuth2 API", "documentationUrl": "goToWebinar", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://api.getgo.com/oauth/v2/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.getgo.com/oauth/v2/token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GoToWebinar/gotowebinar.svg", "supportedNodes": ["n8n-nodes-base.goToWebinar"]}, {"name": "grist<PERSON>pi", "displayName": "Grist API", "documentationUrl": "grist", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Plan Type", "name": "planType", "type": "options", "default": "free", "options": [{"name": "Free", "value": "free"}, {"name": "Paid", "value": "paid"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Custom Subdomain", "name": "customSubdomain", "type": "string", "default": "", "required": true, "description": "Custom subdomain of your team", "displayOptions": {"show": {"planType": ["paid"]}}}, {"displayName": "Self-Hosted URL", "name": "selfHostedUrl", "type": "string", "default": "", "placeholder": "http://localhost:8484", "required": true, "description": "URL of your Grist instance. Include http/https without /api and no trailing slash.", "displayOptions": {"show": {"planType": ["selfHosted"]}}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Grist/grist.svg", "supportedNodes": ["n8n-nodes-base.grist"]}, {"name": "grafana<PERSON><PERSON>", "displayName": "Grafana API", "documentationUrl": "grafana", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "description": "Base URL of your Grafana instance", "placeholder": "e.g. https://n8n.grafana.net/", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.baseUrl.replace(new RegExp(\"/$\"), \"\") + \"/api\" }}", "url": "/folders"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Grafana/grafana.svg", "supportedNodes": ["n8n-nodes-base.grafana"]}, {"name": "gSuiteAdminOAuth2Api", "extends": ["googleOAuth2Api"], "displayName": "Google Workspace Admin OAuth2 API", "documentationUrl": "google", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/admin.directory.group https://www.googleapis.com/auth/admin.directory.user https://www.googleapis.com/auth/admin.directory.domain.readonly https://www.googleapis.com/auth/admin.directory.userschema.readonly https://www.googleapis.com/auth/admin.directory.device.chromeos https://www.googleapis.com/auth/admin.directory.orgunit.readonly"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/GSuiteAdmin/gSuiteAdmin.svg", "supportedNodes": ["n8n-nodes-base.gSuiteAdmin"]}, {"name": "gumroadApi", "displayName": "Gumroad API", "documentationUrl": "gumroad", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gumroad/gumroad.png", "supportedNodes": ["n8n-nodes-base.gumroadTrigger"]}, {"name": "haloPSAApi", "displayName": "HaloPSA API", "documentationUrl": "halopsa", "properties": [{"displayName": "Hosting Type", "name": "hostingType", "type": "options", "options": [{"name": "On-Premise Solution", "value": "onPremise"}, {"name": "Hosted Solution Of Halo", "value": "hostedHalo"}], "default": "onPremise"}, {"displayName": "HaloPSA Authorisation Server URL", "name": "authUrl", "type": "string", "default": "", "required": true}, {"displayName": "Resource Server", "name": "resourceApiUrl", "type": "string", "default": "", "required": true, "description": "The Resource server is available at your \"Halo Web Application URL/api\""}, {"displayName": "Client ID", "name": "client_id", "type": "string", "default": "", "required": true, "description": "Must be your application client ID"}, {"displayName": "Client Secret", "name": "client_secret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true, "description": "Must be your application client secret"}, {"displayName": "Tenant", "name": "tenant", "type": "string", "displayOptions": {"show": {"hostingType": ["hostedHalo"]}}, "default": "", "description": "An additional tenant parameter for HaloPSA hosted solution"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "admin edit:tickets edit:customers", "required": true}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HaloPSA/halopsa.svg", "supportedNodes": ["n8n-nodes-base.haloPSA"]}, {"name": "harvestApi", "displayName": "Harvest API", "documentationUrl": "harvest", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Visit your account details page, and grab the Access Token. See <a href=\"https://help.getharvest.com/api-v2/authentication-api/authentication/authentication/\">Harvest Personal Access Tokens</a>."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Harvest/harvest.png", "supportedNodes": ["n8n-nodes-base.harvest"]}, {"name": "harvestOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Harvest OAuth2 API", "documentationUrl": "harvest", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://id.getharvest.com/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://id.getharvest.com/api/v2/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "all"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Harvest/harvest.png", "supportedNodes": ["n8n-nodes-base.harvest"]}, {"name": "helpScoutOAuth2Api", "extends": ["oAuth2Api"], "displayName": "HelpScout OAuth2 API", "documentationUrl": "helpScout", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://secure.helpscout.net/authentication/authorizeClientApplication", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.helpscout.net/v2/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HelpScout/helpScout.svg", "supportedNodes": ["n8n-nodes-base.helpScout", "n8n-nodes-base.helpScoutTrigger"]}, {"name": "highLevelApi", "displayName": "HighLevel API", "documentationUrl": "highLevel", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://rest.gohighlevel.com/v1", "url": "/custom-values/"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/HighLevel/highLevel.svg", "supportedNodes": ["n8n-nodes-base.highLevel"]}, {"name": "highLevelOAuth2Api", "extends": ["oAuth2Api"], "displayName": "HighLevel OAuth2 API", "documentationUrl": "highLevel", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "options", "default": "https://marketplace.leadconnectorhq.com/oauth/chooselocation", "required": true, "options": [{"name": "White-Label", "value": "https://marketplace.leadconnectorhq.com/oauth/chooselocation"}, {"name": "Standard", "value": "https://marketplace.gohighlevel.com/oauth/chooselocation"}]}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "string", "hint": "Separate scopes by space, scopes needed for node: 'locations.readonly contacts.readonly contacts.write opportunities.readonly opportunities.write users.readonly'", "default": "", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://services.leadconnectorhq.com/oauth/token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}, {"displayName": "Make sure your credentials include the required OAuth scopes for all actions this node performs.", "name": "notice", "type": "notice", "default": "", "displayOptions": {"hideOnCloud": true}}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/highLevel.svg", "supportedNodes": ["n8n-nodes-base.highLevel"]}, {"name": "homeAssistantApi", "displayName": "Home Assistant API", "documentationUrl": "homeAssistant", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 8123}, {"displayName": "SSL", "name": "ssl", "type": "boolean", "default": false}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HomeAssistant/homeAssistant.svg", "supportedNodes": ["n8n-nodes-base.homeAssistant"]}, {"name": "httpBasicAuth", "displayName": "Basic Auth", "documentationUrl": "httpRequest", "genericAuth": true, "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "supportedNodes": ["n8n-nodes-base.formTrigger", "n8n-nodes-base.graphql", "n8n-nodes-base.httpRequest", "n8n-nodes-base.pipedriveTrigger", "n8n-nodes-base.wait", "n8n-nodes-base.webhook"]}, {"name": "httpBearerAuth", "displayName": "Bearer <PERSON>", "documentationUrl": "httpRequest", "genericAuth": true, "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "<PERSON><PERSON>", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "This credential uses the \"Authorization\" header. To use a custom header, use a \"Custom Auth\" credential instead", "name": "useCustomAuth", "type": "notice", "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "Bearer ={{$credentials.token}}"}}}, "supportedNodes": []}, {"name": "httpDigestAuth", "displayName": "Digest Auth", "documentationUrl": "httpRequest", "genericAuth": true, "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "supportedNodes": ["n8n-nodes-base.graphql", "n8n-nodes-base.httpRequest"]}, {"name": "httpHeaderAuth", "displayName": "Header <PERSON>", "documentationUrl": "httpRequest", "genericAuth": true, "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "Name", "name": "name", "type": "string", "default": ""}, {"displayName": "Value", "name": "value", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "To send multiple headers, use a \"Custom Auth\" credential instead", "name": "useCustomAuth", "type": "notice", "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"={{$credentials.name}}": "={{$credentials.value}}"}}}, "supportedNodes": ["n8n-nodes-base.graphql", "n8n-nodes-base.httpRequest", "n8n-nodes-base.wait", "n8n-nodes-base.webhook"]}, {"name": "httpCustomAuth", "displayName": "Custom Auth", "documentationUrl": "httpRequest", "genericAuth": true, "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "JSON", "name": "json", "type": "json", "required": true, "description": "Use json to specify authentication values for headers, body and qs.", "placeholder": "{ \"headers\": { \"key\" : \"value\" }, \"body\": { \"key\": \"value\" }, \"qs\": { \"key\": \"value\" } }", "default": ""}], "supportedNodes": ["n8n-nodes-base.graphql"]}, {"name": "httpQueryAuth", "displayName": "Query <PERSON>", "documentationUrl": "httpRequest", "genericAuth": true, "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "Name", "name": "name", "type": "string", "default": ""}, {"displayName": "Value", "name": "value", "type": "string", "typeOptions": {"password": true}, "default": ""}], "supportedNodes": ["n8n-nodes-base.graphql", "n8n-nodes-base.httpRequest", "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>", "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>"]}, {"name": "httpSslAuth", "displayName": "SSL Certificates", "documentationUrl": "httpRequest", "icon": "node:n8n-nodes-base.httpRequest", "properties": [{"displayName": "CA", "name": "ca", "type": "string", "description": "Certificate Authority certificate", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Certificate", "name": "cert", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Private Key", "name": "key", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Passphrase", "name": "passphrase", "type": "string", "description": "Optional passphrase for the private key, if the private key is encrypted", "typeOptions": {"password": true}, "default": ""}], "supportedNodes": ["n8n-nodes-base.httpRequest"]}, {"name": "hubspotApi", "displayName": "HubSpot API", "documentationUrl": "hubspot", "properties": [{"displayName": "On 30 November, 2022 Hubspot will remove API key support. You will have to connect to HubSpot using private app or Oauth2 auth method. <a target=\"_blank\" href=\"https://developers.hubspot.com/changelog/upcoming-api-key-sunset\">More details (HubSpot.com)</a>", "name": "notice", "type": "notice", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"hapikey": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.hubapi.com", "url": "/account-info/v3/details"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hubspot/hubspot.svg", "supportedNodes": ["n8n-nodes-base.hubspot"]}, {"name": "hubspotAppToken", "displayName": "HubSpot App Token", "documentationUrl": "hubspot", "properties": [{"displayName": "APP Token", "name": "appToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.appToken}}"}}}, "test": {"request": {"baseURL": "https://api.hubapi.com", "url": "/account-info/v3/details"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hubspot/hubspot.svg", "supportedNodes": ["n8n-nodes-base.hubspot"]}, {"name": "hubspotDeveloperApi", "displayName": "HubSpot Developer API", "documentationUrl": "hubspot", "extends": ["oAuth2Api"], "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.hubspot.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.hubapi.com/oauth/v1/token", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "grant_type=authorization_code"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}, {"displayName": "Developer API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "typeOptions": {"password": true}, "default": ""}, {"displayName": "APP ID", "name": "appId", "type": "string", "required": true, "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "crm.objects.contacts.read crm.schemas.contacts.read crm.objects.companies.read crm.schemas.companies.read crm.objects.deals.read crm.schemas.deals.read"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hubspot/hubspot.svg", "supportedNodes": ["n8n-nodes-base.hubspotTrigger"]}, {"name": "hubspotOAuth2Api", "extends": ["oAuth2Api"], "displayName": "HubSpot OAuth2 API", "documentationUrl": "hubspot", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.hubspot.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.hubapi.com/oauth/v1/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "crm.lists.write crm.objects.contacts.read crm.objects.contacts.write crm.objects.companies.read crm.objects.companies.write crm.objects.deals.read crm.objects.deals.write crm.objects.owners.read crm.schemas.companies.read crm.schemas.contacts.read crm.schemas.deals.read forms tickets"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "grant_type=authorization_code"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hubspot/hubspot.svg", "supportedNodes": ["n8n-nodes-base.hubspot"]}, {"name": "humanticAiApi", "displayName": "Humantic AI API", "documentationUrl": "humanticAi", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HumanticAI/humanticai.svg", "supportedNodes": ["n8n-nodes-base.humanticAi"]}, {"name": "hunterApi", "displayName": "Hunter API", "documentationUrl": "hunter", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hunter/hunter.png", "supportedNodes": ["n8n-nodes-base.hunter"]}, {"name": "hybridAnalysisApi", "displayName": "Hybrid Analysis API", "documentationUrl": "hybridanalysis", "httpRequestNode": {"name": "Hybrid Analysis", "docsUrl": "https://www.hybrid-analysis.com/docs/api/v2", "apiBaseUrl": "https://www.hybrid-analysis.com/api/"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"api-key": "={{$credentials.apiKey}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Hybrid.png", "supportedNodes": []}, {"name": "imap", "displayName": "IMAP", "documentationUrl": "imap", "properties": [{"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Host", "name": "host", "type": "string", "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 993}, {"displayName": "SSL/TLS", "name": "secure", "type": "boolean", "default": true}, {"displayName": "Allow Self-Signed Certificates", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "icon": "fa:inbox", "supportedNodes": ["n8n-nodes-base.emailReadImap"]}, {"name": "impervaWafApi", "displayName": "Imperva WAF API", "documentationUrl": "impervawaf", "httpRequestNode": {"name": "Imperva WAF", "docsUrl": "https://docs.imperva.com/bundle/api-docs", "apiBaseUrl": "https://api.imperva.com/"}, "properties": [{"displayName": "API ID", "name": "apiID", "type": "string", "default": "", "required": true}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"x-API-Id": "={{$credentials.apiID}}", "x-API-Key": "={{$credentials.apiKey}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Imperva.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Imperva.dark.svg"}, "supportedNodes": []}, {"name": "intercomApi", "displayName": "Intercom API", "documentationUrl": "intercom", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}", "Accept": "application/json"}}}, "test": {"request": {"baseURL": "https://api.intercom.io", "url": "/me", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Intercom/intercom.svg", "supportedNodes": ["n8n-nodes-base.intercom"]}, {"name": "invoiceNinjaApi", "displayName": "Invoice Ninja API", "documentationUrl": "invoiceNinja", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "hint": "Default URL for v4 is https://app.invoiceninja.com, for v5 it is https://invoicing.co"}, {"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Secret", "name": "secret", "type": "string", "typeOptions": {"password": true}, "default": "", "hint": "This is optional, enter only if you did set a secret in your app and only if you are using v5"}], "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "/api/v1/clients", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/InvoiceNinja/invoiceNinja.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.invoiceNinja", "n8n-nodes-base.invoiceNinjaTrigger"]}, {"name": "iterableApi", "displayName": "Iterable API", "documentationUrl": "iterable", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "EDC", "value": "https://api.eu.iterable.com"}, {"name": "USDC", "value": "https://api.iterable.com"}], "default": "https://api.iterable.com"}], "authenticate": {"type": "generic", "properties": {"headers": {"Api_Key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.region}}", "url": "/api/webhooks", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Iterable/iterable.png", "supportedNodes": ["n8n-nodes-base.iterable"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Jenkins API", "documentationUrl": "jenkins", "properties": [{"displayName": "<PERSON>", "name": "username", "type": "string", "default": ""}, {"displayName": "Personal API Token", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "<PERSON> Instance URL", "name": "baseUrl", "type": "string", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jenkins/jenkins.svg", "supportedNodes": ["n8n-nodes-base.jenkins"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Jina AI API", "documentationUrl": "<PERSON><PERSON><PERSON><PERSON>", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{ $credentials?.api<PERSON>ey }}"}}}, "test": {"request": {"method": "GET", "url": "https://embeddings-dashboard-api.jina.ai/api/v1/api_key/fe_user", "qs": {"api_key": "={{$credentials.apiKey}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/JinaAI/jinaAi.svg", "dark": "icons/n8n-nodes-base/dist/nodes/JinaAI/jinaAi.dark.svg"}, "supportedNodes": ["n8n-nodes-base.jina<PERSON>i"]}, {"name": "jiraSoftwareCloudApi", "displayName": "Jira SW Cloud API", "documentationUrl": "jira", "properties": [{"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://example.atlassian.net"}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.email}}", "password": "={{$credentials.apiToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.domain}}", "url": "/rest/api/2/project"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jira/jira.svg", "supportedNodes": ["n8n-nodes-base.jira", "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>"]}, {"name": "jiraSoftwareServerApi", "displayName": "Jira SW Server API", "documentationUrl": "jira", "properties": [{"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "Password", "name": "password", "typeOptions": {"password": true}, "type": "string", "default": ""}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://example.com"}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.email}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.domain}}", "url": "/rest/api/2/project"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jira/jira.svg", "supportedNodes": ["n8n-nodes-base.jira", "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>"]}, {"name": "jiraSoftwareServerPatApi", "displayName": "Jira SW Server (PAT) API", "documentationUrl": "jira", "properties": [{"displayName": "Personal Access Token", "name": "personalAccessToken", "typeOptions": {"password": true}, "type": "string", "default": ""}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://example.com"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.personalAccessToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.domain}}", "url": "/rest/api/2/project"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jira/jira.svg", "supportedNodes": ["n8n-nodes-base.jira", "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>"]}, {"name": "jotFormApi", "displayName": "JotForm API", "documentationUrl": "jotForm", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "API Domain", "name": "apiDomain", "type": "options", "options": [{"name": "api.jotform.com", "value": "api.jotform.com"}, {"name": "eu-api.jotform.com", "value": "eu-api.jotform.com"}, {"name": "hipaa-api.jotform.com", "value": "hipaa-api.jotform.com"}], "default": "api.jotform.com", "description": "The API domain to use. Use \"eu-api.jotform.com\" if your account is in based in Europe."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/JotForm/jotform.png", "supportedNodes": ["n8n-nodes-base.jotFormTrigger"]}, {"name": "jwtAuth", "displayName": "<PERSON><PERSON><PERSON>", "documentationUrl": "jwt", "properties": [{"displayName": "Key Type", "name": "keyType", "type": "options", "description": "Choose either the secret passphrase or PEM encoded public keys", "options": [{"name": "Passphrase", "value": "passphrase"}, {"name": "PEM Key", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "default": "passphrase"}, {"displayName": "Secret", "name": "secret", "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"keyType": ["passphrase"]}}}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"keyType": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "default": ""}, {"displayName": "Public Key", "name": "public<PERSON>ey", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"keyType": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "default": ""}, {"displayName": "Algorithm", "name": "algorithm", "type": "options", "default": "HS256", "options": [{"name": "HS256", "value": "HS256"}, {"name": "HS384", "value": "HS384"}, {"name": "HS512", "value": "HS512"}, {"name": "RS256", "value": "RS256"}, {"name": "RS384", "value": "RS384"}, {"name": "RS512", "value": "RS512"}, {"name": "ES256", "value": "ES256"}, {"name": "ES384", "value": "ES384"}, {"name": "ES512", "value": "ES512"}, {"name": "PS256", "value": "PS256"}, {"name": "PS384", "value": "PS384"}, {"name": "PS512", "value": "PS512"}, {"name": "none", "value": "none"}]}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/jwt.svg", "supportedNodes": ["n8n-nodes-base.jwt", "n8n-nodes-base.respondToWebhook", "n8n-nodes-base.wait", "n8n-nodes-base.webhook"]}, {"name": "kafka", "displayName": "Kafka", "documentationUrl": "kafka", "properties": [{"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "placeholder": "my-app", "hint": "Will not affect the connection, but will be used to identify the client in the Kafka server logs. Read more <a href=\"https://kafka.apache.org/documentation/#design_quotasgroups\">here</a>"}, {"displayName": "Brokers", "name": "brokers", "type": "string", "default": "", "placeholder": "kafka1:9092,kafka2:9092"}, {"displayName": "SSL", "name": "ssl", "type": "boolean", "default": true}, {"displayName": "Authentication", "name": "authentication", "type": "boolean", "default": false}, {"displayName": "Username", "name": "username", "type": "string", "displayOptions": {"show": {"authentication": [true]}}, "default": "", "description": "Optional username if authenticated is required"}, {"displayName": "Password", "name": "password", "type": "string", "displayOptions": {"show": {"authentication": [true]}}, "typeOptions": {"password": true}, "default": "", "description": "Optional password if authenticated is required"}, {"displayName": "SASL Mechanism", "name": "saslMechanism", "type": "options", "displayOptions": {"show": {"authentication": [true]}}, "options": [{"name": "Plain", "value": "plain"}, {"name": "scram-sha-256", "value": "scram-sha-256"}, {"name": "scram-sha-512", "value": "scram-sha-512"}], "default": "plain"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Kafka/kafka.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Kafka/kafka.dark.svg"}, "supportedNodes": ["n8n-nodes-base.kafka", "n8n-nodes-base.kafkaTrigger"]}, {"name": "keapOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Keap OAuth2 API", "documentationUrl": "keap", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://signin.infusionsoft.com/app/oauth/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.infusionsoft.com/token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "full"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Keap/keap.png", "supportedNodes": ["n8n-nodes-base.keap", "n8n-nodes-base.keapTrigger"]}, {"name": "kibanaApi", "displayName": "Kibana API", "documentationUrl": "kibana", "httpRequestNode": {"name": "Kibana", "docsUrl": "https://www.elastic.co/guide/en/kibana/current/api.html", "apiBaseUrl": ""}, "properties": [{"displayName": "URL", "name": "url", "type": "string", "required": true, "default": "", "placeholder": "http://localhost:5601"}, {"displayName": "Username", "name": "username", "type": "string", "required": true, "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"kbn-xsrf": true}, "auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/api/features"}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Kibana.svg", "supportedNodes": []}, {"name": "kitemakerApi", "displayName": "Kitemaker API", "documentationUrl": "kitemaker", "properties": [{"displayName": "Personal Access Token", "name": "personalAccessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Kitemaker/kitemaker.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Kitemaker/kitemaker.dark.svg"}, "supportedNodes": ["n8n-nodes-base.kitemaker"]}, {"name": "koBoToolboxApi", "displayName": "KoBoToolbox API Token", "documentationUrl": "koBoToolbox", "properties": [{"displayName": "API Root URL", "name": "URL", "type": "string", "default": "https://kf.kobotoolbox.org/"}, {"displayName": "API Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": "", "hint": "You can get your API token at https://[api-root]/token/?format=json (for a logged in user)"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Token {{$credentials.token}}"}}}, "test": {"request": {"baseURL": "={{$credentials.URL}}", "url": "/api/v2/assets/", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/KoBoToolbox/koBoToolbox.svg", "supportedNodes": ["n8n-nodes-base.koBoToolbox", "n8n-nodes-base.koBoToolboxTrigger"]}, {"name": "ldap", "displayName": "LDAP", "documentationUrl": "ldap", "properties": [{"displayName": "LDAP Server Address", "name": "hostname", "type": "string", "default": "", "required": true, "description": "IP or domain of the LDAP server"}, {"displayName": "LDAP Server Port", "name": "port", "type": "string", "default": "389", "description": "Port used to connect to the LDAP server"}, {"displayName": "Binding DN", "name": "bindDN", "type": "string", "default": "", "description": "Distinguished Name of the user to connect as"}, {"displayName": "Binding Password", "name": "bindPassword", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Password of the user provided in the Binding DN field above"}, {"displayName": "Connection Security", "name": "connectionSecurity", "type": "options", "default": "none", "options": [{"name": "None", "value": "none"}, {"name": "TLS", "value": "tls"}, {"name": "STARTTLS", "value": "startTls"}]}, {"displayName": "Ignore SSL/TLS Issues", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL/TLS certificate validation is not possible", "default": false, "displayOptions": {"hide": {"connectionSecurity": ["none"]}}}, {"displayName": "CA Certificate", "name": "caCertificate", "typeOptions": {"alwaysOpenEditWindow": true}, "displayOptions": {"hide": {"connectionSecurity": ["none"]}}, "type": "string", "default": ""}, {"displayName": "Timeout", "description": "Optional connection timeout in seconds", "name": "timeout", "type": "number", "default": 300}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Ldap/ldap.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Ldap/ldap.dark.svg"}, "supportedNodes": ["n8n-nodes-base.ldap"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Lemlist API", "documentationUrl": "lemlist", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://api.lemlist.com/api", "url": "/campaigns"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Lemlist/lemlist.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.lemlist", "n8n-nodes-base.lemlistTrigger"]}, {"name": "linearApi", "displayName": "Linear API", "documentationUrl": "linear", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "={{$credentials.apiKey}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Linear/linear.svg", "supportedNodes": ["n8n-nodes-base.linear", "n8n-nodes-base.linearTrigger"]}, {"name": "linearOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Linear OAuth2 API", "documentationUrl": "linear", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://linear.app/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.linear.app/oauth/token", "required": true}, {"displayName": "Actor", "name": "actor", "type": "options", "options": [{"name": "User", "value": "user", "description": "Resources are created as the user who authorized the application"}, {"name": "Application", "value": "application", "description": "Resources are created as the application"}], "default": "user"}, {"displayName": "Include <PERSON><PERSON>", "name": "includeAdminScope", "type": "boolean", "default": false, "description": "Grants the \"Admin\" scope, Needed to create webhooks"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "={{$self[\"includeAdminScope\"] ? \"read write issues:create comments:create admin\" : \"read write issues:create comments:create\"}}", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "={{\"actor=\"+$self[\"actor\"]}}"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Linear/linear.svg", "supportedNodes": ["n8n-nodes-base.linear", "n8n-nodes-base.linearTrigger"]}, {"name": "lineNotifyOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Line Notify OAuth2 API", "documentationUrl": "line", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://notify-bot.line.me/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://notify-bot.line.me/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "notify", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Line/line.png", "supportedNodes": ["n8n-nodes-base.line"]}, {"name": "lingvaNexApi", "displayName": "LingvaNex API", "documentationUrl": "lingvaNex", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/LingvaNex/lingvanex.png", "supportedNodes": ["n8n-nodes-base.lingvaNex"]}, {"name": "linkedInCommunityManagementOAuth2Api", "extends": ["oAuth2Api"], "displayName": "LinkedIn Community Management OAuth2 API", "documentationUrl": "linkedIn", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.linkedin.com/oauth/v2/authorization", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://www.linkedin.com/oauth/v2/accessToken", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "w_member_social w_organization_social r_basicprofile"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/LinkedIn/linkedin.svg", "supportedNodes": ["n8n-nodes-base.linkedIn"]}, {"name": "linkedInOAuth2Api", "extends": ["oAuth2Api"], "displayName": "LinkedIn OAuth2 API", "documentationUrl": "linkedIn", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Organization Support", "name": "organizationSupport", "type": "boolean", "default": true, "description": "Whether to request permissions to post as an organization"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.linkedin.com/oauth/v2/authorization", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://www.linkedin.com/oauth/v2/accessToken", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "=w_member_social{{$self[\"organizationSupport\"] === true ? \",w_organization_social\": $self[\"legacy\"] === true ? \",r_liteprofile,r_emailaddress\" : \",profile,email,openid\"}}", "description": "Standard scopes for posting on behalf of a user or organization. See <a href=\"https://docs.microsoft.com/en-us/linkedin/marketing/getting-started#available-permissions\"> this resource </a>."}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}, {"displayName": "Legacy", "name": "legacy", "type": "boolean", "default": true, "description": "Whether to use the legacy API"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/LinkedIn/linkedin.svg", "supportedNodes": ["n8n-nodes-base.linkedIn"]}, {"name": "loneScaleApi", "displayName": "LoneScale API", "documentationUrl": "lonescale", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://public-api.lonescale.com", "url": "/users"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/LoneScale/loneScale.svg", "dark": "icons/n8n-nodes-base/dist/nodes/LoneScale/loneScale.dark.svg"}, "supportedNodes": ["n8n-nodes-base.loneScaleTrigger", "n8n-nodes-base.loneScale"]}, {"name": "magento2Api", "displayName": "Magento 2 API", "documentationUrl": "magento2", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": ""}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "={{$credentials.host}}", "url": "/rest/default/V1/modules"}}, "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Magento/magento.svg", "supportedNodes": ["n8n-nodes-base.magento2"]}, {"name": "mailcheckApi", "displayName": "Mailcheck API", "documentationUrl": "mailcheck", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailcheck/mailcheck.svg", "supportedNodes": ["n8n-nodes-base.mailcheck"]}, {"name": "mailchimpApi", "displayName": "Mailchimp API", "documentationUrl": "mailchimp", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=apikey {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "=https://{{$credentials.apiKey.split(\"-\").pop()}}.api.mailchimp.com/3.0", "url": "/lists"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Mailchimp/mailchimp.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Mailchimp/mailchimp.dark.svg"}, "supportedNodes": ["n8n-nodes-base.mailchimp", "n8n-nodes-base.mailchimpTrigger"]}, {"name": "mailchimpOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Mailchimp OAuth2 API", "documentationUrl": "mailchimp", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://login.mailchimp.com/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://login.mailchimp.com/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON><PERSON>", "name": "metadataUrl", "type": "hidden", "default": "https://login.mailchimp.com/oauth2/metadata", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Mailchimp/mailchimp.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Mailchimp/mailchimp.dark.svg"}, "supportedNodes": ["n8n-nodes-base.mailchimp", "n8n-nodes-base.mailchimpTrigger"]}, {"name": "mailerLiteApi", "displayName": "Mailer Lite API", "documentationUrl": "mailerLite", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Classic API", "name": "classicApi", "type": "boolean", "default": true, "description": "If the Classic API should be used, If this is your first time using this node this should be false."}], "test": {"request": {"baseURL": "={{$credentials.classicApi ? \"https://api.mailerlite.com/api/v2\" : \"https://connect.mailerlite.com/api\"}}", "url": "/groups"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/MailerLite/MailerLite.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.mailerLite", "n8n-nodes-base.mailerLiteTrigger"]}, {"name": "mailgunApi", "displayName": "Mailgun API", "documentationUrl": "mailgun", "properties": [{"displayName": "API Domain", "name": "apiDomain", "type": "options", "options": [{"name": "api.eu.mailgun.net", "value": "api.eu.mailgun.net"}, {"name": "api.mailgun.net", "value": "api.mailgun.net"}], "default": "api.mailgun.net", "description": "The configured mailgun API domain"}, {"displayName": "Email Domain", "name": "emailDomain", "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "api", "password": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "=https://{{$credentials.apiDomain}}/v3", "url": "/domains"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailgun/mailgun.svg", "supportedNodes": ["n8n-nodes-base.mailgun"]}, {"name": "mailjetEmailApi", "displayName": "Mailjet Email API", "documentationUrl": "mailjet", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Secret Key", "name": "secret<PERSON>ey", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Sandbox Mode", "name": "sandboxMode", "type": "boolean", "default": false, "description": "Whether to allow to run the API call in a Sandbox mode, where all validations of the payload will be done without delivering the message"}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.apiKey}}", "password": "={{$credentials.secretKey}}"}}}, "test": {"request": {"baseURL": "https://api.mailjet.com", "url": "/v3/REST/template", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailjet/mailjet.svg", "supportedNodes": ["n8n-nodes-base.mailjet", "n8n-nodes-base.mailjetTrigger"]}, {"name": "mailjetSmsApi", "displayName": "Mailjet SMS API", "documentationUrl": "mailjet", "properties": [{"displayName": "Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.token}}"}}}, "test": {"request": {"baseURL": "https://api.mailjet.com", "url": "/v4/sms", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailjet/mailjet.svg", "supportedNodes": ["n8n-nodes-base.mailjet"]}, {"name": "malcoreApi", "displayName": "MalcoreAPI", "documentationUrl": "malcore", "httpRequestNode": {"name": "Malcore", "docsUrl": "https://malcore.readme.io/reference/upload", "apiBaseUrlPlaceholder": "https://api.malcore.io/api/urlcheck"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"apiKey": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.malcore.io/api", "url": "/urlcheck", "method": "POST", "body": {"url": "google.com"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Malcore.png", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Malcore.png"}, "supportedNodes": []}, {"name": "mandr<PERSON><PERSON><PERSON>", "displayName": "Mandrill API", "documentationUrl": "mandrill", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mandrill/mandrill.svg", "supportedNodes": ["n8n-nodes-base.mandrill"]}, {"name": "marketstackApi", "displayName": "Marketstack API", "documentationUrl": "marketstack", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Use HTTPS", "name": "useHttps", "type": "boolean", "default": false, "description": "Whether to use HTTPS (paid plans only)"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Marketstack/marketstack.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Marketstack/marketstack.dark.svg"}, "supportedNodes": ["n8n-nodes-base.marketstack"]}, {"name": "matrixApi", "displayName": "Matrix API", "documentationUrl": "matrix", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Homeserver URL", "name": "homeserverUrl", "type": "string", "default": "https://matrix-client.matrix.org"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Matrix/matrix.png", "supportedNodes": ["n8n-nodes-base.matrix"]}, {"name": "mattermostApi", "displayName": "Mattermost API", "documentationUrl": "mattermost", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": ""}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials.baseUrl.replace(/\\/$/, \"\")}}/api/v4", "url": "/users", "skipSslCertificateValidation": "={{$credentials?.allowUnauthorizedCerts}}"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mattermost/mattermost.svg", "supportedNodes": ["n8n-nodes-base.mattermost"]}, {"name": "mauticApi", "displayName": "Mautic API", "documentationUrl": "mautic", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://name.mautic.net"}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url.replace(new RegExp(\"/$\"), \"\")}}", "url": "/api/users/self"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mautic/mautic.svg", "supportedNodes": ["n8n-nodes-base.mautic", "n8n-nodes-base.mauticTrigger"]}, {"name": "mauticOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Mautic OAuth2 API", "documentationUrl": "mautic", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://name.mautic.net"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "={{$self[\"url\"].endsWith(\"/\") ? $self[\"url\"].slice(0, -1) : $self[\"url\"]}}/oauth/v2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "={{$self[\"url\"].endsWith(\"/\") ? $self[\"url\"].slice(0, -1) : $self[\"url\"]}}/oauth/v2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mautic/mautic.svg", "supportedNodes": ["n8n-nodes-base.mautic", "n8n-nodes-base.mauticTrigger"]}, {"name": "mediumApi", "displayName": "Medium API", "documentationUrl": "medium", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Medium/medium.png", "supportedNodes": ["n8n-nodes-base.medium"]}, {"name": "mediumOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Medium OAuth2 API", "documentationUrl": "medium", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://medium.com/m/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://medium.com/v1/tokens", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "basicProfile,publishPost,listPublications"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Medium/medium.png", "supportedNodes": ["n8n-nodes-base.medium"]}, {"name": "metabaseApi", "displayName": "Metabase API", "documentationUrl": "metabase", "properties": [{"displayName": "Session Token", "name": "sessionToken", "type": "hidden", "typeOptions": {"expirable": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": ""}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Metabase-Session": "={{$credentials.sessionToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "/api/user/current"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Metabase/metabase.svg", "supportedNodes": ["n8n-nodes-base.metabase"]}, {"name": "messageBirdApi", "displayName": "MessageBird API", "documentationUrl": "messageBird", "properties": [{"displayName": "API Key", "name": "accessKey", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MessageBird/messagebird.svg", "supportedNodes": ["n8n-nodes-base.messageBird"]}, {"name": "microsoftAzureCosmosDbSharedKeyApi", "displayName": "Microsoft Azure Cosmos DB API", "documentationUrl": "azurecosmosdb", "properties": [{"displayName": "Account", "name": "account", "default": "", "description": "Account name", "required": true, "type": "string"}, {"displayName": "Key", "name": "key", "default": "", "description": "Account key", "required": true, "type": "string", "typeOptions": {"password": true}}, {"displayName": "Database", "name": "database", "default": "", "description": "Database name", "required": true, "type": "string"}], "test": {"request": {"baseURL": "=https://{{ $credentials.account }}.documents.azure.com/dbs/{{ $credentials.database }}", "url": "/colls"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/AzureCosmosDb/AzureCosmosDb.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/AzureCosmosDb/AzureCosmosDb.svg"}, "authenticate": {}, "supportedNodes": ["n8n-nodes-base.azureCosmosDb"]}, {"name": "microsoftAzureMonitorOAuth2Api", "displayName": "Microsoft Azure Monitor OAuth2 API", "extends": ["oAuth2Api"], "documentationUrl": "microsoftazuremonitor", "httpRequestNode": {"name": "Microsoft Azure Monitor", "docsUrl": "https://learn.microsoft.com/en-us/azure/azure-monitor/logs/api/request-format", "apiBaseUrlPlaceholder": "https://api.loganalytics.azure.com/v1/workspaces/[workspace_id]/query"}, "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "options", "options": [{"name": "Authorization Code", "value": "authorizationCode"}, {"name": "Client Credentials", "value": "clientCredentials"}], "default": "authorizationCode"}, {"displayName": "Tenant ID", "required": true, "name": "tenantId", "type": "string", "default": ""}, {"displayName": "Resource", "name": "resource", "type": "options", "options": [{"name": "Azure Log Analytics", "value": "https://api.loganalytics.azure.com"}, {"name": "Log Analytics", "value": "https://api.loganalytics.io"}, {"name": "Azure Monitor", "value": "https://monitor.azure.com"}, {"name": "Azure Management", "value": "https://management.azure.com"}], "default": "https://api.loganalytics.azure.com"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "=https://login.microsoftonline.com/{{$self[\"tenantId\"]}}/oauth2/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "=https://login.microsoftonline.com/{{$self[\"tenantId\"]}}/oauth2/{{$self[\"grantType\"] === \"clientCredentials\" ? \"v2.0/\" : \"\"}}token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "={{$self[\"grantType\"] === \"clientCredentials\" ? \"\" : \"resource=\" + $self[\"resource\"]}}"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "={{$self[\"grantType\"] === \"clientCredentials\" ? $self[\"resource\"] + \"/.default\" : \"\"}}"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Microsoft.svg", "supportedNodes": []}, {"name": "microsoftDynamicsOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft Dynamics OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "Subdomain", "name": "subdomain", "type": "string", "required": true, "placeholder": "organization", "default": ""}, {"displayName": "Region", "name": "region", "type": "options", "default": "crm.dynamics.com", "options": [{"name": "Asia Pacific (APAC/ APJ)", "value": "crm5.dynamics.com"}, {"name": "Australia (OCE)", "value": "crm6.dynamics.com"}, {"name": "Canada (CAN)", "value": "crm3.dynamics.com"}, {"name": "China (CHN)", "value": "crm.dynamics.cn"}, {"name": "Europe, Middle East, Africa (EMEA/ EUR)", "value": "crm4.dynamics.com"}, {"name": "France (FRA)", "value": "crm12.dynamics.com"}, {"name": "Germany (GER)", "value": "crm16.dynamics.com"}, {"name": "India (IND)", "value": "crm8.dynamics.com"}, {"name": "Japan (JPN)", "value": "crm7.dynamics.com"}, {"name": "Microsoft Cloud Germany (DEU)", "value": "crm.microsoftdynamics.de"}, {"name": "North America (NAM)", "value": "crm.dynamics.com"}, {"name": "North America 2 (US Gov GCC)", "value": "crm9.dynamics.com"}, {"name": "South Africa (ZAF)", "value": "crm14.dynamics.com"}, {"name": "South America (LATAM/ SAM)", "value": "crm2.dynamics.com"}, {"name": "Switzerland (CHE)", "value": "crm17.dynamics.com"}, {"name": "United Arab Emirates (UAE)", "value": "crm15.dynamics.com"}, {"name": "United Kingdom (UK/ GBR)", "value": "crm11.dynamics.com"}, {"name": "United States Government Community Cloud (GCC High)", "value": "crm.microsoftdynamics.us"}]}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "=openid offline_access https://{{$self.subdomain}}.{{$self.region}}/.default"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Dynamics/microsoftDynamicsCrm.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Dynamics/microsoftDynamicsCrm.dark.svg"}, "supportedNodes": ["n8n-nodes-base.microsoftDynamicsCrm"]}, {"name": "microsoftEntraOAuth2Api", "displayName": "Microsoft Entra ID (Azure Active Directory) API", "extends": ["microsoftOAuth2Api"], "documentationUrl": "microsoftentra", "properties": [{"displayName": "Custom Scopes", "name": "customScopes", "type": "boolean", "default": false, "description": "Define custom scopes"}, {"displayName": "The default scopes needed for the node to work are already set, If you change these the node may not function correctly.", "name": "customScopesNotice", "type": "notice", "default": "", "displayOptions": {"show": {"customScopes": [true]}}}, {"displayName": "Enabled Scopes", "name": "enabledScopes", "type": "string", "displayOptions": {"show": {"customScopes": [true]}}, "default": "openid offline_access AccessReview.ReadWrite.All Directory.ReadWrite.All NetworkAccessPolicy.ReadWrite.All DelegatedAdminRelationship.ReadWrite.All EntitlementManagement.ReadWrite.All User.ReadWrite.All Directory.AccessAsUser.All Sites.FullControl.All GroupMember.ReadWrite.All", "description": "Scopes that should be enabled"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "={{$self[\"customScopes\"] ? $self[\"enabledScopes\"] : \"openid offline_access AccessReview.ReadWrite.All Directory.ReadWrite.All NetworkAccessPolicy.ReadWrite.All DelegatedAdminRelationship.ReadWrite.All EntitlementManagement.ReadWrite.All User.ReadWrite.All Directory.AccessAsUser.All Sites.FullControl.All GroupMember.ReadWrite.All\"}}"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Entra/microsoftEntra.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Entra/microsoftEntra.dark.svg"}, "supportedNodes": ["n8n-nodes-base.microsoftEntra"]}, {"name": "microsoftExcelOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft Excel OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "openid offline_access Files.ReadWrite"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Excel/excel.svg", "supportedNodes": ["n8n-nodes-base.microsoftExcel"]}, {"name": "microsoftGraphSecurityOAuth2Api", "displayName": "Microsoft Graph Security OAuth2 API", "extends": ["microsoftOAuth2Api"], "documentationUrl": "microsoft", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "SecurityEvents.ReadWrite.All"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/GraphSecurity/microsoftGraph.svg", "supportedNodes": ["n8n-nodes-base.microsoftGraphSecurity"]}, {"name": "microsoftOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Microsoft OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "string", "default": "https://login.microsoftonline.com/common/oauth2/v2.0/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "string", "default": "https://login.microsoftonline.com/common/oauth2/v2.0/token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "response_mode=query"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Microsoft.svg", "supportedNodes": []}, {"name": "microsoftOneDriveOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft Drive OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "openid offline_access Files.ReadWrite.All"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/OneDrive/oneDrive.svg", "supportedNodes": ["n8n-nodes-base.microsoftOneDrive", "n8n-nodes-base.microsoftOneDriveTrigger"]}, {"name": "microsoftOutlookOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft Outlook OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "openid offline_access Contacts.Read Contacts.ReadWrite Calendars.Read Calendars.Read.Shared Calendars.ReadWrite Mail.ReadWrite Mail.ReadWrite.Shared Mail.Send Mail.Send.Shared MailboxSettings.Read"}, {"displayName": "Use Shared Mailbox", "name": "useShared", "type": "boolean", "default": false}, {"displayName": "User Principal Name", "name": "userPrincipalName", "description": "Target user's UPN or ID", "type": "string", "default": "", "displayOptions": {"show": {"useShared": [true]}}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Outlook/outlook.svg", "supportedNodes": ["n8n-nodes-base.microsoftOutlook", "n8n-nodes-base.microsoftOutlookTrigger"]}, {"name": "microsoftSharePointOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft SharePoint OAuth2 API", "documentationUrl": "microsoft", "httpRequestNode": {"name": "Microsoft SharePoint", "docsUrl": "https://learn.microsoft.com/en-us/sharepoint/dev/apis/sharepoint-rest-graph", "apiBaseUrlPlaceholder": "https://{subdomain}.sharepoint.com/_api/v2.0/"}, "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "=openid offline_access https://{{$self.subdomain}}.sharepoint.com/.default"}, {"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": "", "hint": "You can extract the subdomain from the URL. For example, in the URL \"https://tenant123.sharepoint.com\", the subdomain is \"tenant123\"."}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/SharePoint.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/SharePoint.svg"}, "supportedNodes": []}, {"name": "microsoftSql", "displayName": "Microsoft SQL", "documentationUrl": "microsoftSql", "properties": [{"displayName": "Server", "name": "server", "type": "string", "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "master"}, {"displayName": "User", "name": "user", "type": "string", "default": "sa"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 1433}, {"displayName": "Domain", "name": "domain", "type": "string", "default": ""}, {"displayName": "TLS", "name": "tls", "type": "boolean", "default": true}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "default": false, "description": "Whether to connect even if SSL certificate validation is not possible"}, {"displayName": "Connect Timeout", "name": "connectTimeout", "type": "number", "default": 15000, "description": "Connection timeout in ms"}, {"displayName": "Request Timeout", "name": "requestTimeout", "type": "number", "default": 15000, "description": "Request timeout in ms"}, {"displayName": "TDS Version", "name": "tdsVersion", "type": "options", "options": [{"name": "7_4 (SQL Server 2012 ~ 2019)", "value": "7_4"}, {"name": "7_3_B (SQL Server 2008R2)", "value": "7_3_B"}, {"name": "7_3_A (SQL Server 2008)", "value": "7_3_A"}, {"name": "7_2 (SQL Server 2005)", "value": "7_2"}, {"name": "7_1 (SQL Server 2000)", "value": "7_1"}], "default": "7_4", "description": "The version of TDS to use. If server doesn't support specified version, negotiated version is used instead."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Sql/mssql.svg", "supportedNodes": ["n8n-nodes-base.microsoftSql"]}, {"name": "microsoftTeamsOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft Teams OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "openid offline_access User.ReadWrite.All Group.ReadWrite.All Chat.ReadWrite"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Teams/teams.svg", "supportedNodes": ["n8n-nodes-base.microsoftTeams"]}, {"name": "microsoftToDoOAuth2Api", "extends": ["microsoftOAuth2Api"], "displayName": "Microsoft To Do OAuth2 API", "documentationUrl": "microsoft", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "openid offline_access Tasks.ReadWrite"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/ToDo/todo.svg", "supportedNodes": ["n8n-nodes-base.microsoftToDo"]}, {"name": "mindeeInvoiceApi", "displayName": "Mindee Invoice API", "documentationUrl": "mindee", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mindee/mindee.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.mindee"]}, {"name": "mindeeReceipt<PERSON>pi", "displayName": "Mindee Receipt API", "documentationUrl": "mindee", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mindee/mindee.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.mindee"]}, {"name": "miroOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Miro OAuth2 API", "documentationUrl": "miro", "httpRequestNode": {"name": "Mir<PERSON>", "docsUrl": "https://developers.miro.com/reference/overview", "apiBaseUrl": "https://api.miro.com/v2/"}, "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://miro.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.miro.com/v1/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Miro.svg", "supportedNodes": []}, {"name": "mispApi", "displayName": "MISP API", "documentationUrl": "misp", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": ""}, {"displayName": "Allow Unauthorized Certificates", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.baseUrl.replace(new RegExp(\"/$\"), \"\")}}", "url": "/tags", "skipSslCertificateValidation": "={{$credentials.allowUnauthorizedCerts}}"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Misp/misp.svg", "supportedNodes": ["n8n-nodes-base.misp"]}, {"name": "<PERSON><PERSON><PERSON>", "displayName": "Mist API", "documentationUrl": "mist", "httpRequestNode": {"name": "Mist", "docsUrl": "https://www.mist.com/documentation/mist-api-introduction/", "apiBaseUrl": ""}, "properties": [{"displayName": "API Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "Europe", "value": "eu"}, {"name": "Global", "value": "global"}], "default": "eu"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Token {{$credentials.token}}"}}}, "test": {"request": {"baseURL": "=https://api{{$credentials.region === \"eu\" ? \".eu\" : \"\"}}.mist.com", "url": "/api/v1/self", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Mist.svg", "supportedNodes": []}, {"name": "mocean<PERSON>pi", "displayName": "Mocean Api", "documentationUrl": "mocean", "properties": [{"displayName": "API Key", "name": "mocean-api-key", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "API Secret", "name": "mocean-api-secret", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Mocean/mocean.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Mocean/mocean.dark.svg"}, "supportedNodes": ["n8n-nodes-base.mocean"]}, {"name": "mondayComApi", "displayName": "Monday.com API", "documentationUrl": "mondayCom", "properties": [{"displayName": "Token V2", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiToken}}"}}}, "test": {"request": {"headers": {"API-Version": "2023-10", "Content-Type": "application/json"}, "baseURL": "https://api.monday.com/v2", "method": "POST", "body": "{\"query\":\"query { me { name }}\"}"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/MondayCom/mondayCom.svg", "supportedNodes": ["n8n-nodes-base.mondayCom"]}, {"name": "mondayComOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Monday.com OAuth2 API", "documentationUrl": "mondaycom", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://auth.monday.com/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://auth.monday.com/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "boards:write boards:read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MondayCom/mondayCom.svg", "supportedNodes": ["n8n-nodes-base.mondayCom"]}, {"name": "mongoDb", "displayName": "MongoDB", "documentationUrl": "mongoDb", "properties": [{"displayName": "Configuration Type", "name": "configurationType", "type": "options", "options": [{"name": "Connection String", "value": "connectionString", "description": "Provide connection data via string"}, {"name": "Values", "value": "values", "description": "Provide connection data via values"}], "default": "values"}, {"displayName": "Connection String", "name": "connectionString", "type": "string", "displayOptions": {"show": {"configurationType": ["connectionString"]}}, "default": "", "placeholder": "mongodb://<USERNAME>:<PASSWORD>@localhost:27017/?authSource=admin&readPreference=primary&appname=n8n&ssl=false", "description": "If provided, the value here will be used as a MongoDB connection string, and the MongoDB credentials will be ignored"}, {"displayName": "Host", "name": "host", "type": "string", "displayOptions": {"show": {"configurationType": ["values"]}}, "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "", "description": "Note: the database should still be provided even if using an override connection string"}, {"displayName": "User", "name": "user", "type": "string", "displayOptions": {"show": {"configurationType": ["values"]}}, "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"configurationType": ["values"]}}, "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "displayOptions": {"show": {"configurationType": ["values"]}}, "default": 27017}, {"displayName": "Use TLS", "name": "tls", "type": "boolean", "default": false}, {"displayName": "CA Certificate", "name": "ca", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"tls": [true]}}, "default": ""}, {"displayName": "Public Client Certificate", "name": "cert", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"tls": [true]}}, "default": ""}, {"displayName": "Private Client Key", "name": "key", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"tls": [true]}}, "default": ""}, {"displayName": "Passphrase", "name": "passphrase", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"tls": [true]}}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MongoDb/mongodb.svg", "supportedNodes": ["n8n-nodes-base.mongoDb"]}, {"name": "monicaCrmApi", "displayName": "Monica CRM API", "documentationUrl": "monicaCrm", "properties": [{"displayName": "Environment", "name": "environment", "type": "options", "default": "cloudHosted", "options": [{"name": "Cloud-Hosted", "value": "cloudHosted"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Self-Hosted Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://www.mydomain.com", "displayOptions": {"show": {"environment": ["selfHosted"]}}}, {"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MonicaCrm/monicaCrm.png", "supportedNodes": ["n8n-nodes-base.monicaCrm"]}, {"name": "mqtt", "displayName": "MQTT", "documentationUrl": "mqtt", "properties": [{"displayName": "Protocol", "name": "protocol", "type": "options", "options": [{"name": "Mqtt", "value": "mqtt"}, {"name": "Mqtts", "value": "mqtts"}, {"name": "Ws", "value": "ws"}], "default": "mqtt"}, {"displayName": "Host", "name": "host", "type": "string", "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 1883}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Clean Session", "name": "clean", "type": "boolean", "default": true, "description": "Whether to use clean session - set to false to receive QoS 1 and 2 messages while offline"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "description": "Client ID. If left empty, one is autogenerated for you."}, {"displayName": "SSL", "name": "ssl", "type": "boolean", "default": false}, {"displayName": "Passwordless", "name": "passwordless", "type": "boolean", "displayOptions": {"show": {"ssl": [true]}}, "default": true, "description": "Whether to use passwordless connection with certificates (SASL mechanism EXTERNAL)"}, {"displayName": "CA Certificates", "name": "ca", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true]}}, "default": "", "description": "SSL CA Certificates to use"}, {"displayName": "Reject Unauthorized Certificate", "name": "rejectUnauthorized", "type": "boolean", "displayOptions": {"show": {"ssl": [true], "passwordless": [true]}}, "default": false, "description": "Whether to validate Certificate"}, {"displayName": "Client Certificate", "name": "cert", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true], "passwordless": [true]}}, "default": "", "description": "SSL Client Certificate to use"}, {"displayName": "Client Key", "name": "key", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true], "passwordless": [true]}}, "default": "", "description": "SSL Client Key to use"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MQTT/mqtt.svg", "supportedNodes": ["n8n-nodes-base.mqtt", "n8n-nodes-base.mqttTrigger"]}, {"name": "msg91Api", "displayName": "Msg91 Api", "documentationUrl": "msg91", "properties": [{"displayName": "Authentication Key", "name": "auth<PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Msg91/msg91.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Msg91/msg91.dark.svg"}, "supportedNodes": ["n8n-nodes-base.msg91"]}, {"name": "mySql", "displayName": "MySQL", "documentationUrl": "mySql", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "mysql"}, {"displayName": "User", "name": "user", "type": "string", "default": "mysql"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 3306}, {"displayName": "Connect Timeout", "name": "connectTimeout", "type": "number", "default": 10000, "description": "The milliseconds before a timeout occurs during the initial connection to the MySQL server"}, {"displayName": "SSL", "name": "ssl", "type": "boolean", "default": false}, {"displayName": "CA Certificate", "name": "caCertificate", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true]}}, "type": "string", "default": ""}, {"displayName": "Client Private Key", "name": "clientPrivateKey", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true]}}, "type": "string", "default": ""}, {"displayName": "Client Certificate", "name": "clientCertificate", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true]}}, "type": "string", "default": ""}, {"displayName": "SSH Tunnel", "name": "sshTunnel", "type": "boolean", "default": false}, {"displayName": "SSH Authenticate with", "name": "sshAuthenticateWith", "type": "options", "default": "password", "options": [{"name": "Password", "value": "password"}, {"name": "Private Key", "value": "privateKey"}], "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH Host", "name": "sshHost", "type": "string", "default": "localhost", "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH Port", "name": "sshPort", "type": "number", "default": 22, "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH User", "name": "sshUser", "type": "string", "default": "root", "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH Password", "name": "sshPassword", "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"sshTunnel": [true], "sshAuthenticateWith": ["password"]}}}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "typeOptions": {"rows": 4, "password": true}, "default": "", "displayOptions": {"show": {"sshTunnel": [true], "sshAuthenticateWith": ["privateKey"]}}}, {"displayName": "Passphrase", "name": "passphrase", "type": "string", "default": "", "description": "Passphrase used to create the key, if no passphrase was used leave empty", "displayOptions": {"show": {"sshTunnel": [true], "sshAuthenticateWith": ["privateKey"]}}}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/MySql/mysql.svg", "dark": "icons/n8n-nodes-base/dist/nodes/MySql/mysql.dark.svg"}, "supportedNodes": ["n8n-nodes-base.mySql"]}, {"name": "n8nApi", "displayName": "n8n API", "documentationUrl": "https://docs.n8n.io/api/authentication/", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The API key for the n8n instance"}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "https://<name>.app.n8n.cloud/api/v1", "description": "The API URL of the n8n instance"}], "authenticate": {"type": "generic", "properties": {"headers": {"X-N8N-API-KEY": "={{ $credentials.apiKey }}"}}}, "test": {"request": {"baseURL": "={{ $credentials.baseUrl }}", "url": "/workflows?limit=5"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/N8n/n8n.svg", "supportedNodes": ["n8n-nodes-base.n8n"]}, {"name": "nasa<PERSON>pi", "displayName": "NASA API", "documentationUrl": "nasa", "properties": [{"displayName": "API Key", "name": "api_key", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Nasa/nasa.png", "supportedNodes": ["n8n-nodes-base.nasa"]}, {"name": "netlifyApi", "displayName": "Netlify API", "documentationUrl": "netlify", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Netlify/netlify.svg", "supportedNodes": ["n8n-nodes-base.netlify", "n8n-nodes-base.netlifyTrigger"]}, {"name": "citrixAdcApi", "displayName": "Netscaler ADC API", "documentationUrl": "netscaleradc", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "required": true}, {"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true}, {"displayName": "Password", "name": "password", "type": "string", "default": "", "required": true, "typeOptions": {"password": true}}], "authenticate": {"type": "generic", "properties": {"headers": {"X-NITRO-USER": "={{$credentials.username}}", "X-NITRO-PASS": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/nitro/v1/config/nspartition?view=summary"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Netscaler/ADC/netscaler.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Netscaler/ADC/netscaler.dark.svg"}, "supportedNodes": ["n8n-nodes-base.citrixAdc"]}, {"name": "nextCloudApi", "displayName": "NextCloud API", "documentationUrl": "nextCloud", "properties": [{"displayName": "Web DAV URL", "name": "webDavUrl", "type": "string", "placeholder": "https://nextcloud.example.com/remote.php/webdav", "default": ""}, {"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "={{$credentials.webDavUrl.replace('/remote.php/webdav', '')}}", "url": "/ocs/v1.php/cloud/capabilities", "headers": {"OCS-APIRequest": true}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/NextCloud/nextcloud.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.nextCloud"]}, {"name": "nextCloudOAuth2Api", "extends": ["oAuth2Api"], "displayName": "NextCloud OAuth2 API", "documentationUrl": "nextCloud", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Web DAV URL", "name": "webDavUrl", "type": "string", "placeholder": "https://nextcloud.example.com/remote.php/webdav", "default": ""}, {"displayName": "Authorization URL", "name": "authUrl", "type": "string", "default": "https://nextcloud.example.com/apps/oauth2/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "string", "default": "https://nextcloud.example.com/apps/oauth2/api/v1/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/NextCloud/nextcloud.svg", "supportedNodes": ["n8n-nodes-base.nextCloud"]}, {"name": "nocoDb", "displayName": "NocoDB", "documentationUrl": "nocoDb", "properties": [{"displayName": "User <PERSON>", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Host", "name": "host", "type": "string", "default": "", "placeholder": "http(s)://localhost:8080"}], "authenticate": {"type": "generic", "properties": {"headers": {"xc-auth": "={{$credentials.apiToken}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/NocoDB/nocodb.svg", "supportedNodes": ["n8n-nodes-base.nocoDb"]}, {"name": "nocoDbApiToken", "displayName": "NocoDB API Token", "documentationUrl": "nocoDb", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Host", "name": "host", "type": "string", "default": "", "placeholder": "http(s)://localhost:8080"}], "authenticate": {"type": "generic", "properties": {"headers": {"xc-token": "={{$credentials.apiToken}}"}}}, "test": {"request": {"baseURL": "={{ $credentials.host }}", "url": "/api/v1/auth/user/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/NocoDB/nocodb.svg", "supportedNodes": ["n8n-nodes-base.nocoDb"]}, {"name": "notionApi", "displayName": "Notion API", "documentationUrl": "notion", "properties": [{"displayName": "Internal Integration Secret", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://api.notion.com/v1", "url": "/users/me"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Notion/notion.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Notion/notion.dark.svg"}, "authenticate": {}, "supportedNodes": ["n8n-nodes-base.notion", "n8n-nodes-base.notionTrigger"]}, {"name": "notionOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Notion OAuth2 API", "documentationUrl": "notion", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://api.notion.com/v1/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.notion.com/v1/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "supportedNodes": []}, {"name": "npmApi", "displayName": "Npm API", "documentationUrl": "npm", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Registry Url", "name": "registryUrl", "type": "string", "default": "https://registry.npmjs.org"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials.registryUrl}}", "url": "/-/whoami"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Npm/npm.svg", "supportedNodes": ["n8n-nodes-base.npm"]}, {"name": "oAuth1Api", "displayName": "OAuth1 API", "documentationUrl": "httpRequest", "genericAuth": true, "properties": [{"displayName": "Authorization URL", "name": "authUrl", "type": "string", "default": "", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "string", "default": "", "required": true}, {"displayName": "Consumer Key", "name": "consumerKey", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Consumer Secret", "name": "consumerSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Request Token URL", "name": "requestTokenUrl", "type": "string", "default": "", "required": true}, {"displayName": "Signature Method", "name": "signatureMethod", "type": "options", "options": [{"name": "HMAC-SHA1", "value": "HMAC-SHA1"}, {"name": "HMAC-SHA256", "value": "HMAC-SHA256"}, {"name": "HMAC-SHA512", "value": "HMAC-SHA512"}], "default": "HMAC-SHA1", "required": true}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GraphQL/graphql.png", "supportedNodes": ["n8n-nodes-base.graphql", "n8n-nodes-base.httpRequest"]}, {"name": "oAuth2Api", "displayName": "OAuth2 API", "documentationUrl": "httpRequest", "genericAuth": true, "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "options", "options": [{"name": "Authorization Code", "value": "authorizationCode"}, {"name": "Client Credentials", "value": "clientCredentials"}, {"name": "PKCE", "value": "pkce"}], "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "string", "displayOptions": {"show": {"grantType": ["authorizationCode", "pkce"]}}, "default": "", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "string", "default": "", "required": true}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "string", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "string", "displayOptions": {"show": {"grantType": ["authorizationCode", "pkce"]}}, "default": "", "description": "For some services additional query parameters have to be set which can be defined here", "placeholder": "access_type=offline"}, {"displayName": "Authentication", "name": "authentication", "type": "options", "options": [{"name": "Body", "value": "body", "description": "Send credentials in body"}, {"name": "Header", "value": "header", "description": "Send credentials as Basic Auth header"}], "default": "header"}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "ignoreSSLIssues", "type": "boolean", "default": false, "doNotInherit": true}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GraphQL/graphql.png", "supportedNodes": ["n8n-nodes-base.graphql", "n8n-nodes-base.httpRequest"]}, {"name": "odooApi", "displayName": "Odoo API", "documentationUrl": "odoo", "properties": [{"displayName": "Site URL", "name": "url", "type": "string", "default": "", "placeholder": "https://my-organization.odoo.com", "required": true}, {"displayName": "Username", "name": "username", "type": "string", "default": "", "placeholder": "<EMAIL>", "required": true}, {"displayName": "Password or API Key", "name": "password", "type": "string", "default": "", "typeOptions": {"password": true}, "required": true}, {"displayName": "Database Name", "name": "db", "type": "string", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Odoo/odoo.svg", "supportedNodes": ["n8n-nodes-base.odoo"]}, {"name": "oktaApi", "displayName": "Okta API", "documentationUrl": "okta", "httpRequestNode": {"name": "Okta", "docsUrl": "https://developer.okta.com/docs/reference/", "apiBaseUrl": "", "hidden": true}, "properties": [{"displayName": "URL", "name": "url", "type": "string", "required": true, "default": "", "placeholder": "https://dev-123456.okta.com"}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "required": true, "default": "", "description": "Secure Session Web Service Access Token"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=SSWS {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/api/v1/api-tokens"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Okta.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Okta.dark.svg"}, "supportedNodes": ["n8n-nodes-base.okta"]}, {"name": "oneSimpleApi", "displayName": "One Simple API", "documentationUrl": "oneSimpleApi", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/OneSimpleApi/onesimpleapi.svg", "supportedNodes": ["n8n-nodes-base.oneSimpleApi"]}, {"name": "onfleetApi", "displayName": "Onfleet API", "documentationUrl": "onfleet", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Onfleet/Onfleet.svg", "supportedNodes": ["n8n-nodes-base.onfleet", "n8n-nodes-base.onfleetTrigger"]}, {"name": "openAiApi", "displayName": "OpenAi", "documentationUrl": "openAi", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Organization ID (optional)", "name": "organizationId", "type": "string", "default": "", "hint": "Only required if you belong to multiple organisations", "description": "For users who belong to multiple organizations, you can set which organization is used for an API request. Usage from these API requests will count against the specified organization's subscription quota."}, {"displayName": "Base URL", "name": "url", "type": "string", "default": "https://api.openai.com/v1", "description": "Override the default base URL for the API"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}", "OpenAI-Organization": "={{$credentials.organizationId}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "/models"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/OpenAi/openAi.svg", "dark": "icons/n8n-nodes-base/dist/nodes/OpenAi/openAi.dark.svg"}, "supportedNodes": ["n8n-nodes-base.openAi"]}, {"name": "openCtiApi", "displayName": "OpenCTI API", "documentationUrl": "opencti", "httpRequestNode": {"name": "OpenCTI", "docsUrl": "https://docs.opencti.io/latest/deployment/integrations/?h=api#graphql-api", "apiBaseUrl": ""}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/OpenCTI.png", "supportedNodes": []}, {"name": "openWeatherMapApi", "displayName": "OpenWeatherMap API", "documentationUrl": "openWeatherMap", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/OpenWeatherMap/openWeatherMap.svg", "supportedNodes": ["n8n-nodes-base.openWeatherMap"]}, {"name": "orbitApi", "displayName": "Orbit API", "documentationUrl": "orbit", "properties": [{"displayName": "Orbit has been shutdown and will no longer function from July 11th, You can read more <a target=\"_blank\" href=\"https://orbit.love/blog/orbit-is-joining-postman\">here</a>.", "name": "deprecated", "type": "notice", "default": ""}, {"displayName": "API Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Orbit/orbit.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Orbit/orbit.dark.svg"}, "supportedNodes": ["n8n-nodes-base.orbit"]}, {"name": "ouraApi", "displayName": "Oura API", "documentationUrl": "oura", "properties": [{"displayName": "Personal Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.ouraring.com", "url": "/v2/usercollection/personal_info"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Oura/oura.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Oura/oura.dark.svg"}, "supportedNodes": ["n8n-nodes-base.oura"]}, {"name": "paddleApi", "displayName": "Paddle API", "documentationUrl": "paddle", "properties": [{"displayName": "Vendor <PERSON>th <PERSON>", "name": "vendorAuthCode", "type": "string", "default": ""}, {"displayName": "Vendor ID", "name": "vendorId", "type": "string", "default": ""}, {"displayName": "Use Sandbox Environment API", "name": "sandbox", "type": "boolean", "default": false}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Paddle/paddle.png", "supportedNodes": ["n8n-nodes-base.paddle"]}, {"name": "pagerDutyApi", "displayName": "PagerDuty API", "documentationUrl": "pager<PERSON>uty", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PagerDuty/pagerDuty.svg", "supportedNodes": ["n8n-nodes-base.pagerDuty"]}, {"name": "pagerDutyOAuth2Api", "extends": ["oAuth2Api"], "displayName": "PagerDuty OAuth2 API", "documentationUrl": "pager<PERSON>uty", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://app.pagerduty.com/oauth/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://app.pagerduty.com/oauth/token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "write"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PagerDuty/pagerDuty.svg", "supportedNodes": ["n8n-nodes-base.pagerDuty"]}, {"name": "payPalApi", "displayName": "PayPal API", "documentationUrl": "payPal", "properties": [{"displayName": "Client ID", "name": "clientId", "type": "string", "default": ""}, {"displayName": "Secret", "name": "secret", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Environment", "name": "env", "type": "options", "default": "live", "options": [{"name": "Sandbox", "value": "sanbox"}, {"name": "Live", "value": "live"}]}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PayPal/paypal.svg", "supportedNodes": ["n8n-nodes-base.payPal", "n8n-nodes-base.payPalTrigger"]}, {"name": "peekalink<PERSON><PERSON>", "displayName": "Peekalink API", "documentationUrl": "peekalink", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Peekalink/peekalink.png", "supportedNodes": ["n8n-nodes-base.peekalink"]}, {"name": "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "displayName": "Phantombuster API", "documentationUrl": "phantombuster", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Phantombuster-Key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.phantombuster.com/api/v2", "url": "/agents/fetch-all"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Phantombuster/phantombuster.png", "supportedNodes": ["n8n-nodes-base.phantombuster"]}, {"name": "philipsHueOAuth2Api", "extends": ["oAuth2Api"], "displayName": "PhilipHue OAuth2 API", "documentationUrl": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "APP ID", "name": "appId", "type": "string", "default": ""}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://api.meethue.com/v2/oauth2/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.meethue.com/v2/oauth2/token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "={{\"appid=\"+$self[\"appId\"]}}"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PhilipsHue/philipshue.png", "supportedNodes": ["n8n-nodes-base.philipsHue"]}, {"name": "pipedriveApi", "displayName": "Pipedrive API", "documentationUrl": "pipedrive", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"api_token": "={{$credentials.apiToken}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pipedrive/pipedrive.svg", "supportedNodes": ["n8n-nodes-base.pipedrive", "n8n-nodes-base.pipedriveTrigger"]}, {"name": "pipedriveOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Pipedrive OAuth2 API", "documentationUrl": "pipedrive", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://oauth.pipedrive.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://oauth.pipedrive.com/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pipedrive/pipedrive.svg", "supportedNodes": ["n8n-nodes-base.pipedrive", "n8n-nodes-base.pipedriveTrigger"]}, {"name": "plivoApi", "displayName": "Plivo API", "documentationUrl": "plivo", "properties": [{"displayName": "Auth ID", "name": "authId", "type": "string", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "authToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Plivo/plivo.svg", "supportedNodes": ["n8n-nodes-base.plivo"]}, {"name": "postgres", "displayName": "Postgres", "documentationUrl": "postgres", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "postgres"}, {"displayName": "User", "name": "user", "type": "string", "default": "postgres"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Maximum Number of Connections", "name": "maxConnections", "type": "number", "default": 100, "description": "Make sure this value times the number of workers you have is lower than the maximum number of connections your postgres instance allows."}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "default": false, "description": "Whether to connect even if SSL certificate validation is not possible"}, {"displayName": "SSL", "name": "ssl", "type": "options", "displayOptions": {"show": {"allowUnauthorizedCerts": [false]}}, "options": [{"name": "Allow", "value": "allow"}, {"name": "Disable", "value": "disable"}, {"name": "Require", "value": "require"}], "default": "disable"}, {"displayName": "Port", "name": "port", "type": "number", "default": 5432}, {"displayName": "SSH Tunnel", "name": "sshTunnel", "type": "boolean", "default": false}, {"displayName": "SSH Authenticate with", "name": "sshAuthenticateWith", "type": "options", "default": "password", "options": [{"name": "Password", "value": "password"}, {"name": "Private Key", "value": "privateKey"}], "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH Host", "name": "sshHost", "type": "string", "default": "localhost", "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH Port", "name": "sshPort", "type": "number", "default": 22, "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH User", "name": "sshUser", "type": "string", "default": "root", "displayOptions": {"show": {"sshTunnel": [true]}}}, {"displayName": "SSH Password", "name": "sshPassword", "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"sshTunnel": [true], "sshAuthenticateWith": ["password"]}}}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "typeOptions": {"rows": 4, "password": true}, "default": "", "displayOptions": {"show": {"sshTunnel": [true], "sshAuthenticateWith": ["privateKey"]}}}, {"displayName": "Passphrase", "name": "passphrase", "type": "string", "default": "", "description": "Passphrase used to create the key, if no passphrase was used leave empty", "displayOptions": {"show": {"sshTunnel": [true], "sshAuthenticateWith": ["privateKey"]}}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Postgres/postgres.svg", "supportedNodes": ["n8n-nodes-base.postgres", "n8n-nodes-base.postgresTrigger"]}, {"name": "postHogApi", "displayName": "PostHog API", "documentationUrl": "postHog", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "https://app.posthog.com"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PostHog/postHog.svg", "supportedNodes": ["n8n-nodes-base.postHog"]}, {"name": "postmarkApi", "displayName": "Postmark API", "documentationUrl": "postmark", "properties": [{"displayName": "Server API Token", "name": "serverToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Postmark-Server-Token": "={{$credentials.serverToken}}"}}}, "test": {"request": {"baseURL": "https://api.postmarkapp.com", "url": "/server", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Postmark/postmark.png", "supportedNodes": ["n8n-nodes-base.postmarkTrigger"]}, {"name": "profitWellApi", "displayName": "ProfitWell API", "documentationUrl": "profitWell", "properties": [{"displayName": "API Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Your Private Token"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/ProfitWell/profitwell.svg", "dark": "icons/n8n-nodes-base/dist/nodes/ProfitWell/profitwell.dark.svg"}, "supportedNodes": ["n8n-nodes-base.profitWell"]}, {"name": "pushbulletOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Pushbullet OAuth2 API", "documentationUrl": "pushbullet", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.pushbullet.com/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.pushbullet.com/oauth2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pushbullet/pushbullet.svg", "supportedNodes": ["n8n-nodes-base.pushbullet"]}, {"name": "pushcutApi", "displayName": "Pushcut API", "documentationUrl": "pushcut", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pushcut/pushcut.png", "supportedNodes": ["n8n-nodes-base.pushcut", "n8n-nodes-base.pushcutTrigger"]}, {"name": "pushoverApi", "displayName": "Pushover API", "documentationUrl": "pushover", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://api.pushover.net/1", "url": "=/licenses.json?token={{$credentials?.apiKey}}", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pushover/pushover.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.pushover"]}, {"name": "qRadarApi", "displayName": "QRadar API", "documentationUrl": "qradar", "httpRequestNode": {"name": "QRadar", "docsUrl": "https://www.ibm.com/docs/en/qradar-common", "apiBaseUrl": ""}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"SEC": "={{$credentials.apiKey}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/IBM.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/IBM.dark.svg"}, "supportedNodes": []}, {"name": "qualys<PERSON>pi", "displayName": "Qualys API", "documentationUrl": "qualys", "httpRequestNode": {"name": "Qualys", "docsUrl": "https://qualysguard.qg2.apps.qualys.com/qwebhelp/fo_portal/api_doc/index.htm", "apiBaseUrl": "https://qualysapi.qualys.com/api/"}, "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Requested With", "name": "requestedWith", "type": "string", "default": "n8n application", "description": "User description, like a user agent"}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Requested-With": "={{$credentials.requestedWith}}"}, "auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Qualys.svg", "supportedNodes": []}, {"name": "questDb", "displayName": "QuestDB", "documentationUrl": "questDb", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "qdb"}, {"displayName": "User", "name": "user", "type": "string", "default": "admin"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "quest"}, {"displayName": "SSL", "name": "ssl", "type": "options", "options": [{"name": "Allow", "value": "allow"}, {"name": "Disable", "value": "disable"}, {"name": "Require", "value": "require"}], "default": "disable"}, {"displayName": "Port", "name": "port", "type": "number", "default": 8812}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuestDb/questdb.png", "supportedNodes": ["n8n-nodes-base.questDb"]}, {"name": "quickbaseApi", "displayName": "Quick Base API", "documentationUrl": "quickbase", "properties": [{"displayName": "Hostname", "name": "hostname", "type": "string", "default": "", "required": true, "placeholder": "demo.quickbase.com"}, {"displayName": "User <PERSON>", "name": "userToken", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuickBase/quickbase.png", "supportedNodes": ["n8n-nodes-base.quickbase"]}, {"name": "quickBooksOAuth2Api", "extends": ["oAuth2Api"], "displayName": "QuickBooks Online OAuth2 API", "documentationUrl": "quickbooks", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://appcenter.intuit.com/connect/oauth2"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "com.intuit.quickbooks.accounting"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}, {"displayName": "Environment", "name": "environment", "type": "options", "default": "production", "options": [{"name": "Production", "value": "production"}, {"name": "Sandbox", "value": "sandbox"}]}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuickBooks/quickbooks.svg", "supportedNodes": ["n8n-nodes-base.quickbooks"]}, {"name": "rabbitmq", "displayName": "RabbitMQ", "documentationUrl": "rabbitmq", "properties": [{"displayName": "Hostname", "name": "hostname", "type": "string", "default": "", "placeholder": "localhost"}, {"displayName": "Port", "name": "port", "type": "number", "default": 5672}, {"displayName": "User", "name": "username", "type": "string", "default": "", "placeholder": "guest"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "placeholder": "guest"}, {"displayName": "Vhost", "name": "vhost", "type": "string", "default": "/"}, {"displayName": "SSL", "name": "ssl", "type": "boolean", "default": false}, {"displayName": "Passwordless", "name": "passwordless", "type": "boolean", "displayOptions": {"show": {"ssl": [true]}}, "default": true, "description": "Whether to use passwordless connection with certificates (SASL mechanism EXTERNAL)"}, {"displayName": "CA Certificates", "name": "ca", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true]}}, "default": "", "description": "SSL CA Certificates to use"}, {"displayName": "Client Certificate", "name": "cert", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true], "passwordless": [true]}}, "default": "", "description": "SSL Client Certificate to use"}, {"displayName": "Client Key", "name": "key", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true], "passwordless": [true]}}, "default": "", "description": "SSL Client Key to use"}, {"displayName": "Passphrase", "name": "passphrase", "type": "string", "typeOptions": {"password": true}, "displayOptions": {"show": {"ssl": [true], "passwordless": [true]}}, "default": "", "description": "SSL passphrase to use"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/RabbitMQ/rabbitmq.svg", "supportedNodes": ["n8n-nodes-base.rabbitmq", "n8n-nodes-base.rabbitmqTrigger"]}, {"name": "raindropOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Raindrop OAuth2 API", "documentationUrl": "raindrop", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://raindrop.io/oauth/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.raindrop.io/v1/oauth/access_token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Raindrop/raindrop.svg", "supportedNodes": ["n8n-nodes-base.raindrop"]}, {"name": "rapid7InsightVmApi", "displayName": "Rapid7 InsightVM API", "documentationUrl": "rapid7insightvm", "httpRequestNode": {"name": "Rapid7 InsightVM", "docsUrl": "https://docs.rapid7.com/", "apiBaseUrlPlaceholder": "https://insight.rapid7.com/"}, "properties": [{"displayName": "URL", "name": "url", "required": true, "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"x-api-key": "={{$credentials.apiKey}}", "Accept": "application/json"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/validate", "method": "GET"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Rapid7InsightVm.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Rapid7InsightVm.svg"}, "supportedNodes": []}, {"name": "recordedFutureApi", "displayName": "Recorded Future API", "documentationUrl": "recordedfuture", "httpRequestNode": {"name": "Recorded Future", "docsUrl": "https://api.recordedfuture.com", "apiBaseUrl": "https://api.recordedfuture.com/v2/"}, "properties": [{"displayName": "Access Token", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-RFToken": "={{$credentials.accessToken}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/RecordedFuture.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/RecordedFuture.dark.svg"}, "supportedNodes": []}, {"name": "redditOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Reddit OAuth2 API", "documentationUrl": "reddit", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "response_type=code"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "grant_type=authorization_code"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "duration=permanent"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.reddit.com/api/v1/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://www.reddit.com/api/v1/access_token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "identity edit history mysubreddits read save submit"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Reddit/reddit.svg", "supportedNodes": ["n8n-nodes-base.reddit"]}, {"name": "redis", "displayName": "Redis", "documentationUrl": "redis", "properties": [{"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "User", "name": "user", "type": "string", "default": "", "hint": "Leave blank for password-only auth"}, {"displayName": "Host", "name": "host", "type": "string", "default": "localhost"}, {"displayName": "Port", "name": "port", "type": "number", "default": 6379}, {"displayName": "Database Number", "name": "database", "type": "number", "default": 0}, {"displayName": "SSL", "name": "ssl", "type": "boolean", "default": false}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Redis/redis.svg", "supportedNodes": ["n8n-nodes-base.redis", "n8n-nodes-base.redisTrigger"]}, {"name": "rocketchatApi", "displayName": "Rocket API", "documentationUrl": "rocketchat", "properties": [{"displayName": "User ID", "name": "userId", "type": "string", "default": ""}, {"displayName": "Auth Key", "name": "auth<PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://n8n.rocket.chat"}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Auth-Token": "={{$credentials.authKey}}", "X-User-Id": "={{$credentials.userId}}"}}}, "test": {"request": {"baseURL": "={{$credentials.domain}}", "url": "/api/v1/webdav.getMyAccounts"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Rocketchat/rocketchat.svg", "supportedNodes": ["n8n-nodes-base.rocketchat"]}, {"name": "rundeckApi", "displayName": "Rundeck API", "documentationUrl": "rundeck", "properties": [{"displayName": "Url", "name": "url", "type": "string", "default": "", "placeholder": "http://127.0.0.1:4440"}, {"displayName": "Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"user-agent": "n8n", "X-Rundeck-Auth-Token": "={{$credentials?.token}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/api/14/system/info", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Rundeck/rundeck.png", "supportedNodes": ["n8n-nodes-base.rundeck"]}, {"name": "s3", "displayName": "S3", "documentationUrl": "s3", "properties": [{"displayName": "S3 Endpoint", "name": "endpoint", "type": "string", "default": ""}, {"displayName": "Region", "name": "region", "type": "string", "default": "us-east-1"}, {"displayName": "Access Key ID", "name": "accessKeyId", "type": "string", "default": ""}, {"displayName": "Secret Access Key", "name": "secretAccessKey", "type": "string", "default": "", "typeOptions": {"password": true}}, {"displayName": "Force Path Style", "name": "forcePathStyle", "type": "boolean", "default": false}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "ignoreSSLIssues", "type": "boolean", "default": false, "description": "Whether to connect even if SSL certificate validation is not possible"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/S3/s3.png", "supportedNodes": ["n8n-nodes-base.s3"]}, {"name": "salesforceJwtApi", "displayName": "Salesforce JWT API", "documentationUrl": "salesforce", "properties": [{"displayName": "Environment Type", "name": "environment", "type": "options", "options": [{"name": "Production", "value": "production"}, {"name": "Sandbox", "value": "sandbox"}], "default": "production"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true, "description": "Consumer Key from Salesforce Connected App"}, {"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true, "description": "Use the multiline editor. Make sure it is in standard PEM key format:<br />-----BEGIN PRIVATE KEY-----<br />KEY DATA GOES HERE<br />-----END PRIVATE KEY-----"}], "test": {"request": {"baseURL": "={{$credentials?.environment === \"sandbox\" ? \"https://test.salesforce.com\" : \"https://login.salesforce.com\"}}", "url": "/services/oauth2/userinfo", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Salesforce/salesforce.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.salesforce"]}, {"name": "salesforceOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Salesforce OAuth2 API", "documentationUrl": "salesforce", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "pkce"}, {"displayName": "Environment Type", "name": "environment", "type": "options", "options": [{"name": "Production", "value": "production"}, {"name": "Sandbox", "value": "sandbox"}], "default": "production"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "required": true, "default": "={{ $self[\"environment\"] === \"sandbox\" ? \"https://test.salesforce.com/services/oauth2/authorize\" : \"https://login.salesforce.com/services/oauth2/authorize\" }}"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "required": true, "default": "={{ $self[\"environment\"] === \"sandbox\" ? \"https://test.salesforce.com/services/oauth2/token\" : \"https://login.salesforce.com/services/oauth2/token\" }}"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "full refresh_token"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Salesforce/salesforce.svg", "supportedNodes": ["n8n-nodes-base.salesforce", "n8n-nodes-base.salesforceTrigger"]}, {"name": "salesmateApi", "displayName": "Salesmate API", "documentationUrl": "salesmate", "properties": [{"displayName": "Session Token", "name": "sessionToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "n8n.salesmate.io"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Salesmate/salesmate.png", "supportedNodes": ["n8n-nodes-base.salesmate"]}, {"name": "seaTableApi", "displayName": "SeaTable API", "documentationUrl": "seaTable", "properties": [{"displayName": "Environment", "name": "environment", "type": "options", "default": "cloudHosted", "options": [{"name": "Cloud-Hosted", "value": "cloudHosted"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "Self-Hosted Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://seatable.example.com", "displayOptions": {"show": {"environment": ["selfHosted"]}}}, {"displayName": "API Token (of a Base)", "name": "token", "type": "string", "description": "The API-Token of the SeaTable base you would like to use with n8n. n8n can only connect to one base at a time.", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Timezone", "name": "timezone", "type": "options", "default": "", "description": "Seatable server's timezone", "options": [{"value": "Europe/Andorra", "name": "Europe/Andorra"}, {"value": "Asia/Dubai", "name": "Asia/Dubai"}, {"value": "Asia/Kabul", "name": "Asia/Kabul"}, {"value": "America/Antigua", "name": "America/Antigua"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/Anguilla", "name": "America/Anguilla"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Europe/Tirane", "name": "Europe/Tirane"}, {"value": "Asia/Yerevan", "name": "Asia/Yerevan"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Luanda", "name": "Africa/Luanda"}, {"value": "Antarctica/Casey", "name": "Antarctica/Casey"}, {"value": "Antarctica/Davis", "name": "Antarctica/Davis"}, {"value": "Antarctica/DumontDUrville", "name": "Antarctica/DumontDUrville"}, {"value": "Antarctica/Mawson", "name": "Antarctica/Mawson"}, {"value": "Antarctica/McMurdo", "name": "Antarctica/McMurdo"}, {"value": "Antarctica/Palmer", "name": "Antarctica/Palmer"}, {"value": "Antarctica/Rothera", "name": "Antarctica/Rothera"}, {"value": "Antarctica/Syowa", "name": "Antarctica/Syowa"}, {"value": "Antarctica/Troll", "name": "Antarctica/Troll"}, {"value": "Antarctica/Vostok", "name": "Antarctica/Vostok"}, {"value": "Asia/Riyadh", "name": "Asia/Riyadh"}, {"value": "Asia/Urumqi", "name": "Asia/Urumqi"}, {"value": "Pacific/Auckland", "name": "Pacific/Auckland"}, {"value": "Pacific/Port_Moresby", "name": "Pacific/Port_Moresby"}, {"value": "America/Argentina/Buenos_Aires", "name": "America/Argentina/Buenos_Aires"}, {"value": "America/Argentina/Catamarca", "name": "America/Argentina/Catamarca"}, {"value": "America/Argentina/Cordoba", "name": "America/Argentina/Cordoba"}, {"value": "America/Argentina/Jujuy", "name": "America/Argentina/Jujuy"}, {"value": "America/Argentina/La_Rioja", "name": "America/Argentina/La_Rioja"}, {"value": "America/Argentina/Mendoza", "name": "America/Argentina/Mendoza"}, {"value": "America/Argentina/Rio_Gallegos", "name": "America/Argentina/Rio_Gallegos"}, {"value": "America/Argentina/Salta", "name": "America/Argentina/Salta"}, {"value": "America/Argentina/San_Juan", "name": "America/Argentina/San_Juan"}, {"value": "America/Argentina/San_Luis", "name": "America/Argentina/San_Luis"}, {"value": "America/Argentina/Tucuman", "name": "America/Argentina/Tucuman"}, {"value": "America/Argentina/Ushuaia", "name": "America/Argentina/Ushuaia"}, {"value": "Pacific/Pago_Pago", "name": "Pacific/Pago_Pago"}, {"value": "Europe/Vienna", "name": "Europe/Vienna"}, {"value": "Antarctica/Macquarie", "name": "Antarctica/Macquarie"}, {"value": "Australia/Adelaide", "name": "Australia/Adelaide"}, {"value": "Australia/Brisbane", "name": "Australia/Brisbane"}, {"value": "Australia/Broken_Hill", "name": "Australia/Broken_Hill"}, {"value": "Australia/Darwin", "name": "Australia/Darwin"}, {"value": "Australia/Eucla", "name": "Australia/Eucla"}, {"value": "Australia/Hobart", "name": "Australia/Hobart"}, {"value": "Australia/Lindeman", "name": "Australia/Lindeman"}, {"value": "Australia/Lord_Howe", "name": "Australia/Lord_Howe"}, {"value": "Australia/Melbourne", "name": "Australia/Melbourne"}, {"value": "Australia/Perth", "name": "Australia/Perth"}, {"value": "Australia/Sydney", "name": "Australia/Sydney"}, {"value": "America/Aruba", "name": "America/Aruba"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Europe/Helsinki", "name": "Europe/Helsinki"}, {"value": "Europe/Mariehamn", "name": "Europe/Mariehamn"}, {"value": "Asia/Baku", "name": "Asia/Baku"}, {"value": "Europe/Belgrade", "name": "Europe/Belgrade"}, {"value": "Europe/Sarajevo", "name": "Europe/Sarajevo"}, {"value": "America/Barbados", "name": "America/Barbados"}, {"value": "Asia/Dhaka", "name": "Asia/Dhaka"}, {"value": "Europe/Brussels", "name": "Europe/Brussels"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Ouagadougou", "name": "Africa/Ouagadougou"}, {"value": "Europe/Sofia", "name": "Europe/Sofia"}, {"value": "Asia/Bahrain", "name": "Asia/Bahrain"}, {"value": "Asia/Qatar", "name": "Asia/Qatar"}, {"value": "Africa/Bujumbura", "name": "Africa/Bujumbura"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Porto-Novo", "name": "Africa/Porto-Novo"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/St_Barthelemy", "name": "America/St_Barthelemy"}, {"value": "Atlantic/Bermuda", "name": "Atlantic/Bermuda"}, {"value": "Asia/Brunei", "name": "Asia/Brunei"}, {"value": "Asia/Kuching", "name": "Asia/Kuching"}, {"value": "America/La_Paz", "name": "America/La_Paz"}, {"value": "America/Kralendijk", "name": "America/Kralendijk"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/Araguaina", "name": "America/Araguaina"}, {"value": "America/Bahia", "name": "America/Bahia"}, {"value": "America/Belem", "name": "America/Belem"}, {"value": "America/Boa_Vista", "name": "America/Boa_Vista"}, {"value": "America/Campo_Grande", "name": "America/Campo_Grande"}, {"value": "America/Cuiaba", "name": "America/Cuiaba"}, {"value": "America/Eirunepe", "name": "America/Eirunepe"}, {"value": "America/Fortaleza", "name": "America/Fortaleza"}, {"value": "America/Maceio", "name": "America/Maceio"}, {"value": "America/Manaus", "name": "America/Manaus"}, {"value": "America/Noronha", "name": "America/Noronha"}, {"value": "America/Porto_Velho", "name": "America/Porto_Velho"}, {"value": "America/Recife", "name": "America/Recife"}, {"value": "America/Rio_Branco", "name": "America/Rio_Branco"}, {"value": "America/Santarem", "name": "America/Santarem"}, {"value": "America/Sao_Paulo", "name": "America/Sao_Paulo"}, {"value": "America/Nassau", "name": "America/Nassau"}, {"value": "America/Toronto", "name": "America/Toronto"}, {"value": "Asia/Thimphu", "name": "Asia/Thimphu"}, {"value": "Africa/Gaborone", "name": "Africa/Gaborone"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "Europe/Minsk", "name": "Europe/Minsk"}, {"value": "America/Belize", "name": "America/Belize"}, {"value": "America/Atikokan", "name": "America/Atikokan"}, {"value": "America/Blanc-Sablon", "name": "America/Blanc-Sablon"}, {"value": "America/Cambridge_Bay", "name": "America/Cambridge_Bay"}, {"value": "America/Creston", "name": "America/Creston"}, {"value": "America/Dawson", "name": "America/Dawson"}, {"value": "America/Dawson_Creek", "name": "America/Dawson_Creek"}, {"value": "America/Edmonton", "name": "America/Edmonton"}, {"value": "America/Fort_Nelson", "name": "America/Fort_Nelson"}, {"value": "America/Glace_Bay", "name": "America/Glace_Bay"}, {"value": "America/Goose_Bay", "name": "America/Goose_Bay"}, {"value": "America/Halifax", "name": "America/Halifax"}, {"value": "America/Inuvik", "name": "America/Inuvik"}, {"value": "America/Iqaluit", "name": "America/Iqaluit"}, {"value": "America/Moncton", "name": "America/Moncton"}, {"value": "America/Nipigon", "name": "America/Nipigon"}, {"value": "America/Panama", "name": "America/Panama"}, {"value": "America/Pangnirtung", "name": "America/Pangnirtung"}, {"value": "America/Phoenix", "name": "America/Phoenix"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/Rainy_River", "name": "America/Rainy_River"}, {"value": "America/Rankin_Inlet", "name": "America/Rankin_Inlet"}, {"value": "America/Regina", "name": "America/Regina"}, {"value": "America/Resolute", "name": "America/Resolute"}, {"value": "America/St_Johns", "name": "America/St_Johns"}, {"value": "America/Swift_Current", "name": "America/Swift_Current"}, {"value": "America/Thunder_Bay", "name": "America/Thunder_Bay"}, {"value": "America/Toronto", "name": "America/Toronto"}, {"value": "America/Vancouver", "name": "America/Vancouver"}, {"value": "America/Whitehorse", "name": "America/Whitehorse"}, {"value": "America/Winnipeg", "name": "America/Winnipeg"}, {"value": "America/Yellowknife", "name": "America/Yellowknife"}, {"value": "Asia/Yangon", "name": "Asia/Yangon"}, {"value": "Indian/Cocos", "name": "Indian/Cocos"}, {"value": "Africa/Kinshasa", "name": "Africa/Kinshasa"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Lubumbashi", "name": "Africa/Lubumbashi"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "Africa/Bangui", "name": "Africa/Bangui"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Brazzaville", "name": "Africa/Brazzaville"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Europe/Zurich", "name": "Europe/Zurich"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Pacific/Rarotonga", "name": "Pacific/Rarotonga"}, {"value": "America/Punta_Arenas", "name": "America/Punta_Arenas"}, {"value": "America/Santiago", "name": "America/Santiago"}, {"value": "Pacific/Easter", "name": "Pacific/Easter"}, {"value": "Africa/Douala", "name": "Africa/Douala"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Asia/Shanghai", "name": "Asia/Shanghai"}, {"value": "Asia/Urumqi", "name": "Asia/Urumqi"}, {"value": "America/Bogota", "name": "America/Bogota"}, {"value": "America/Costa_Rica", "name": "America/Costa_Rica"}, {"value": "America/Havana", "name": "America/Havana"}, {"value": "Atlantic/Cape_Verde", "name": "Atlantic/Cape_Verde"}, {"value": "America/Curacao", "name": "America/Curacao"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Asia/Bangkok", "name": "Asia/Bangkok"}, {"value": "Indian/Christmas", "name": "Indian/Christmas"}, {"value": "Asia/Famagusta", "name": "Asia/Famagusta"}, {"value": "Asia/Nicosia", "name": "Asia/Nicosia"}, {"value": "Europe/Prague", "name": "Europe/Prague"}, {"value": "Europe/Berlin", "name": "Europe/Berlin"}, {"value": "Europe/Busingen", "name": "Europe/Busingen"}, {"value": "Europe/Zurich", "name": "Europe/Zurich"}, {"value": "Africa/Djibouti", "name": "Africa/Djibouti"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Europe/Berlin", "name": "Europe/Berlin"}, {"value": "Europe/Copenhagen", "name": "Europe/Copenhagen"}, {"value": "America/Dominica", "name": "America/Dominica"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/Santo_Domingo", "name": "America/Santo_Domingo"}, {"value": "Africa/Algiers", "name": "Africa/Algiers"}, {"value": "America/Guayaquil", "name": "America/Guayaquil"}, {"value": "Pacific/Galapagos", "name": "Pacific/Galapagos"}, {"value": "Europe/Tallinn", "name": "Europe/Tallinn"}, {"value": "Africa/Cairo", "name": "Africa/Cairo"}, {"value": "Africa/El_Aaiun", "name": "Africa/El_Aaiun"}, {"value": "Africa/Asmara", "name": "Africa/Asmara"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Africa/Ceuta", "name": "Africa/Ceuta"}, {"value": "Atlantic/Canary", "name": "Atlantic/Canary"}, {"value": "Europe/Madrid", "name": "Europe/Madrid"}, {"value": "Africa/Addis_Ababa", "name": "Africa/Addis_Ababa"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Europe/Helsinki", "name": "Europe/Helsinki"}, {"value": "Pacific/Fiji", "name": "Pacific/Fiji"}, {"value": "Atlantic/Stanley", "name": "Atlantic/Stanley"}, {"value": "Pacific/Chuuk", "name": "Pacific/Chuuk"}, {"value": "Pacific/Guadalcanal", "name": "Pacific/Guadalcanal"}, {"value": "Pacific/Kosrae", "name": "Pacific/Kosrae"}, {"value": "Pacific/Pohnpei", "name": "Pacific/Pohnpei"}, {"value": "Pacific/Port_Moresby", "name": "Pacific/Port_Moresby"}, {"value": "Atlantic/Faroe", "name": "Atlantic/Faroe"}, {"value": "Europe/Paris", "name": "Europe/Paris"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Libreville", "name": "Africa/Libreville"}, {"value": "Europe/London", "name": "Europe/London"}, {"value": "America/Grenada", "name": "America/Grenada"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Asia/Tbilisi", "name": "Asia/Tbilisi"}, {"value": "America/Cayenne", "name": "America/Cayenne"}, {"value": "Europe/Guernsey", "name": "Europe/Guernsey"}, {"value": "Europe/London", "name": "Europe/London"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Accra", "name": "Africa/Accra"}, {"value": "Europe/Gibraltar", "name": "Europe/Gibraltar"}, {"value": "America/Danmarkshavn", "name": "America/Danmarkshavn"}, {"value": "America/Nuuk", "name": "America/Nuuk"}, {"value": "America/Scoresbysund", "name": "America/Scoresbysund"}, {"value": "America/Thule", "name": "America/Thule"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Banjul", "name": "Africa/Banjul"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Conakry", "name": "Africa/Conakry"}, {"value": "America/Guadeloupe", "name": "America/Guadeloupe"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Malabo", "name": "Africa/Malabo"}, {"value": "Europe/Athens", "name": "Europe/Athens"}, {"value": "Atlantic/South_Georgia", "name": "Atlantic/South_Georgia"}, {"value": "America/Guatemala", "name": "America/Guatemala"}, {"value": "Pacific/Guam", "name": "Pacific/Guam"}, {"value": "Africa/Bissau", "name": "Africa/Bissau"}, {"value": "America/Guyana", "name": "America/Guyana"}, {"value": "Asia/Hong_Kong", "name": "Asia/Hong_Kong"}, {"value": "America/Tegucigalpa", "name": "America/Tegucigalpa"}, {"value": "Europe/Belgrade", "name": "Europe/Belgrade"}, {"value": "Europe/Zagreb", "name": "Europe/Zagreb"}, {"value": "America/Port-au-Prince", "name": "America/Port-au-Prince"}, {"value": "Europe/Budapest", "name": "Europe/Budapest"}, {"value": "Asia/Jakarta", "name": "Asia/Jakarta"}, {"value": "Asia/Jayapura", "name": "Asia/Jayapura"}, {"value": "Asia/Makassar", "name": "Asia/Makassar"}, {"value": "Asia/Pontianak", "name": "Asia/Pontianak"}, {"value": "Europe/Dublin", "name": "Europe/Dublin"}, {"value": "Asia/Jerusalem", "name": "Asia/Jerusalem"}, {"value": "Europe/Isle_of_Man", "name": "Europe/Isle_of_Man"}, {"value": "Europe/London", "name": "Europe/London"}, {"value": "Asia/Kolkata", "name": "Asia/Kolkata"}, {"value": "Indian/Chagos", "name": "Indian/Chagos"}, {"value": "Asia/Baghdad", "name": "Asia/Baghdad"}, {"value": "Asia/Tehran", "name": "Asia/Tehran"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Atlantic/Reykjavik", "name": "Atlantic/Reykjavik"}, {"value": "Europe/Rome", "name": "Europe/Rome"}, {"value": "Europe/Jersey", "name": "Europe/Jersey"}, {"value": "Europe/London", "name": "Europe/London"}, {"value": "America/Jamaica", "name": "America/Jamaica"}, {"value": "Asia/Amman", "name": "Asia/Amman"}, {"value": "Asia/Tokyo", "name": "Asia/Tokyo"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Asia/Bishkek", "name": "Asia/Bishkek"}, {"value": "Asia/Bangkok", "name": "Asia/Bangkok"}, {"value": "Asia/Phnom_Penh", "name": "Asia/Phnom_Penh"}, {"value": "Pacific/Kanton", "name": "Pacific/Kanton"}, {"value": "Pacific/Kiritimati", "name": "Pacific/Kiritimati"}, {"value": "Pacific/Tarawa", "name": "Pacific/Tarawa"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Indian/Comoro", "name": "Indian/Comoro"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/St_Kitts", "name": "America/St_Kitts"}, {"value": "Asia/Pyongyang", "name": "Asia/Pyongyang"}, {"value": "Asia/Seoul", "name": "Asia/Seoul"}, {"value": "Asia/Kuwait", "name": "Asia/Kuwait"}, {"value": "Asia/Riyadh", "name": "Asia/Riyadh"}, {"value": "America/Cayman", "name": "America/Cayman"}, {"value": "America/Panama", "name": "America/Panama"}, {"value": "Asia/Almaty", "name": "Asia/Almaty"}, {"value": "Asia/Aqtau", "name": "Asia/Aqtau"}, {"value": "Asia/Aqtobe", "name": "Asia/Aqtobe"}, {"value": "Asia/Atyrau", "name": "Asia/Atyrau"}, {"value": "Asia/Oral", "name": "Asia/Oral"}, {"value": "Asia/Qostanay", "name": "Asia/Qostanay"}, {"value": "Asia/Qyzylorda", "name": "Asia/Qyzylorda"}, {"value": "Asia/Bangkok", "name": "Asia/Bangkok"}, {"value": "Asia/Vientiane", "name": "Asia/Vientiane"}, {"value": "Asia/Beirut", "name": "Asia/Beirut"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/St_Lucia", "name": "America/St_Lucia"}, {"value": "Europe/Vaduz", "name": "Europe/Vaduz"}, {"value": "Europe/Zurich", "name": "Europe/Zurich"}, {"value": "Asia/Colombo", "name": "Asia/Colombo"}, {"value": "Africa/Monrovia", "name": "Africa/Monrovia"}, {"value": "Africa/Johannesburg", "name": "Africa/Johannesburg"}, {"value": "Africa/Maseru", "name": "Africa/Maseru"}, {"value": "Europe/Vilnius", "name": "Europe/Vilnius"}, {"value": "Europe/Brussels", "name": "Europe/Brussels"}, {"value": "Europe/Luxembourg", "name": "Europe/Luxembourg"}, {"value": "Europe/Riga", "name": "Europe/Riga"}, {"value": "Africa/Tripoli", "name": "Africa/Tripoli"}, {"value": "Africa/Casablanca", "name": "Africa/Casablanca"}, {"value": "Europe/Monaco", "name": "Europe/Monaco"}, {"value": "Europe/Paris", "name": "Europe/Paris"}, {"value": "Europe/Chisinau", "name": "Europe/Chisinau"}, {"value": "Europe/Belgrade", "name": "Europe/Belgrade"}, {"value": "Europe/Podgorica", "name": "Europe/Podgorica"}, {"value": "America/Marigot", "name": "America/Marigot"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Indian/Antananarivo", "name": "Indian/Antananarivo"}, {"value": "Pacific/Kwajalein", "name": "Pacific/Kwajalein"}, {"value": "Pacific/Majuro", "name": "Pacific/Majuro"}, {"value": "Pacific/Tarawa", "name": "Pacific/Tarawa"}, {"value": "Europe/Belgrade", "name": "Europe/Belgrade"}, {"value": "Europe/Skopje", "name": "Europe/Skopje"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Bamako", "name": "Africa/Bamako"}, {"value": "Asia/Yangon", "name": "Asia/Yangon"}, {"value": "Asia/Choibalsan", "name": "Asia/Choibalsan"}, {"value": "Asia/Hovd", "name": "Asia/Hovd"}, {"value": "Asia/Ulaanbaatar", "name": "Asia/Ulaanbaatar"}, {"value": "Asia/Macau", "name": "Asia/Macau"}, {"value": "Pacific/Guam", "name": "Pacific/Guam"}, {"value": "Pacific/Saipan", "name": "Pacific/Saipan"}, {"value": "America/Martinique", "name": "America/Martinique"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Nouakchott", "name": "Africa/Nouakchott"}, {"value": "America/Montserrat", "name": "America/Montserrat"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Europe/Malta", "name": "Europe/Malta"}, {"value": "Indian/Mauritius", "name": "Indian/Mauritius"}, {"value": "Indian/Maldives", "name": "Indian/Maldives"}, {"value": "Africa/Blantyre", "name": "Africa/Blantyre"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "America/Bahia_Banderas", "name": "America/Bahia_Banderas"}, {"value": "America/Cancun", "name": "America/Cancun"}, {"value": "America/Chihuahua", "name": "America/Chihuahua"}, {"value": "America/Hermosillo", "name": "America/Hermosillo"}, {"value": "America/Matamoros", "name": "America/Matamoros"}, {"value": "America/Mazatlan", "name": "America/Mazatlan"}, {"value": "America/Merida", "name": "America/Merida"}, {"value": "America/Mexico_City", "name": "America/Mexico_City"}, {"value": "America/Monterrey", "name": "America/Monterrey"}, {"value": "America/Ojinaga", "name": "America/Ojinaga"}, {"value": "America/Tijuana", "name": "America/Tijuana"}, {"value": "Asia/Kuala_Lumpur", "name": "Asia/Kuala_Lumpur"}, {"value": "Asia/Kuching", "name": "Asia/Kuching"}, {"value": "Asia/Singapore", "name": "Asia/Singapore"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "Africa/Windhoek", "name": "Africa/Windhoek"}, {"value": "Pacific/Noumea", "name": "Pacific/Noumea"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "Africa/Niamey", "name": "Africa/Niamey"}, {"value": "Pacific/Norfolk", "name": "Pacific/Norfolk"}, {"value": "Africa/Lagos", "name": "Africa/Lagos"}, {"value": "America/Managua", "name": "America/Managua"}, {"value": "Europe/Amsterdam", "name": "Europe/Amsterdam"}, {"value": "Europe/Brussels", "name": "Europe/Brussels"}, {"value": "Europe/Berlin", "name": "Europe/Berlin"}, {"value": "Europe/Oslo", "name": "Europe/Oslo"}, {"value": "Asia/Kathmandu", "name": "Asia/Kathmandu"}, {"value": "Pacific/Nauru", "name": "Pacific/Nauru"}, {"value": "Pacific/Niue", "name": "Pacific/Niue"}, {"value": "Pacific/Auckland", "name": "Pacific/Auckland"}, {"value": "Pacific/Chatham", "name": "Pacific/Chatham"}, {"value": "Asia/Dubai", "name": "Asia/Dubai"}, {"value": "Asia/Muscat", "name": "Asia/Muscat"}, {"value": "America/Panama", "name": "America/Panama"}, {"value": "America/Lima", "name": "America/Lima"}, {"value": "Pacific/Gambier", "name": "Pacific/Gambier"}, {"value": "Pacific/Marquesas", "name": "Pacific/Marquesas"}, {"value": "Pacific/Tahiti", "name": "Pacific/Tahiti"}, {"value": "Pacific/Bougainville", "name": "Pacific/Bougainville"}, {"value": "Pacific/Port_Moresby", "name": "Pacific/Port_Moresby"}, {"value": "Asia/Manila", "name": "Asia/Manila"}, {"value": "Asia/Karachi", "name": "Asia/Karachi"}, {"value": "Europe/Warsaw", "name": "Europe/Warsaw"}, {"value": "America/Miquelon", "name": "America/Miquelon"}, {"value": "Pacific/Pitcairn", "name": "Pacific/Pitcairn"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Asia/Gaza", "name": "Asia/Gaza"}, {"value": "Asia/Hebron", "name": "Asia/Hebron"}, {"value": "Atlantic/Azores", "name": "Atlantic/Azores"}, {"value": "Atlantic/Madeira", "name": "Atlantic/Madeira"}, {"value": "Europe/Lisbon", "name": "Europe/Lisbon"}, {"value": "Pacific/Palau", "name": "Pacific/Palau"}, {"value": "America/Asuncion", "name": "America/Asuncion"}, {"value": "Asia/Qatar", "name": "Asia/Qatar"}, {"value": "Asia/Dubai", "name": "Asia/Dubai"}, {"value": "Indian/Reunion", "name": "Indian/Reunion"}, {"value": "Europe/Bucharest", "name": "Europe/Bucharest"}, {"value": "Europe/Belgrade", "name": "Europe/Belgrade"}, {"value": "Asia/Anadyr", "name": "Asia/Anadyr"}, {"value": "Asia/Barnaul", "name": "Asia/Barnaul"}, {"value": "Asia/Chita", "name": "Asia/Chita"}, {"value": "Asia/Irkutsk", "name": "Asia/Irkutsk"}, {"value": "Asia/Kamchatka", "name": "Asia/Kamchatka"}, {"value": "Asia/Khandyga", "name": "Asia/Khandyga"}, {"value": "Asia/Krasnoyarsk", "name": "Asia/Krasnoyarsk"}, {"value": "Asia/Magadan", "name": "Asia/Magadan"}, {"value": "Asia/Novokuznetsk", "name": "Asia/Novokuznetsk"}, {"value": "Asia/Novosibirsk", "name": "Asia/Novosibirsk"}, {"value": "Asia/Omsk", "name": "Asia/Omsk"}, {"value": "Asia/Sakhalin", "name": "Asia/Sakhalin"}, {"value": "Asia/Srednekolymsk", "name": "Asia/Srednekolymsk"}, {"value": "Asia/Tomsk", "name": "Asia/Tomsk"}, {"value": "Asia/Ust-Nera", "name": "Asia/Ust-Nera"}, {"value": "Asia/Vladivostok", "name": "Asia/Vladivostok"}, {"value": "Asia/Yakutsk", "name": "Asia/Yakutsk"}, {"value": "Asia/Yekaterinburg", "name": "Asia/Yekaterinburg"}, {"value": "Europe/Astrakhan", "name": "Europe/Astrakhan"}, {"value": "Europe/Kaliningrad", "name": "Europe/Kaliningrad"}, {"value": "Europe/Kirov", "name": "Europe/Kirov"}, {"value": "Europe/Moscow", "name": "Europe/Moscow"}, {"value": "Europe/Samara", "name": "Europe/Samara"}, {"value": "Europe/Saratov", "name": "Europe/Saratov"}, {"value": "Europe/Simferopol", "name": "Europe/Simferopol"}, {"value": "Europe/Ulyanovsk", "name": "Europe/Ulyanovsk"}, {"value": "Europe/Volgograd", "name": "Europe/Volgograd"}, {"value": "Africa/Kigali", "name": "Africa/Kigali"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "Asia/Riyadh", "name": "Asia/Riyadh"}, {"value": "Pacific/Guadalcanal", "name": "Pacific/Guadalcanal"}, {"value": "Asia/Dubai", "name": "Asia/Dubai"}, {"value": "Indian/Mahe", "name": "Indian/Mahe"}, {"value": "Africa/Khartoum", "name": "Africa/Khartoum"}, {"value": "Europe/Berlin", "name": "Europe/Berlin"}, {"value": "Europe/Stockholm", "name": "Europe/Stockholm"}, {"value": "Asia/Singapore", "name": "Asia/Singapore"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Atlantic/St_Helena", "name": "Atlantic/St_Helena"}, {"value": "Europe/Belgrade", "name": "Europe/Belgrade"}, {"value": "Europe/Ljubljana", "name": "Europe/Ljubljana"}, {"value": "Arctic/Longyearbyen", "name": "Arctic/Longyearbyen"}, {"value": "Europe/Berlin", "name": "Europe/Berlin"}, {"value": "Europe/Bratislava", "name": "Europe/Bratislava"}, {"value": "Europe/Prague", "name": "Europe/Prague"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Freetown", "name": "Africa/Freetown"}, {"value": "Europe/Rome", "name": "Europe/Rome"}, {"value": "Europe/San_Marino", "name": "Europe/San_Marino"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Dakar", "name": "Africa/Dakar"}, {"value": "Africa/Mogadishu", "name": "Africa/Mogadishu"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "America/Paramaribo", "name": "America/Paramaribo"}, {"value": "Africa/Juba", "name": "Africa/Juba"}, {"value": "Africa/Sao_Tome", "name": "Africa/Sao_Tome"}, {"value": "America/El_Salvador", "name": "America/El_Salvador"}, {"value": "America/Lower_Princes", "name": "America/Lower_Princes"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Asia/Damascus", "name": "Asia/Damascus"}, {"value": "Africa/Johannesburg", "name": "Africa/Johannesburg"}, {"value": "Africa/Mbabane", "name": "Africa/Mbabane"}, {"value": "America/Grand_Turk", "name": "America/Grand_Turk"}, {"value": "Africa/Ndjamena", "name": "Africa/Ndjamena"}, {"value": "Asia/Dubai", "name": "Asia/Dubai"}, {"value": "Indian/Kerguelen", "name": "Indian/Kerguelen"}, {"value": "Indian/Maldives", "name": "Indian/Maldives"}, {"value": "Africa/Abidjan", "name": "Africa/Abidjan"}, {"value": "Africa/Lome", "name": "Africa/Lome"}, {"value": "Asia/Bangkok", "name": "Asia/Bangkok"}, {"value": "Asia/Dushanbe", "name": "Asia/Dushanbe"}, {"value": "Pacific/Fakaofo", "name": "Pacific/Fakaofo"}, {"value": "Asia/Dili", "name": "Asia/Dili"}, {"value": "Asia/Ashgabat", "name": "Asia/Ashgabat"}, {"value": "Africa/Tunis", "name": "Africa/Tunis"}, {"value": "Pacific/Tongatapu", "name": "Pacific/Tongatapu"}, {"value": "Europe/Istanbul", "name": "Europe/Istanbul"}, {"value": "America/Port_of_Spain", "name": "America/Port_of_Spain"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "Pacific/Funafuti", "name": "Pacific/Funafuti"}, {"value": "Pacific/Tarawa", "name": "Pacific/Tarawa"}, {"value": "Asia/Taipei", "name": "Asia/Taipei"}, {"value": "Africa/Dar_es_Salaam", "name": "Africa/Dar_es_Salaam"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Europe/Kyiv", "name": "Europe/Kyiv"}, {"value": "Europe/Simferopol", "name": "Europe/Simferopol"}, {"value": "Europe/Uzhgorod", "name": "Europe/Uzhgorod"}, {"value": "Europe/Zaporozhye", "name": "Europe/Zaporozhye"}, {"value": "Africa/Kampala", "name": "Africa/Kampala"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Pacific/Honolulu", "name": "Pacific/Honolulu"}, {"value": "Pacific/Midway", "name": "Pacific/Midway"}, {"value": "Pacific/Pago_Pago", "name": "Pacific/Pago_Pago"}, {"value": "Pacific/Tarawa", "name": "Pacific/Tarawa"}, {"value": "Pacific/Wake", "name": "Pacific/Wake"}, {"value": "America/Adak", "name": "America/Adak"}, {"value": "America/Anchorage", "name": "America/Anchorage"}, {"value": "America/Boise", "name": "America/Boise"}, {"value": "America/Chicago", "name": "America/Chicago"}, {"value": "America/Denver", "name": "America/Denver"}, {"value": "America/Detroit", "name": "America/Detroit"}, {"value": "America/Indiana/Indianapolis", "name": "America/Indiana/Indianapolis"}, {"value": "America/Indiana/Knox", "name": "America/Indiana/Knox"}, {"value": "America/Indiana/Marengo", "name": "America/Indiana/Marengo"}, {"value": "America/Indiana/Petersburg", "name": "America/Indiana/Petersburg"}, {"value": "America/Indiana/Tell_City", "name": "America/Indiana/Tell_City"}, {"value": "America/Indiana/Vevay", "name": "America/Indiana/Vevay"}, {"value": "America/Indiana/Vincennes", "name": "America/Indiana/Vincennes"}, {"value": "America/Indiana/Winamac", "name": "America/Indiana/Winamac"}, {"value": "America/Juneau", "name": "America/Juneau"}, {"value": "America/Kentucky/Louisville", "name": "America/Kentucky/Louisville"}, {"value": "America/Kentucky/Monticello", "name": "America/Kentucky/Monticello"}, {"value": "America/Los_Angeles", "name": "America/Los_Angeles"}, {"value": "America/Menominee", "name": "America/Menominee"}, {"value": "America/Metlakatla", "name": "America/Metlakatla"}, {"value": "America/New_York", "name": "America/New_York"}, {"value": "America/Nome", "name": "America/Nome"}, {"value": "America/North_Dakota/Beulah", "name": "America/North_Dakota/Beulah"}, {"value": "America/North_Dakota/Center", "name": "America/North_Dakota/Center"}, {"value": "America/North_Dakota/New_Salem", "name": "America/North_Dakota/New_Salem"}, {"value": "America/Phoenix", "name": "America/Phoenix"}, {"value": "America/Sitka", "name": "America/Sitka"}, {"value": "America/Yakutat", "name": "America/Yakutat"}, {"value": "Pacific/Honolulu", "name": "Pacific/Honolulu"}, {"value": "America/Montevideo", "name": "America/Montevideo"}, {"value": "Asia/Samarkand", "name": "Asia/Samarkand"}, {"value": "Asia/Tashkent", "name": "Asia/Tashkent"}, {"value": "Europe/Rome", "name": "Europe/Rome"}, {"value": "Europe/Vatican", "name": "Europe/Vatican"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/St_Vincent", "name": "America/St_Vincent"}, {"value": "America/Caracas", "name": "America/Caracas"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/Tortola", "name": "America/Tortola"}, {"value": "America/Puerto_Rico", "name": "America/Puerto_Rico"}, {"value": "America/St_Thomas", "name": "America/St_Thomas"}, {"value": "Asia/Bangkok", "name": "Asia/Bangkok"}, {"value": "Asia/Ho_Chi_Minh", "name": "Asia/Ho_Chi_Minh"}, {"value": "Pacific/Efate", "name": "Pacific/Efate"}, {"value": "Pacific/Tarawa", "name": "Pacific/Tarawa"}, {"value": "Pacific/Wallis", "name": "Pacific/Wallis"}, {"value": "Pacific/Apia", "name": "Pacific/Apia"}, {"value": "Asia/Aden", "name": "Asia/Aden"}, {"value": "Asia/Riyadh", "name": "Asia/Riyadh"}, {"value": "Africa/Nairobi", "name": "Africa/Nairobi"}, {"value": "Indian/Mayotte", "name": "Indian/Mayotte"}, {"value": "Africa/Johannesburg", "name": "Africa/Johannesburg"}, {"value": "Africa/Lusaka", "name": "Africa/Lusaka"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}, {"value": "Africa/Harare", "name": "Africa/Harare"}, {"value": "Africa/Maputo", "name": "Africa/Maputo"}]}], "test": {"request": {"baseURL": "={{$credentials?.domain || \"https://cloud.seatable.io\" }}", "url": "/api/v2.1/dtable/app-access-token/", "headers": {"Authorization": "={{\"Token \" + $credentials.token}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/SeaTable/seaTable.svg", "supportedNodes": ["n8n-nodes-base.seaTable", "n8n-nodes-base.seaTableTrigger"]}, {"name": "securityScorecardApi", "displayName": "SecurityScorecard API", "documentationUrl": "securityScorecard", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SecurityScorecard/securityScorecard.svg", "supportedNodes": ["n8n-nodes-base.securityScorecard"]}, {"name": "segmentApi", "displayName": "Segment API", "documentationUrl": "segment", "properties": [{"displayName": "Write Key", "name": "writekey", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Segment/segment.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.segment"]}, {"name": "sekoiaApi", "displayName": "Sekoia API", "documentationUrl": "sekoia", "httpRequestNode": {"name": "Sekoia", "docsUrl": "https://docs.sekoia.io/cti/features/integrations/api/", "apiBaseUrl": "https://api.sekoia.io/"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Sekoia.svg", "supportedNodes": []}, {"name": "sendGridApi", "displayName": "SendGrid API", "documentationUrl": "sendgrid", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.sendgrid.com/v3", "url": "/marketing/contacts"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/SendGrid/sendGrid.svg", "supportedNodes": ["n8n-nodes-base.sendGrid"]}, {"name": "sendInBlueApi", "displayName": "Brevo", "documentationUrl": "brevo", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"api-key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.brevo.com/v3", "url": "/account"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Brevo/brevo.svg", "supportedNodes": ["n8n-nodes-base.sendInBlue", "n8n-nodes-base.sendInBlueTrigger"]}, {"name": "sendyApi", "displayName": "Sendy API", "documentationUrl": "sendy", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://yourdomain.com"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Sendy/sendy.png", "supportedNodes": ["n8n-nodes-base.sendy"]}, {"name": "sentryIoApi", "displayName": "Sentry.io API", "documentationUrl": "sentryIo", "properties": [{"displayName": "Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.svg", "dark": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.dark.svg"}, "supportedNodes": ["n8n-nodes-base.sentryIo"]}, {"name": "sentryIoOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Sentry.io OAuth2 API", "documentationUrl": "sentryIo", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://sentry.io/oauth/authorize/", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://sentry.io/oauth/token/", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "event:admin event:read org:read project:read project:releases team:read event:write org:admin project:write team:write project:admin team:admin"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.svg", "dark": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.dark.svg"}, "supportedNodes": ["n8n-nodes-base.sentryIo"]}, {"name": "sentryIoServerApi", "displayName": "Sentry.io Server API", "documentationUrl": "sentryIo", "properties": [{"displayName": "Token", "name": "token", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://example.com"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.svg", "dark": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.dark.svg"}, "supportedNodes": ["n8n-nodes-base.sentryIo"]}, {"name": "serviceNowOAuth2Api", "extends": ["oAuth2Api"], "displayName": "ServiceNow OAuth2 API", "documentationUrl": "serviceNow", "properties": [{"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": "", "hint": "The subdomain can be extracted from the URL. If the URL is: https://dev99890.service-now.com the subdomain is dev99890", "required": true}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "=https://{{$self[\"subdomain\"]}}.service-now.com/oauth_auth.do", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "=https://{{$self[\"subdomain\"]}}.service-now.com/oauth_token.do", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "useraccount"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "response_type=code"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "grant_type=authorization_code"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ServiceNow/servicenow.svg", "supportedNodes": ["n8n-nodes-base.serviceNow"]}, {"name": "serviceNowBasicApi", "extends": ["httpBasicAuth"], "displayName": "ServiceNow Basic Auth API", "documentationUrl": "serviceNow", "properties": [{"displayName": "User", "name": "user", "type": "string", "required": true, "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "required": true, "typeOptions": {"password": true}, "default": ""}, {"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": "", "hint": "The subdomain can be extracted from the URL. If the URL is: https://dev99890.service-now.com the subdomain is dev99890", "required": true}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.user}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "=https://{{$credentials?.subdomain}}.service-now.com", "url": "/api/now/table/sys_user_role"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/ServiceNow/servicenow.svg", "supportedNodes": ["n8n-nodes-base.serviceNow"]}, {"name": "sftp", "displayName": "SFTP", "documentationUrl": "ftp", "properties": [{"displayName": "Host", "name": "host", "required": true, "type": "string", "default": ""}, {"displayName": "Port", "name": "port", "required": true, "type": "number", "default": 22}, {"displayName": "Username", "name": "username", "required": true, "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "String that contains a private key for either key-based or hostbased user authentication (OpenSSH format)"}, {"displayName": "Passphrase", "name": "passphrase", "typeOptions": {"password": true}, "type": "string", "default": "", "description": "For an encrypted private key, this is the passphrase used to decrypt it"}], "icon": "fa:server", "iconColor": "dark-blue", "supportedNodes": ["n8n-nodes-base.ftp"]}, {"name": "shopifyApi", "displayName": "Shopify API", "documentationUrl": "shopify", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Password", "name": "password", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Shop Subdomain", "name": "shopSubdomain", "required": true, "type": "string", "default": "", "description": "Only the subdomain without .myshopify.com"}, {"displayName": "Shared Secret", "name": "sharedSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "=https://{{$credentials.shopSubdomain}}.myshopify.com/admin/api/2024-07", "url": "/products.json"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Shopify/shopify.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.shopify", "n8n-nodes-base.shopifyTrigger"]}, {"name": "shopifyAccessTokenApi", "displayName": "Shopify Access Token API", "documentationUrl": "shopify", "properties": [{"displayName": "Shop Subdomain", "name": "shopSubdomain", "required": true, "type": "string", "default": "", "description": "Only the subdomain without .myshopify.com"}, {"displayName": "Access Token", "name": "accessToken", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "APP Secret Key", "name": "appSecretKey", "required": true, "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Secret key needed to verify the webhook when using Shopify Trigger node"}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Shopify-Access-Token": "={{$credentials?.accessToken}}"}}}, "test": {"request": {"baseURL": "=https://{{$credentials?.shopSubdomain}}.myshopify.com/admin/api/2024-07", "url": "/products.json"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Shopify/shopify.svg", "supportedNodes": ["n8n-nodes-base.shopify", "n8n-nodes-base.shopifyTrigger"]}, {"name": "shopifyOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Shopify OAuth2 API", "documentationUrl": "shopify", "properties": [{"displayName": "Shop Subdomain", "name": "shopSubdomain", "required": true, "type": "string", "default": "", "description": "Only the subdomain without .myshopify.com"}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true, "hint": "Be aware that Shopify refers to the Client ID as API Key"}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true, "hint": "Be aware that Shopify refers to the Client Secret as API Secret Key"}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "=https://{{$self[\"shopSubdomain\"]}}.myshopify.com/admin/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "=https://{{$self[\"shopSubdomain\"]}}.myshopify.com/admin/oauth/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "write_orders read_orders write_products read_products"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "access_mode=value"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Shopify/shopify.svg", "supportedNodes": ["n8n-nodes-base.shopify", "n8n-nodes-base.shopifyTrigger"]}, {"name": "signl4Api", "displayName": "SIGNL4 Webhook", "documentationUrl": "signl4", "properties": [{"displayName": "Team Secret", "name": "teamSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The team secret is the last part of your SIGNL4 webhook URL"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Signl4/signl4.png", "supportedNodes": ["n8n-nodes-base.signl4"]}, {"name": "slackApi", "displayName": "Slack API", "documentationUrl": "slack", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://slack.com", "url": "/api/users.profile.get"}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "error", "value": "invalid_auth", "message": "Invalid access token"}}]}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Slack/slack.svg", "supportedNodes": ["n8n-nodes-base.slack", "n8n-nodes-base.slackTrigger"]}, {"name": "slackOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Slack OAuth2 API", "documentationUrl": "slack", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://slack.com/oauth/v2/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://slack.com/api/oauth.v2.access"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "chat:write"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "user_scope=channels:read channels:write chat:write files:read files:write groups:read im:read mpim:read reactions:read reactions:write stars:read stars:write usergroups:write usergroups:read users.profile:read users.profile:write users:read"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}, {"displayName": "If you get an Invalid Scopes error, make sure you add the correct one <a target=\"_blank\" href=\"https://docs.n8n.io/integrations/builtin/credentials/slack/#using-oauth\">here</a> to your Slack integration", "name": "notice", "type": "notice", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Slack/slack.svg", "supportedNodes": ["n8n-nodes-base.slack"]}, {"name": "sms77Api", "displayName": "seven API", "documentationUrl": "sms77", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"X-Api-Key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://gateway.seven.io/api", "url": "/hooks", "qs": {"action": "read"}}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "success", "message": "Invalid API Key"}}]}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Sms77/seven.svg", "supportedNodes": ["n8n-nodes-base.sms77"]}, {"name": "smtp", "displayName": "SMTP", "documentationUrl": "sendemail", "properties": [{"displayName": "User", "name": "user", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Host", "name": "host", "type": "string", "default": ""}, {"displayName": "Port", "name": "port", "type": "number", "default": 465}, {"displayName": "SSL/TLS", "name": "secure", "type": "boolean", "default": true}, {"displayName": "Disable STARTTLS", "name": "disableStartTls", "type": "boolean", "default": false, "displayOptions": {"show": {"secure": [false]}}}, {"displayName": "Client Host Name", "name": "hostName", "type": "string", "default": "", "placeholder": "", "description": "The hostname of the client, used for identifying to the server"}], "icon": "fa:envelope", "supportedNodes": ["n8n-nodes-base.emailSend"]}, {"name": "snowflake", "displayName": "Snowflake", "documentationUrl": "snowflake", "properties": [{"displayName": "Account", "name": "account", "type": "string", "default": "", "description": "Enter the name of your Snowflake account"}, {"displayName": "Database", "name": "database", "type": "string", "default": "", "description": "Specify the database you want to use after creating the connection"}, {"displayName": "Warehouse", "name": "warehouse", "type": "string", "default": "", "description": "The default virtual warehouse to use for the session after connecting. Used for performing queries, loading data, etc."}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "<PERSON><PERSON><PERSON>", "name": "schema", "type": "string", "default": "", "description": "Enter the schema you want to use after creating the connection"}, {"displayName": "Role", "name": "role", "type": "string", "default": "", "description": "Enter the security role you want to use after creating the connection"}, {"displayName": "Client Session Keep Alive", "name": "clientSessionKeepAlive", "type": "boolean", "default": false, "description": "Whether to keep alive the client session. By default, client connections typically time out approximately 3-4 hours after the most recent query was executed. If the parameter clientSessionKeepAlive is set to true, the client’s connection to the server will be kept alive indefinitely, even if no queries are executed."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Snowflake/snowflake.svg", "supportedNodes": ["n8n-nodes-base.snowflake"]}, {"name": "solarWindsIpamApi", "displayName": "SolarWinds IPAM", "documentationUrl": "solarwindsipam", "httpRequestNode": {"name": "SolarWinds IPAM", "docsUrl": "https://www.solarwinds.com/ip-address-manager", "apiBaseUrlPlaceholder": "https://your-ipam-server"}, "properties": [{"displayName": "Base URL", "name": "url", "required": true, "type": "string", "default": "", "placeholder": "https://your-ipam-server", "description": "The base URL of your SolarWinds IPAM server."}, {"displayName": "Username", "name": "username", "required": true, "type": "string", "default": "", "description": "The username for SolarWinds IPAM API."}, {"displayName": "Password", "name": "password", "required": true, "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The password for SolarWinds IPAM API."}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/SolarWinds/InformationService/v3/Json/Query", "method": "GET", "qs": {"query": "SELECT TOP 1 AccountID FROM IPAM.AccountRoles"}, "skipSslCertificateValidation": true}, "rules": [{"type": "responseCode", "properties": {"value": 403, "message": "Connection failed: Invalid credentials or unreachable server"}}]}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsIpam.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsIpam.svg"}, "supportedNodes": []}, {"name": "solarWindsObservabilityApi", "displayName": "SolarWinds Observability", "documentationUrl": "solarwindsobservability", "httpRequestNode": {"name": "SolarWinds Observability", "docsUrl": "https://documentation.solarwinds.com/en/success_center/observability/content/api/api-swagger.htm", "apiBaseUrlPlaceholder": "https://api.xx-yy.cloud.solarwinds.com/"}, "properties": [{"displayName": "URL", "name": "url", "required": true, "type": "string", "default": ""}, {"displayName": "API Token", "name": "apiToken", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiToken}}", "Content-Type": "application/json-rpc"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/v1/logs", "method": "GET"}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "error", "value": "invalid_auth", "message": "Invalid access token"}}]}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsObservability.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsObservability.svg"}, "supportedNodes": []}, {"name": "splunkApi", "displayName": "Splunk API", "documentationUrl": "splunk", "properties": [{"displayName": "<PERSON><PERSON>", "name": "authToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "description": "Protocol, domain and port", "placeholder": "e.g. https://localhost:8089", "default": ""}, {"displayName": "Allow Self-Signed Certificates", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials?.authToken}}"}}}, "test": {"request": {"url": "={{$credentials.baseUrl}}/services/alerts/fired_alerts", "method": "GET", "skipSslCertificateValidation": "={{$credentials?.allowUnauthorizedCerts}}"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Splunk/splunk.svg", "supportedNodes": ["n8n-nodes-base.splunk"]}, {"name": "spontitApi", "displayName": "Spontit API", "documentationUrl": "spontit", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Spontit/spontit.png", "supportedNodes": ["n8n-nodes-base.spontit"]}, {"name": "spotifyOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Spotify OAuth2 API", "documentationUrl": "spotify", "properties": [{"displayName": "Spotify Server", "name": "server", "type": "hidden", "default": "https://api.spotify.com/"}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://accounts.spotify.com/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://accounts.spotify.com/api/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "user-read-playback-state playlist-read-collaborative user-modify-playback-state playlist-modify-public user-read-currently-playing playlist-read-private user-read-recently-played playlist-modify-private user-library-read user-follow-read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Spotify/spotify.svg", "supportedNodes": ["n8n-nodes-base.spotify"]}, {"name": "shuffle<PERSON><PERSON><PERSON>", "displayName": "Shuffler API", "documentationUrl": "shuffler", "httpRequestNode": {"name": "<PERSON>ffle<PERSON>", "docsUrl": "https://shuffler.io/docs/API", "apiBaseUrl": "https://shuffler.io/api/v1/"}, "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://shuffler.io/api", "url": "/v1/users/getusers", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Shuffler.svg", "supportedNodes": []}, {"name": "sshPassword", "displayName": "SSH Password", "documentationUrl": "ssh", "properties": [{"displayName": "Host", "name": "host", "required": true, "type": "string", "default": "", "placeholder": "localhost"}, {"displayName": "Port", "name": "port", "required": true, "type": "number", "default": 22}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "icon": "fa:terminal", "iconColor": "black", "supportedNodes": ["n8n-nodes-base.ssh"]}, {"name": "sshPrivateKey", "displayName": "SSH Private Key", "documentationUrl": "ssh", "properties": [{"displayName": "Host", "name": "host", "required": true, "type": "string", "default": "", "placeholder": "localhost"}, {"displayName": "Port", "name": "port", "required": true, "type": "number", "default": 22}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Private Key", "name": "privateKey", "type": "string", "typeOptions": {"rows": 4, "password": true}, "default": ""}, {"displayName": "Passphrase", "name": "passphrase", "type": "string", "default": "", "description": "Passphase used to create the key, if no passphase was used leave empty", "typeOptions": {"password": true}}], "icon": "fa:terminal", "iconColor": "black", "supportedNodes": ["n8n-nodes-base.ssh"]}, {"name": "stackbyApi", "displayName": "Stackby API", "documentationUrl": "stackby", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Stackby/stackby.png", "supportedNodes": ["n8n-nodes-base.stackby"]}, {"name": "storyblokContentApi", "displayName": "Storyblok Content API", "documentationUrl": "storyblok", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Storyblok/storyblok.svg", "supportedNodes": ["n8n-nodes-base.storyblok"]}, {"name": "storyblokManagementApi", "displayName": "Storyblok Management API", "documentationUrl": "storyblok", "properties": [{"displayName": "Personal Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Storyblok/storyblok.svg", "supportedNodes": ["n8n-nodes-base.storyblok"]}, {"name": "strapi<PERSON><PERSON>", "displayName": "Strapi API", "documentationUrl": "strapi", "properties": [{"displayName": "Make sure you are using a user account not an admin account", "name": "notice", "type": "notice", "default": ""}, {"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://api.example.com"}, {"displayName": "API Version", "name": "apiVersion", "default": "v3", "type": "options", "description": "The version of api to be used", "options": [{"name": "Version 4", "value": "v4", "description": "API version supported by Strapi 4"}, {"name": "Version 3", "value": "v3", "description": "API version supported by Strapi 3"}]}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Strapi/strapi.svg", "supportedNodes": ["n8n-nodes-base.strapi"]}, {"name": "strapiTokenApi", "displayName": "Strapi API Token", "documentationUrl": "strapi", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://api.example.com"}, {"displayName": "API Version", "name": "apiVersion", "default": "v3", "type": "options", "description": "The version of api to be used", "options": [{"name": "Version 4", "value": "v4", "description": "API version supported by Strapi 4"}, {"name": "Version 3", "value": "v3", "description": "API version supported by Strapi 3"}]}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiToken}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "={{$credentials.apiVersion === \"v3\" ? \"/users/count\" : \"/api/users/count\"}}", "ignoreHttpStatusErrors": true}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "error.name", "value": "UnauthorizedError", "message": "Invalid API token"}}]}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Strapi/strapi.svg", "supportedNodes": ["n8n-nodes-base.strapi"]}, {"name": "stravaOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Strava OAuth2 API", "documentationUrl": "strava", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://www.strava.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://www.strava.com/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "activity:read_all,activity:write", "required": true}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Strava/strava.svg", "supportedNodes": ["n8n-nodes-base.strava", "n8n-nodes-base.stravaTrigger"]}, {"name": "stripeApi", "displayName": "Stripe API", "documentationUrl": "stripe", "properties": [{"displayName": "Secret Key", "name": "secret<PERSON>ey", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.secretKey}}"}}}, "test": {"request": {"baseURL": "https://api.stripe.com/v1", "url": "/charges", "json": true}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Stripe/stripe.svg", "supportedNodes": ["n8n-nodes-base.stripe", "n8n-nodes-base.stripeTrigger"]}, {"name": "supabaseApi", "displayName": "Supabase API", "documentationUrl": "supabase", "properties": [{"displayName": "Host", "name": "host", "type": "string", "placeholder": "https://your_account.supabase.co", "default": ""}, {"displayName": "Service Role Secret", "name": "serviceRole", "type": "string", "default": "", "typeOptions": {"password": true}}], "authenticate": {"type": "generic", "properties": {"headers": {"apikey": "={{$credentials.serviceRole}}", "Authorization": "=Bearer {{$credentials.serviceRole}}"}}}, "test": {"request": {"baseURL": "={{$credentials.host}}/rest/v1", "headers": {"Prefer": "return=representation"}, "url": "/"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Supabase/supabase.svg", "supportedNodes": ["n8n-nodes-base.supabase"]}, {"name": "surveyMonkeyApi", "displayName": "SurveyMonkey API", "documentationUrl": "surveyMonkey", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The access token must have the following scopes:\n\t\t\t<ul>\n\t\t\t\t<li>Create/modify webhooks</li>\n\t\t\t\t<li>View webhooks</li>\n\t\t\t\t<li>View surveys</li>\n\t\t\t\t<li>View collectors</li>\n\t\t\t\t<li>View responses</li>\n\t\t\t\t<li>View response details</li>\n\t\t\t</ul>"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": ""}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SurveyMonkey/surveyMonkey.svg", "supportedNodes": ["n8n-nodes-base.surveyMonkeyTrigger"]}, {"name": "surveyMonkeyOAuth2Api", "extends": ["oAuth2Api"], "displayName": "SurveyMonkey OAuth2 API", "documentationUrl": "surveyMonkey", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://api.surveymonkey.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.surveymonkey.com/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "surveys_read,collectors_read,responses_read,responses_read_detail,webhooks_write,webhooks_read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SurveyMonkey/surveyMonkey.svg", "supportedNodes": ["n8n-nodes-base.surveyMonkeyTrigger"]}, {"name": "syncroMspApi", "displayName": "SyncroMSP API", "documentationUrl": "syncromsp", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SyncroMSP/syncromsp.png", "supportedNodes": ["n8n-nodes-base.syncroMsp"]}, {"name": "sysdigApi", "displayName": "Sysdig API", "documentationUrl": "sysdig", "httpRequestNode": {"name": "Sysdig", "docsUrl": "https://docs.sysdig.com/en/docs/developer-tools/sysdig-api/", "apiBaseUrl": "https://app.us1.sysdig.com"}, "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Sysdig.Black.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Sysdig.White.svg"}, "supportedNodes": []}, {"name": "taigaApi", "displayName": "Taiga API", "documentationUrl": "taiga", "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Environment", "name": "environment", "type": "options", "default": "cloud", "options": [{"name": "Cloud", "value": "cloud"}, {"name": "Self-Hosted", "value": "selfHosted"}]}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://taiga.yourdomain.com", "displayOptions": {"show": {"environment": ["selfHosted"]}}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Taiga/taiga.svg", "supportedNodes": ["n8n-nodes-base.taiga", "n8n-nodes-base.taigaTrigger"]}, {"name": "tapfiliateApi", "displayName": "Tapfiliate API", "documentationUrl": "tapfiliate", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Tapfiliate/tapfiliate.svg", "supportedNodes": ["n8n-nodes-base.tapfiliate"]}, {"name": "telegramApi", "displayName": "Telegram API", "documentationUrl": "telegram", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Chat with the <a href=\"https://telegram.me/botfather\">bot father</a> to obtain the access token"}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "https://api.telegram.org", "description": "Base URL for Telegram Bot API"}], "test": {"request": {"baseURL": "={{$credentials.baseUrl}}/bot{{$credentials.accessToken}}", "url": "/getMe"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Telegram/telegram.svg", "supportedNodes": ["n8n-nodes-base.telegram", "n8n-nodes-base.telegramTrigger"]}, {"name": "theHiveProjectApi", "displayName": "The Hive 5 API", "documentationUrl": "theHive", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "default": "", "typeOptions": {"password": true}}, {"displayName": "URL", "name": "url", "default": "", "type": "string", "description": "The URL of TheHive instance", "placeholder": "https://localhost:9000"}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials?.<PERSON><PERSON><PERSON><PERSON>}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "/api/case"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/TheHiveProject/thehiveproject.svg", "supportedNodes": ["n8n-nodes-base.theHiveProject"]}, {"name": "theHiveApi", "displayName": "The Hive API", "documentationUrl": "theHive", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "URL", "name": "url", "default": "", "type": "string", "description": "The URL of TheHive instance", "placeholder": "https://localhost:9000"}, {"displayName": "API Version", "name": "apiVersion", "default": "", "type": "options", "description": "The version of api to be used", "options": [{"name": "TheHive 4+ (api v1)", "value": "v1", "description": "API version with TheHive 4 support, also works with TheHive 5 but not all features are supported"}, {"name": "TheHive 3 (api v0)", "value": "", "description": "API version with TheHive 3 support"}]}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials?.<PERSON><PERSON><PERSON><PERSON>}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "/api/case"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/TheHive/thehive.svg", "supportedNodes": ["n8n-nodes-base.theHive"]}, {"name": "timescaleDb", "displayName": "TimescaleDB", "documentationUrl": "timescaleDb", "properties": [{"displayName": "Host", "name": "host", "type": "string", "default": "localhost"}, {"displayName": "Database", "name": "database", "type": "string", "default": "postgres"}, {"displayName": "User", "name": "user", "type": "string", "default": "postgres"}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "default": false, "description": "Whether to connect even if SSL certificate validation is not possible"}, {"displayName": "SSL", "name": "ssl", "type": "options", "displayOptions": {"show": {"allowUnauthorizedCerts": [false]}}, "options": [{"name": "Allow", "value": "allow"}, {"name": "Disable", "value": "disable"}, {"name": "Require", "value": "require"}], "default": "disable"}, {"displayName": "Port", "name": "port", "type": "number", "default": 5432}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/TimescaleDb/timescaleDb.svg", "dark": "icons/n8n-nodes-base/dist/nodes/TimescaleDb/timescaleDb.dark.svg"}, "supportedNodes": ["n8n-nodes-base.timescaleDb"]}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "displayName": "Todoist API", "documentationUrl": "todoist", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.todoist.com/rest/v2", "url": "/labels"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Todoist/todoist.svg", "supportedNodes": ["n8n-nodes-base.todoist"]}, {"name": "todoistOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Todoist OAuth2 API", "documentationUrl": "todoist", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://todoist.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://todoist.com/oauth/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "data:read_write,data:delete"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Todoist/todoist.svg", "supportedNodes": ["n8n-nodes-base.todoist"]}, {"name": "toggl<PERSON>pi", "displayName": "Toggl API", "documentationUrl": "toggl", "properties": [{"displayName": "Email Address", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "https://api.track.toggl.com/api/v9", "url": "/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Toggl/toggl.png", "supportedNodes": ["n8n-nodes-base.togglTrigger"]}, {"name": "totpApi", "displayName": "TOTP API", "documentationUrl": "totp", "properties": [{"displayName": "Secret", "name": "secret", "type": "string", "typeOptions": {"password": true}, "default": "", "placeholder": "e.g. BVDRSBXQB2ZEL5HE", "required": true, "description": "Secret key encoded in the QR code during setup. <a href=\"https://github.com/google/google-authenticator/wiki/Key-Uri-Format#secret\">Learn more</a>."}, {"displayName": "Label", "name": "label", "type": "string", "default": "", "required": true, "placeholder": "e.g. GitHub:john-doe", "description": "Identifier for the TOTP account, in the <code>issuer:username</code> format. <a href=\"https://github.com/google/google-authenticator/wiki/Key-Uri-Format#label\">Learn more</a>."}], "icon": "fa:fingerprint", "supportedNodes": ["n8n-nodes-base.totp"]}, {"name": "travisCiApi", "displayName": "Travis API", "documentationUrl": "travisCi", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/TravisCi/travisci.png", "supportedNodes": ["n8n-nodes-base.travisCi"]}, {"name": "trellixEpoApi", "displayName": "Trellix (McAfee) ePolicy Orchestrator API", "documentationUrl": "trellixepo", "httpRequestNode": {"name": "Trellix (McAfee) ePolicy Orchestrator", "docsUrl": "https://docs.trellix.com/en/bundle/epolicy-orchestrator-web-api-reference-guide", "apiBaseUrl": ""}, "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Trellix.svg", "supportedNodes": []}, {"name": "trelloApi", "displayName": "Trello API", "documentationUrl": "trello", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "OAuth Secret", "name": "oauth<PERSON><PERSON>ret", "type": "hidden", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://api.trello.com", "url": "=/1/tokens/{{$credentials.apiToken}}/member"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Trello/trello.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.trello", "n8n-nodes-base.trelloTrigger"]}, {"name": "twakeCloudApi", "displayName": "Twake Cloud API", "documentationUrl": "twake", "properties": [{"displayName": "Workspace Key", "name": "workspaceKey", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.workspaceKey}}"}}}, "test": {"request": {"baseURL": "https://plugins.twake.app/plugins/n8n", "url": "/channel", "method": "POST"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Twake/twake.png", "supportedNodes": ["n8n-nodes-base.twake"]}, {"name": "twakeServerApi", "displayName": "Twake Server API", "documentationUrl": "twake", "httpRequestNode": {"name": "Twake Server", "docsUrl": "https://doc.twake.app/developers-api/home", "apiBaseUrl": ""}, "properties": [{"displayName": "Host URL", "name": "hostUrl", "type": "string", "default": ""}, {"displayName": "Public ID", "name": "publicId", "type": "string", "default": ""}, {"displayName": "Private API Key", "name": "privateApiKey", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Twake.png", "supportedNodes": []}, {"name": "t<PERSON><PERSON><PERSON><PERSON>", "displayName": "Twilio API", "documentationUrl": "twi<PERSON>", "properties": [{"displayName": "Auth Type", "name": "authType", "type": "options", "default": "authToken", "options": [{"name": "<PERSON><PERSON>", "value": "authToken"}, {"name": "API Key", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"displayName": "Account SID", "name": "accountSid", "type": "string", "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "authToken", "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"authType": ["authToken"]}}}, {"displayName": "API Key SID", "name": "apiKeySid", "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"authType": ["<PERSON><PERSON><PERSON><PERSON>"]}}}, {"displayName": "API Key Secret", "name": "apiKeySecret", "type": "string", "typeOptions": {"password": true}, "default": "", "displayOptions": {"show": {"authType": ["<PERSON><PERSON><PERSON><PERSON>"]}}}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{ $credentials.authType === \"apiKey\" ? $credentials.apiKeySid : $credentials.accountSid }}", "password": "={{ $credentials.authType === \"apiKey\" ? $credentials.apiKeySecret : $credentials.authToken }}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Twilio/twilio.svg", "supportedNodes": ["n8n-nodes-base.twilio", "n8n-nodes-base.twilioTrigger"]}, {"name": "twistOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Twist OAuth2 API", "documentationUrl": "twist", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://twist.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://twist.com/oauth/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "attachments:write,channels:remove,comments:remove,messages:remove,threads:remove,workspaces:read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Twist/twist.png", "supportedNodes": ["n8n-nodes-base.twist"]}, {"name": "twitterOAuth1Api", "extends": ["oAuth1Api"], "displayName": "X OAuth API", "documentationUrl": "twitter", "properties": [{"displayName": "Request Token URL", "name": "requestTokenUrl", "type": "hidden", "default": "https://api.twitter.com/oauth/request_token"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://api.twitter.com/oauth/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.twitter.com/oauth/access_token"}, {"displayName": "Signature Method", "name": "signatureMethod", "type": "hidden", "default": "HMAC-SHA1"}, {"displayName": "Some operations require a Basic or Pro API. Refer to <a href=\"https://developer.x.com/en/docs/twitter-api\" target=\"_blank\">X API Docs</a> for more information.", "name": "apiPermissions", "type": "notice", "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Twitter/x.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Twitter/x.dark.svg"}, "supportedNodes": ["n8n-nodes-base.twitter"]}, {"name": "twitterOAuth2Api", "extends": ["oAuth2Api"], "displayName": "X OAuth2 API", "documentationUrl": "twitter", "properties": [{"displayName": "Some operations require a Basic or Pro API. Refer to <a href=\"https://developer.x.com/en/docs/twitter-api\" target=\"_blank\">X API Docs</a> for more information.", "name": "apiPermissions", "type": "notice", "default": ""}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "pkce"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://twitter.com/i/oauth2/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.twitter.com/2/oauth2/token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "tweet.read users.read tweet.write tweet.moderate.write users.read follows.read follows.write offline.access like.read like.write dm.write dm.read list.read list.write"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Twitter/x.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Twitter/x.dark.svg"}, "supportedNodes": ["n8n-nodes-base.twitter"]}, {"name": "typeformApi", "displayName": "Typeform API", "documentationUrl": "typeform", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.typeform.com", "url": "/forms"}}, "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Typeform/typeform.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Typeform/typeform.dark.svg"}, "supportedNodes": ["n8n-nodes-base.typeformTrigger"]}, {"name": "typeformOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Typeform OAuth2 API", "documentationUrl": "typeform", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://api.typeform.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.typeform.com/oauth/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "webhooks:write webhooks:read forms:read"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Typeform/typeform.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Typeform/typeform.dark.svg"}, "supportedNodes": ["n8n-nodes-base.typeformTrigger"]}, {"name": "unleashedSoftwareApi", "displayName": "Unleashed API", "documentationUrl": "unleashedSoftware", "properties": [{"displayName": "API ID", "name": "apiId", "type": "string", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "default": "", "typeOptions": {"password": true}}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/UnleashedSoftware/unleashedSoftware.png", "supportedNodes": ["n8n-nodes-base.unleashedSoftware"]}, {"name": "upleadApi", "displayName": "Uplead API", "documentationUrl": "uplead", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Uplead/uplead.png", "supportedNodes": ["n8n-nodes-base.uplead"]}, {"name": "uprocApi", "displayName": "uProc API", "documentationUrl": "uProc", "properties": [{"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "test": {"request": {"baseURL": "https://api.uproc.io/api/v2", "url": "/profile", "method": "GET"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/UProc/uproc.png", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.uproc"]}, {"name": "uptimeRobotApi", "displayName": "Uptime Robot API", "documentationUrl": "uptimeRobot", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/UptimeRobot/uptimerobot.svg", "supportedNodes": ["n8n-nodes-base.uptimeRobot"]}, {"name": "urlScanIoApi", "displayName": "urlscan.io API", "documentationUrl": "urlScanIo", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"API-KEY": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://urlscan.io", "url": "/user/quotas"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/UrlScanIo/urlScanIo.svg", "supportedNodes": ["n8n-nodes-base.urlScanIo"]}, {"name": "veroApi", "displayName": "Vero API", "documentationUrl": "vero", "properties": [{"displayName": "<PERSON><PERSON>", "name": "authToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Vero/vero.svg", "supportedNodes": ["n8n-nodes-base.vero"]}, {"name": "vertica<PERSON>pi", "displayName": "Vertica API", "documentationUrl": "vertica", "httpRequestNode": {"name": "Vertica", "docsUrl": "vertica", "apiBaseUrlPlaceholder": "http://<server>:<port>/v1/"}, "properties": [{"displayName": "URL", "name": "url", "required": true, "type": "string", "default": "https://localhost:8443", "placeholder": "https://<server>:<port>"}, {"displayName": "Username", "name": "username", "type": "string", "default": "", "description": "The username for accessing the Vertica database."}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "The password for accessing the Vertica database."}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/v1/health", "method": "GET", "skipSslCertificateValidation": true}, "rules": [{"type": "responseCode", "properties": {"value": 403, "message": "Connection failed: Invalid credentials or insufficient permissions"}}, {"type": "responseCode", "properties": {"value": 503, "message": "Service unavailable: Server is overloaded or under maintenance"}}, {"type": "responseCode", "properties": {"value": 504, "message": "Gateway timeout: Upstream server took too long to respond"}}]}, "supportedNodes": []}, {"name": "virusTotalApi", "displayName": "VirusTotal API", "documentationUrl": "virustotal", "httpRequestNode": {"name": "VirusTotal", "docsUrl": "https://developers.virustotal.com/reference/overview", "apiBaseUrl": "https://www.virustotal.com/api/v3/"}, "properties": [{"displayName": "API Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"x-apikey": "={{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://www.virustotal.com/api/v3", "url": "/popular_threat_categories"}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/VirusTotal.svg", "supportedNodes": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Vonage API", "documentationUrl": "vonage", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "API Secret", "name": "apiSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Vonage/vonage.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Vonage/vonage.dark.svg"}, "supportedNodes": ["n8n-nodes-base.von<PERSON>"]}, {"name": "venafiTlsProtectCloudApi", "displayName": "Venafi TLS Protect Cloud", "documentationUrl": "venafitlsprotectcloud", "properties": [{"displayName": "Region", "name": "region", "type": "options", "options": [{"name": "US", "value": "cloud"}, {"name": "EU", "value": "eu"}], "default": "cloud"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"tppl-api-key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "=https://api.venafi.{{$credentials.region ?? \"cloud\"}}", "url": "/v1/preferences"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Venafi/venafi.svg", "supportedNodes": ["n8n-nodes-base.venafiTlsProtectCloud", "n8n-nodes-base.venafiTlsProtectCloudTrigger"]}, {"name": "venafiTlsProtectDatacenterApi", "displayName": "Venafi TLS Protect Datacenter API", "documentationUrl": "venafitlsprotectdatacenter", "properties": [{"displayName": "Domain", "name": "domain", "type": "string", "default": "", "placeholder": "https://example.com"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": ""}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Allow Self-Signed Certificates", "name": "allowUnauthorizedCerts", "type": "boolean", "default": true}, {"displayName": "Access Token", "name": "token", "type": "hidden", "typeOptions": {"expirable": true}, "default": ""}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "certificate:manage"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.token}}"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Venafi/venafi.svg", "supportedNodes": ["n8n-nodes-base.venafiTlsProtectDatacenter"]}, {"name": "webflowApi", "displayName": "Webflow API", "documentationUrl": "webflow", "properties": [{"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.webflow.com", "url": "/sites"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Webflow/webflow.svg", "supportedNodes": ["n8n-nodes-base.webflow", "n8n-nodes-base.webflowTrigger"]}, {"name": "webflowOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Webflow OAuth2 API", "documentationUrl": "webflow", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Legacy", "name": "legacy", "type": "boolean", "default": true, "description": "If the legacy API should be used"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://webflow.com/oauth/authorize", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://api.webflow.com/oauth/access_token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "={{$self[\"legacy\"] ? \"\" : \"cms:read cms:write sites:read forms:read\"}}"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "", "description": "For some services additional query parameters have to be set which can be defined here", "placeholder": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Webflow/webflow.svg", "supportedNodes": ["n8n-nodes-base.webflow", "n8n-nodes-base.webflowTrigger"]}, {"name": "wekan<PERSON><PERSON>", "displayName": "Wekan API", "documentationUrl": "wekan", "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://wekan.yourdomain.com"}, {"displayName": "Session Token", "name": "token", "type": "hidden", "typeOptions": {"expirable": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.token}}"}}}, "test": {"request": {"baseURL": "={{$credentials.url.replace(new RegExp(\"/$\"), \"\")}}", "url": "/api/user"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wekan/wekan.svg", "supportedNodes": ["n8n-nodes-base.wekan"]}, {"name": "whatsAppApi", "displayName": "WhatsApp API", "documentationUrl": "whatsApp", "properties": [{"displayName": "Access Token", "type": "string", "typeOptions": {"password": true}, "name": "accessToken", "default": "", "required": true}, {"displayName": "Business Account ID", "type": "string", "name": "businessAccountId", "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://graph.facebook.com/v13.0", "url": "/", "ignoreHttpStatusErrors": true}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "error.type", "value": "OAuthException", "message": "Invalid access token"}}]}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/WhatsApp/whatsapp.svg", "supportedNodes": ["n8n-nodes-base.whatsApp"]}, {"name": "whatsAppTriggerApi", "displayName": "WhatsApp OAuth API", "documentationUrl": "whatsApp", "properties": [{"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "test": {"request": {"method": "POST", "baseURL": "https://graph.facebook.com/v19.0/oauth/access_token", "body": {"client_id": "={{$credentials.clientId}}", "client_secret": "={{$credentials.clientSecret}}", "grant_type": "client_credentials"}}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/WhatsApp/whatsapp.svg", "supportedNodes": ["n8n-nodes-base.whatsAppTrigger"]}, {"name": "wise<PERSON><PERSON>", "displayName": "Wise API", "documentationUrl": "wise", "properties": [{"displayName": "API Token", "name": "apiToken", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Environment", "name": "environment", "type": "options", "default": "live", "options": [{"name": "Live", "value": "live"}, {"name": "Test", "value": "test"}]}, {"displayName": "Private Key (Optional)", "name": "privateKey", "type": "string", "typeOptions": {"password": true}, "default": "", "description": "Optional private key used for Strong Customer Authentication (SCA). Only needed to retrieve statements, and execute transfers."}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wise/wise.svg", "supportedNodes": ["n8n-nodes-base.wise", "n8n-nodes-base.wiseTrigger"]}, {"name": "wooCommerceApi", "displayName": "WooCommerce API", "documentationUrl": "wooCommerce", "properties": [{"displayName": "Consumer Key", "name": "consumerKey", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Consumer Secret", "name": "consumerSecret", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "WooCommerce URL", "name": "url", "type": "string", "default": "", "placeholder": "https://example.com"}, {"displayName": "Include Credentials in Query", "name": "includeCredentialsInQuery", "type": "boolean", "default": false, "description": "Whether credentials should be included in the query. Occasionally, some servers may not parse the Authorization header correctly (if you see a “Consumer key is missing” error when authenticating over SSL, you have a server issue). In this case, you may provide the consumer key/secret as query string parameters instead."}], "test": {"request": {"baseURL": "={{$credentials.url}}/wp-json/wc/v3", "url": "/products/categories"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/WooCommerce/wooCommerce.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.wooCommerce", "n8n-nodes-base.wooCommerceTrigger"]}, {"name": "wordpressApi", "displayName": "Wordpress API", "documentationUrl": "wordpress", "properties": [{"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Wordpress URL", "name": "url", "type": "string", "default": "", "placeholder": "https://example.com"}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.username}}", "password": "={{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}/wp-json/wp/v2", "url": "/users", "method": "GET", "skipSslCertificateValidation": "={{$credentials.allowUnauthorizedCerts}}"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wordpress/wordpress.svg", "supportedNodes": ["n8n-nodes-base.wordpress"]}, {"name": "workableApi", "displayName": "Workable API", "documentationUrl": "workable", "properties": [{"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": ""}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Workable/workable.png", "supportedNodes": ["n8n-nodes-base.workableTrigger"]}, {"name": "wufoo<PERSON><PERSON>", "displayName": "Wufoo API", "documentationUrl": "wufoo", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}, {"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": ""}], "authenticate": {"type": "generic", "properties": {"auth": {"username": "={{$credentials.apiKey}}", "password": "not-needed"}}}, "test": {"request": {"baseURL": "=https://{{$credentials.subdomain}}.wufoo.com", "url": "/api/v3/forms.json"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wufoo/wufoo.png", "supportedNodes": ["n8n-nodes-base.wufooTrigger"]}, {"name": "xeroOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Xero OAuth2 API", "documentationUrl": "xero", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://login.xero.com/identity/connect/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://identity.xero.com/connect/token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "offline_access accounting.transactions accounting.settings accounting.contacts"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Xero/xero.svg", "supportedNodes": ["n8n-nodes-base.xero"]}, {"name": "yourlsApi", "displayName": "Yourls API", "documentationUrl": "yourls", "properties": [{"displayName": "Signature", "name": "signature", "type": "string", "default": ""}, {"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "http://localhost:8080"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Yourls/yourls.png", "supportedNodes": ["n8n-nodes-base.yourls"]}, {"name": "youTubeOAuth2Api", "icon": "node:n8n-nodes-base.youTube", "extends": ["googleOAuth2Api"], "displayName": "YouTube OAuth2 API", "documentationUrl": "google", "properties": [{"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/youtubepartner https://www.googleapis.com/auth/youtube.force-ssl https://www.googleapis.com/auth/youtube.upload https://www.googleapis.com/auth/youtubepartner-channel-audit"}], "supportedNodes": ["n8n-nodes-base.youTube"]}, {"name": "zabbixApi", "displayName": "Zabbix API", "documentationUrl": "zabbix", "httpRequestNode": {"name": "Zabbix", "docsUrl": "https://www.zabbix.com/documentation/current/en/manual/api", "apiBaseUrl": ""}, "properties": [{"displayName": "URL", "name": "url", "required": true, "type": "string", "default": ""}, {"displayName": "API Token", "name": "apiToken", "required": true, "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiToken}}", "Content-Type": "application/json-rpc"}}}, "test": {"request": {"baseURL": "={{$credentials.url}}", "url": "/api_jsonrpc.php", "method": "POST", "body": {"jsonrpc": "2.0", "method": "host.get", "params": {"output": ["hostid", "host"], "selectInterfaces": ["interfaceid", "ip"]}, "id": 2}}, "rules": [{"type": "responseSuccessBody", "properties": {"key": "result", "message": "Invalid access token"}}]}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Zabbix.svg", "supportedNodes": []}, {"name": "zammadBasicAuthApi", "displayName": "Zammad Basic Auth API", "documentationUrl": "<PERSON>am<PERSON>", "properties": [{"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "https://n8n-helpdesk.zammad.com", "required": true}, {"displayName": "Email", "name": "username", "type": "string", "default": "", "placeholder": "<EMAIL>", "required": true}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zammad/zammad.svg", "supportedNodes": ["n8n-nodes-base.zammad"]}, {"name": "zammadTokenAuthApi", "displayName": "<PERSON><PERSON><PERSON>", "documentationUrl": "<PERSON>am<PERSON>", "properties": [{"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "https://n8n-helpdesk.zammad.com", "required": true}, {"displayName": "Access Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Ignore SSL Issues (Insecure)", "name": "allowUnauthorizedCerts", "type": "boolean", "description": "Whether to connect even if SSL certificate validation is not possible", "default": false}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zammad/zammad.svg", "supportedNodes": ["n8n-nodes-base.zammad"]}, {"name": "zendeskApi", "displayName": "Zendesk API", "documentationUrl": "zendesk", "properties": [{"displayName": "Subdomain", "name": "subdomain", "type": "string", "description": "The subdomain of your Zendesk work environment", "placeholder": "company", "default": ""}, {"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "API Token", "name": "apiToken", "type": "string", "default": "", "typeOptions": {"password": true}}], "test": {"request": {"baseURL": "=https://{{$credentials.subdomain}}.zendesk.com/api/v2", "url": "/ticket_fields.json"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zendesk/zendesk.svg", "authenticate": {}, "supportedNodes": ["n8n-nodes-base.zendesk", "n8n-nodes-base.zendeskTrigger"]}, {"name": "zendeskOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Zendesk OAuth2 API", "documentationUrl": "zendesk", "properties": [{"displayName": "Subdomain", "name": "subdomain", "type": "string", "default": "", "placeholder": "n8n", "description": "The subdomain of your Zendesk work environment", "required": true}, {"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "=https://{{$self[\"subdomain\"]}}.zendesk.com/oauth/authorizations/new", "description": "URL to get authorization code. Replace {SUBDOMAIN_HERE} with your subdomain.", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "=https://{{$self[\"subdomain\"]}}.zendesk.com/oauth/tokens", "description": "URL to get access token. Replace {SUBDOMAIN_HERE} with your subdomain.", "required": true}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": "", "required": true}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "read write"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "", "description": "For some services additional query parameters have to be set which can be defined here", "placeholder": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zendesk/zendesk.svg", "supportedNodes": ["n8n-nodes-base.zendesk", "n8n-nodes-base.zendeskTrigger"]}, {"name": "zohoOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Zoho OAuth2 API", "documentationUrl": "zoho", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "options", "options": [{"name": "https://accounts.zoho.com/oauth/v2/auth", "value": "https://accounts.zoho.com/oauth/v2/auth", "description": "For the EU, AU, and IN domains"}, {"name": "https://accounts.zoho.com.cn/oauth/v2/auth", "value": "https://accounts.zoho.com.cn/oauth/v2/auth", "description": "For the CN domain"}], "default": "https://accounts.zoho.com/oauth/v2/auth", "required": true}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "options", "options": [{"name": "AU - https://accounts.zoho.com.au/oauth/v2/token", "value": "https://accounts.zoho.com.au/oauth/v2/token"}, {"name": "CN - https://accounts.zoho.com.cn/oauth/v2/token", "value": "https://accounts.zoho.com.cn/oauth/v2/token"}, {"name": "EU - https://accounts.zoho.eu/oauth/v2/token", "value": "https://accounts.zoho.eu/oauth/v2/token"}, {"name": "IN - https://accounts.zoho.in/oauth/v2/token", "value": "https://accounts.zoho.in/oauth/v2/token"}, {"name": "US - https://accounts.zoho.com/oauth/v2/token", "value": "https://accounts.zoho.com/oauth/v2/token"}], "default": "https://accounts.zoho.com/oauth/v2/token", "required": true}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "ZohoCRM.modules.ALL,ZohoCRM.settings.all,ZohoCRM.users.all"}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "access_type=offline"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zoho/zoho.svg", "supportedNodes": ["n8n-nodes-base.zohoCrm"]}, {"name": "zoomApi", "displayName": "Zoom API", "documentationUrl": "zoom", "properties": [{"displayName": "On 1 June, 2023 Zoom will remove JWT App support. You will have to connect to Zoom using the Oauth2 auth method. <a target=\"_blank\" href=\"https://marketplace.zoom.us/docs/guides/build/jwt-app/jwt-faq/\">More details (zoom.us)</a>", "name": "notice", "type": "notice", "default": ""}, {"displayName": "JWT Token", "name": "accessToken", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.accessToken}}"}}}, "test": {"request": {"baseURL": "https://api.zoom.us/v2", "url": "/users/me"}}, "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zoom/zoom.svg", "supportedNodes": ["n8n-nodes-base.zoom"]}, {"name": "zoomOAuth2Api", "extends": ["oAuth2Api"], "displayName": "Zoom OAuth2 API", "documentationUrl": "zoom", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "hidden", "default": "https://zoom.us/oauth/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "hidden", "default": "https://zoom.us/oauth/token"}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": ""}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": ""}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "header"}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zoom/zoom.svg", "supportedNodes": ["n8n-nodes-base.zoom"]}, {"name": "zscalerZiaApi", "displayName": "Zscaler ZIA API", "documentationUrl": "zscalerzia", "httpRequestNode": {"name": "Zscaler ZIA", "docsUrl": "https://help.zscaler.com/zia/getting-started-zia-api", "apiBaseUrl": ""}, "properties": [{"displayName": "<PERSON><PERSON>", "name": "cookie", "type": "hidden", "typeOptions": {"expirable": true}, "default": ""}, {"displayName": "Base URL", "name": "baseUrl", "type": "string", "default": "", "placeholder": "e.g. zsapi.zscalerthree.net", "required": true}, {"displayName": "Username", "name": "username", "type": "string", "default": "", "required": true}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}, {"displayName": "Api Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": "", "required": true}], "authenticate": {"type": "generic", "properties": {"headers": {"Cookie": "={{$credentials.cookie}}"}}}, "test": {"request": {"url": "=https://{{$credentials.baseUrl}}/api/v1/authSettings/exemptedUrls"}}, "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Zscaler.svg", "supportedNodes": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Zulip API", "documentationUrl": "<PERSON><PERSON>", "properties": [{"displayName": "URL", "name": "url", "type": "string", "default": "", "placeholder": "https://yourZulipDomain.zulipchat.com"}, {"displayName": "Email", "name": "email", "type": "string", "placeholder": "<EMAIL>", "default": ""}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "default": ""}], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zulip/zulip.svg", "supportedNodes": ["n8n-nodes-base.zulip"]}, {"name": "anthropicApi", "displayName": "Anthropic", "documentationUrl": "anthropic", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Base URL", "name": "url", "type": "string", "default": "https://api.anthropic.com", "description": "Override the default base URL for the API"}], "authenticate": {"type": "generic", "properties": {"headers": {"x-api-key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials?.url}}", "url": "/v1/messages", "method": "POST", "headers": {"anthropic-version": "2023-06-01"}, "body": {"model": "claude-3-haiku-20240307", "messages": [{"role": "user", "content": "Hey"}], "max_tokens": 1}}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMChatAnthropic/anthropic.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.lmChatAnthropic"]}, {"name": "azureOpenAiApi", "displayName": "Azure Open AI", "documentationUrl": "azureopenai", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Resource Name", "name": "resourceName", "type": "string", "required": true, "default": ""}, {"displayName": "API Version", "name": "apiVersion", "type": "string", "required": true, "default": "2025-03-01-preview"}, {"displayName": "Endpoint", "name": "endpoint", "type": "string", "placeholder": "https://westeurope.api.cognitive.microsoft.com"}], "authenticate": {"type": "generic", "properties": {"headers": {"api-key": "={{$credentials.apiKey}}"}}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsAzureOpenAi/azure.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.embeddingsAzureOpenAi", "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi"]}, {"name": "azureEntraCognitiveServicesOAuth2Api", "displayName": "Azure Entra ID (Azure Active Directory) API", "extends": ["oAuth2Api"], "documentationUrl": "azureEntraCognitiveServicesOAuth2Api", "properties": [{"displayName": "Grant Type", "name": "grantType", "type": "hidden", "default": "authorizationCode"}, {"displayName": "Resource Name", "name": "resourceName", "type": "string", "required": true, "default": ""}, {"displayName": "API Version", "name": "apiVersion", "type": "string", "required": true, "default": "2025-03-01-preview"}, {"displayName": "Endpoint", "name": "endpoint", "type": "string", "placeholder": "https://westeurope.api.cognitive.microsoft.com"}, {"displayName": "Tenant ID", "name": "tenantId", "type": "string", "default": "common", "description": "Enter your Azure Tenant ID (Directory ID) or keep \"common\" for multi-tenant apps. Using a specific Tenant ID is generally recommended and required for certain authentication flows.", "placeholder": "e.g., xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx or common"}, {"displayName": "Authorization URL", "name": "authUrl", "type": "string", "default": "https://login.microsoftonline.com/$TENANT_ID/oauth2/authorize"}, {"displayName": "Access Token URL", "name": "accessTokenUrl", "type": "string", "default": "https://login.microsoftonline.com/$TENANT_ID/oauth2/token"}, {"displayName": "Client ID", "name": "clientId", "type": "string", "required": true, "default": "", "description": "Client ID obtained from the Azure AD App Registration"}, {"displayName": "Client Secret", "name": "clientSecret", "type": "string", "required": true, "typeOptions": {"password": true}, "default": "", "description": "Client Secret obtained from the Azure AD App Registration"}, {"displayName": "Additional Body Properties", "name": "additionalBodyProperties", "type": "hidden", "default": "{\"grant_type\": \"client_credentials\", \"resource\": \"https://cognitiveservices.azure.com/\"}"}, {"displayName": "Authentication", "name": "authentication", "type": "hidden", "default": "body"}, {"displayName": "Custom Scopes", "name": "customScopes", "type": "boolean", "default": false, "description": "Define custom scopes. You might need this if the default scopes are not sufficient or if you want to minimize permissions. Ensure you include \"openid\" and \"offline_access\"."}, {"displayName": "Auth URI Query Parameters", "name": "authQueryParameters", "type": "hidden", "default": "", "description": "For some services additional query parameters have to be set which can be defined here", "placeholder": ""}, {"displayName": "Enabled Scopes", "name": "enabledScopes", "type": "string", "displayOptions": {"show": {"customScopes": [true]}}, "default": "openid offline_access", "placeholder": "openid offline_access", "description": "Space-separated list of scopes to request."}, {"displayName": "<PERSON><PERSON>", "name": "scope", "type": "hidden", "default": "={{ $self.customScopes ? $self.enabledScopes : \"openid offline_access\"}}"}], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatAzureOpenAi/azure.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.lmChatAzureOpenAi"]}, {"name": "cohereApi", "displayName": "CohereApi", "documentationUrl": "cohere", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.cohere.ai", "url": "/v1/models?page_size=1"}}, "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsCohere/cohere.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsCohere/cohere.dark.svg"}, "supportedNodes": ["@n8n/n8n-nodes-langchain.embeddingsCohere", "@n8n/n8n-nodes-langchain.lmCohere"]}, {"name": "deepSeekApi", "displayName": "DeepSeek", "documentationUrl": "deepseek", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Base URL", "name": "url", "type": "hidden", "default": "https://api.deepseek.com"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{ $credentials.url }}", "url": "/models"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatDeepSeek/deepseek.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.lmChatDeepSeek"]}, {"name": "googlePalmApi", "displayName": "Google Gemini(PaLM) Api", "documentationUrl": "google", "properties": [{"displayName": "Host", "name": "host", "required": true, "type": "string", "default": "https://generativelanguage.googleapis.com"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.host}}/v1beta/models"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsGoogleGemini/google.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "@n8n/n8n-nodes-langchain.lmChatGoogleGemini"]}, {"name": "groqApi", "displayName": "Groq", "documentationUrl": "groq", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.groq.com/openai/v1", "url": "/models"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatGroq/groq.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.lmChatGroq"]}, {"name": "huggingFace<PERSON>pi", "displayName": "HuggingFaceApi", "documentationUrl": "huggingface", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api-inference.huggingface.co", "url": "/models/gpt2"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsHuggingFaceInference/huggingface.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.embeddingsHuggingFaceInference", "@n8n/n8n-nodes-langchain.lmOpenHuggingFaceInference"]}, {"name": "motorheadApi", "displayName": "MotorheadApi", "documentationUrl": "motorhead", "properties": [{"displayName": "Host", "name": "host", "required": true, "type": "string", "default": "https://api.getmetal.io/v1"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Client ID", "name": "clientId", "type": "string", "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"x-metal-client-id": "={{$credentials.clientId}}", "x-metal-api-key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.host}}/keys/current"}}, "icon": "fa:file-export", "iconColor": "black", "supportedNodes": ["@n8n/n8n-nodes-langchain.memoryMotorhead"]}, {"name": "mil<PERSON>s<PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "documentationUrl": "mil<PERSON><PERSON>", "properties": [{"displayName": "Base URL", "name": "baseUrl", "required": true, "type": "string", "default": "http://localhost:19530"}, {"displayName": "Username", "name": "username", "type": "string", "default": ""}, {"displayName": "Password", "name": "password", "type": "string", "typeOptions": {"password": true}, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.username}}:{{$credentials.password}}"}}}, "test": {"request": {"baseURL": "={{ $credentials.baseUrl }}", "url": "/v1/vector/collections", "method": "GET"}}, "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreMilvus/milvus-icon-black.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreMilvus/milvus-icon-white.svg"}, "supportedNodes": ["@n8n/n8n-nodes-langchain.vectorStoreMilvus"]}, {"name": "mistralCloudApi", "displayName": "Mistral Cloud API", "documentationUrl": "mistral", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.mistral.ai/v1", "url": "/models", "method": "GET"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsMistralCloud/mistral.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.embeddingsMistralCloud", "@n8n/n8n-nodes-langchain.lmChatMistralCloud"]}, {"name": "o<PERSON>ma<PERSON><PERSON>", "displayName": "Ollama", "documentationUrl": "ollama", "properties": [{"displayName": "Base URL", "name": "baseUrl", "required": true, "type": "string", "default": "http://localhost:11434"}], "test": {"request": {"baseURL": "={{ $credentials.baseUrl }}", "url": "/", "method": "GET"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/embeddings/EmbeddingsOllama/ollama.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.embeddingsOllama", "@n8n/n8n-nodes-langchain.lmChatOllama", "@n8n/n8n-nodes-langchain.lmOllama"]}, {"name": "openRouterApi", "displayName": "OpenRouter", "documentationUrl": "openrouter", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Base URL", "name": "url", "type": "hidden", "default": "https://openrouter.ai/api/v1"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{ $credentials.url }}", "url": "/key"}}, "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatOpenRouter/openrouter.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatOpenRouter/openrouter.dark.svg"}, "supportedNodes": ["@n8n/n8n-nodes-langchain.lmChatOpenRouter"]}, {"name": "pineconeApi", "displayName": "PineconeApi", "documentationUrl": "pinecone", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Api-Key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://api.pinecone.io/indexes", "headers": {"accept": "application/json; charset=utf-8"}}}, "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStorePinecone/pinecone.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStorePinecone/pinecone.dark.svg"}, "supportedNodes": ["@n8n/n8n-nodes-langchain.vectorStorePinecone", "@n8n/n8n-nodes-langchain.vectorStorePineconeInsert", "@n8n/n8n-nodes-langchain.vectorStorePineconeLoad"]}, {"name": "qdrantApi", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentationUrl": "https://docs.n8n.io/integrations/builtin/credentials/qdrant/", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": false, "default": ""}, {"displayName": "Qdrant URL", "name": "qdrantUrl", "type": "string", "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"api-key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.qdrantUrl}}", "url": "/collections"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreQdrant/qdrant.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.vectorStoreQdrant"]}, {"name": "searXngApi", "displayName": "SearXNG", "documentationUrl": "searxng", "properties": [{"displayName": "API URL", "name": "apiUrl", "type": "string", "default": "", "required": true}], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/tools/ToolSearXng/searXng.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.toolSearXng"]}, {"name": "serp<PERSON><PERSON>", "displayName": "SerpAPI", "documentationUrl": "serp", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"api_key": "={{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "https://serpapi.com", "url": "/account.json "}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/tools/ToolSerpApi/serpApi.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.toolSerpApi"]}, {"name": "wolfram<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentationUrl": "wolframalpha", "properties": [{"displayName": "App ID", "name": "appId", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"qs": {"api_key": "={{$credentials.appId}}"}}}, "test": {"request": {"baseURL": "https://api.wolframalpha.com/v1", "url": "=/simple", "qs": {"i": "How much is 1 1", "appid": "={{$credentials.appId}}"}}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/tools/ToolWolframAlpha/wolfram-alpha.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.toolWolframAlpha"]}, {"name": "xAiApi", "displayName": "xAi", "documentationUrl": "xai", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}, {"displayName": "Base URL", "name": "url", "type": "hidden", "default": "https://api.x.ai/v1"}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{ $credentials.url }}", "url": "/models"}}, "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatXAiGrok/logo.dark.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatXAiGrok/logo.svg"}, "supportedNodes": ["@n8n/n8n-nodes-langchain.lmChatXAiGrok"]}, {"name": "xataApi", "displayName": "Xata <PERSON>", "documentationUrl": "xata", "properties": [{"displayName": "Database Endpoint", "name": "databaseEndpoint", "required": true, "type": "string", "default": "", "placeholder": "https://{workspace}.{region}.xata.sh/db/{database}"}, {"displayName": "Branch", "name": "branch", "required": true, "type": "string", "default": "main"}, {"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": true, "default": ""}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "=Bearer {{$credentials.apiKey}}"}}}, "test": {"request": {"baseURL": "={{$credentials.databaseEndpoint}}:{{$credentials.branch}}"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/memory/MemoryXata/xata.svg", "supportedNodes": ["@n8n/n8n-nodes-langchain.memoryXata"]}, {"name": "zepApi", "displayName": "Zep Api", "documentationUrl": "zep", "properties": [{"displayName": "API Key", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "typeOptions": {"password": true}, "required": false, "default": ""}, {"displayName": "Cloud", "description": "Whether you are adding credentials for Zep Cloud instead of Zep Open Source", "name": "cloud", "type": "boolean", "default": false}, {"displayName": "API URL", "name": "apiUrl", "required": false, "type": "string", "default": "http://localhost:8000", "displayOptions": {"show": {"cloud": [false]}}}], "authenticate": {"type": "generic", "properties": {"headers": {"Authorization": "={{$credentials.apiKey && !$credentials.cloud ? \"Bearer \" + $credentials.apiKey : \"Api-Key \" + $credentials.apiKey }}"}}}, "test": {"request": {"baseURL": "={{!$credentials.cloud ? $credentials.apiUrl : \"https://api.getzep.com\"}}", "url": "={{!$credentials.cloud ? \"/api/v1/collection\" : \"/api/v2/collections\"}}"}}, "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/memory/MemoryZep/zep.png", "supportedNodes": ["@n8n/n8n-nodes-langchain.memoryZep", "@n8n/n8n-nodes-langchain.vectorStoreZep", "@n8n/n8n-nodes-langchain.vectorStoreZepInsert", "@n8n/n8n-nodes-langchain.vectorStoreZepLoad"]}]