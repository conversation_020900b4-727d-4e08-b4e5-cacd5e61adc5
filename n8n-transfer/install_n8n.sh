# n8n 一键安装脚本 (v1.105.3)
# 将此脚本复制到终端中执行即可完成安装

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() { echo -e "${BLUE}[信息]${NC} $1"; }
success() { echo -e "${GREEN}[成功]${NC} $1"; }
warning() { echo -e "${YELLOW}[警告]${NC} $1"; }
error() { echo -e "${RED}[错误]${NC} $1"; }

# 检测必要条件
check_prerequisites() {
  info "检查安装必要条件..."

  # 检查Docker是否安装
  if ! command -v docker &> /dev/null; then
    error "未检测到Docker。请先安装Docker Desktop，然后再运行此脚本。"
    error "您可以从 https://www.docker.com/products/docker-desktop/ 下载Docker Desktop。"
    return 1
  fi

  # 检查n8n-transfer文件夹是否存在
  if [ ! -d ~/Desktop/n8n-transfer ]; then
    error "在桌面上找不到n8n-transfer文件夹。"
    error "请确保您已将n8n-transfer文件夹复制到Mac的桌面上。"
    return 1
  fi

  # 检查Docker镜像文件是否存在（支持两种文件名）
  if [ -f ~/Desktop/n8n-transfer/n8n-custom-v1.105.3.tar ]; then
    DOCKER_IMAGE_FILE="n8n-custom-v1.105.3.tar"
    DOCKER_IMAGE_NAME="n8n-custom-updated:v1.105.3"
  elif [ -f ~/Desktop/n8n-transfer/n8n-custom.tar ]; then
    DOCKER_IMAGE_FILE="n8n-custom.tar"
    DOCKER_IMAGE_NAME="n8n-docker-custom-n8n:latest"
  else
    error "找不到Docker镜像文件。"
    error "请确保n8n-transfer文件夹中包含 n8n-custom-v1.105.3.tar 或 n8n-custom.tar 文件。"
    return 1
  fi

  # 检查container-data文件夹是否存在
  if [ ! -d ~/Desktop/n8n-transfer/container-data ]; then
    error "找不到数据文件夹(container-data)。"
    error "请确保n8n-transfer文件夹中包含container-data文件夹。"
    return 1
  fi

  success "所有必要条件已满足，开始安装..."
  info "使用镜像文件: $DOCKER_IMAGE_FILE"
  return 0
}

# 设置Docker环境
setup_environment() {
  info "设置Docker环境..."
  
  # 创建数据目录
  mkdir -p ~/.docker
  
  # 设置Docker环境变量以避免连接问题
  export DOCKER_HOST=unix:///var/run/docker.sock
  
  # 将环境变量添加到.zshrc文件中（如果不存在）
  if ! grep -q "DOCKER_HOST=unix:///var/run/docker.sock" ~/.zshrc; then
    echo 'export DOCKER_HOST=unix:///var/run/docker.sock' >> ~/.zshrc
    success "已将Docker环境变量添加到~/.zshrc文件中"
  fi
  
  success "Docker环境设置完成！"
}

# 导入Docker镜像
import_docker_image() {
  info "导入Docker镜像(可能需要几分钟)..."

  # 检查镜像是否已存在
  if docker images | grep -q "$DOCKER_IMAGE_NAME"; then
    warning "检测到镜像已存在，跳过导入步骤"
  else
    docker load -i ~/Desktop/n8n-transfer/$DOCKER_IMAGE_FILE
    if [ $? -ne 0 ]; then
      error "导入Docker镜像失败，请检查Docker是否正常运行"
      return 1
    fi
    success "Docker镜像导入成功！"
  fi
  return 0
}

# 复制数据并设置权限
copy_data() {
  info "复制n8n完整数据(包括工作流文件)..."

  # 备份现有数据（如果存在）
  if [ -d ~/.docker ]; then
    warning "检测到现有数据目录，创建备份..."
    mv ~/.docker ~/.docker.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null
  fi

  # 创建新的数据目录
  mkdir -p ~/.docker

  # 复制完整数据目录
  info "复制完整数据目录(包括 .n8n, pubmed-search, OCR 等)..."
  cp -R ~/Desktop/n8n-transfer/container-data/* ~/.docker/ 2>/dev/null

  # 如果失败，则使用sudo
  if [ $? -ne 0 ]; then
    warning "复制数据需要管理员权限，可能会提示您输入密码"
    sudo cp -R ~/Desktop/n8n-transfer/container-data/* ~/.docker/
    sudo chmod -R 755 ~/.docker
    sudo chown -R $(whoami) ~/.docker
  fi

  # 验证关键数据是否正确复制
  if [ -f ~/.docker/.n8n/database.sqlite ]; then
    source_size=$(ls -la ~/Desktop/n8n-transfer/container-data/.n8n/database.sqlite | awk '{print $5}')
    dest_size=$(ls -la ~/.docker/.n8n/database.sqlite | awk '{print $5}')

    if [ "$source_size" = "$dest_size" ]; then
      success "数据库文件大小匹配，数据复制成功！"
    else
      warning "数据库文件大小不匹配，尝试重新复制..."
      sudo rm -rf ~/.docker
      sudo mkdir -p ~/.docker
      sudo cp -R ~/Desktop/n8n-transfer/container-data/* ~/.docker/
      sudo chmod -R 777 ~/.docker
      sudo chown -R $(whoami) ~/.docker
    fi
  else
    error "数据库文件不存在，复制可能失败"
    return 1
  fi

  # 验证工作流文件是否存在
  if [ -d ~/.docker/pubmed-search ]; then
    success "工作流文件目录 pubmed-search 复制成功！"
  fi

  if [ -d ~/.docker/OCR ]; then
    success "OCR 文件目录复制成功！"
  fi

  success "完整数据复制完成！"
  return 0
}

# 启动n8n容器
start_container() {
  info "启动n8n容器..."

  # 停止并删除已存在的容器
  docker stop n8n-custom 2>/dev/null
  docker rm n8n-custom 2>/dev/null

  # 启动新容器
  docker run -d --name n8n-custom \
    -p 5678:5678 \
    -v ~/.docker:/data \
    --restart=unless-stopped \
    $DOCKER_IMAGE_NAME

  if [ $? -ne 0 ]; then
    error "启动容器失败"
    return 1
  fi

  success "n8n容器已成功启动！"
  return 0
}

# 验证安装
verify_installation() {
  info "等待n8n启动中(可能需要30秒)..."
  sleep 5
  
  # 检查容器是否运行
  if ! docker ps | grep -q "n8n-custom"; then
    error "n8n容器未在运行，请检查日志"
    docker logs n8n-custom
    return 1
  fi
  
  # 显示容器日志
  docker logs n8n-custom
  
  success "=== n8n安装完成! ==="
  info "您现在可以通过浏览器访问: http://localhost:5678"
  info "如果页面为空白，请等待30秒后刷新，或尝试清除浏览器缓存"
  
  # 尝试自动打开浏览器
  sleep 5
  open http://localhost:5678 2>/dev/null || info "请手动打开浏览器访问 http://localhost:5678"
  return 0
}

# 主函数
main() {
  echo -e "${GREEN}========================================${NC}"
  echo -e "${GREEN}      n8n 一键安装脚本 (v1.105.3)      ${NC}"
  echo -e "${GREEN}========================================${NC}"
  
  check_prerequisites || return 1
  setup_environment
  import_docker_image || return 1
  copy_data || return 1
  start_container || return 1
  verify_installation
}

# 执行主函数
main