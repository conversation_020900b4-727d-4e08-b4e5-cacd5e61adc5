# n8n Docker 容器迁移指南 (Windows 到 Mac) (v1.93.0)

本指南详细说明如何将 n8n v1.93.0 Docker 容器（含 Pandoc 3.1.13 和 Python 3.12 及其依赖包）从 Windows 电脑完整迁移到 Mac 电脑，并添加持久化存储。

## 迁移背景

原始 n8n 容器配置:
- 容器内安装了 Pandoc 3.1.13
- 容器内安装了 Python 3.12 及 pandas、openpyxl 依赖包
- 容器内的数据存储在 `/data` 路径
- 容器**没有**将数据目录挂载到宿主机（数据不持久化）

## 第一阶段：在 Windows 源电脑上导出

### 1. 创建迁移文件夹
```powershell
mkdir C:\n8n-transfer
```

### 2. 导出自定义 Docker 镜像
```powershell
docker save -o C:\n8n-transfer\n8n-custom.tar n8n-docker-custom-n8n:latest
```

### 3. 从容器内部导出数据（关键步骤）
```powershell
docker cp n8n-custom:/data C:\n8n-transfer\container-data
```
这一步将容器内存储的所有 n8n 数据（工作流、凭证等）复制到宿主机上。

### 4. 将整个 C:\n8n-transfer 文件夹复制到传输介质
将该文件夹复制到U盘、外接硬盘或通过云存储/网络共享传输到 Mac 电脑。

## 第二阶段：在 Mac 目标电脑上导入

### 1. 准备工作
确保 Mac 电脑已安装并启动 Docker Desktop。如果还没有，请从 [Docker 官网](https://www.docker.com/products/docker-desktop/) 下载并安装。

### 2. 将 n8n-transfer 文件夹复制到 Mac
例如复制到桌面或其他位置。我们假设文件被复制到 `~/Desktop/n8n-transfer`。

### 3. 打开终端并设置环境
```bash
# 创建用于存储数据的目录
mkdir -p ~/.docker

# 预防性设置 Docker 环境变量（强烈建议）
export DOCKER_HOST=unix:///var/run/docker.sock
```

> **关于权限的注意事项：** 如果在后续步骤中遇到 "Permission denied" 错误，可以使用以下命令进行一次性权限设置（会要求输入密码）：
```bash
sudo chown -R $(whoami) ~/.docker
```

> **提示：** 为了避免未来可能出现的 Docker 连接问题（如 `Failed to initialize: protocol not available`），建议将环境变量设置添加到您的 shell 配置文件中以永久生效：
```bash
echo 'export DOCKER_HOST=unix:///var/run/docker.sock' >> ~/.zshrc

# 加载新配置
source ~/.zshrc
```

### 4. 导入 Docker 镜像
```bash
# 导入 Docker 镜像
# 注意：这个步骤不需要 sudo，因为 Docker 命令已经具有适当的权限
docker load -i ~/Desktop/n8n-transfer/n8n-custom.tar
```

### 5. 复制容器数据到目标目录

```bash
# 复制数据
cp -R ~/Desktop/n8n-transfer/container-data/.n8n ~/.docker/ 2>/dev/null || {
  # 如果复制失败（权限问题），则使用 sudo 进行一次性操作
  echo "需要管理员权限，请输入密码"
  sudo rm -rf ~/.docker/.n8n
  sudo mkdir -p ~/.docker/.n8n
  sudo cp -R ~/Desktop/n8n-transfer/container-data/.n8n/* ~/.docker/.n8n/
  sudo chmod -R 755 ~/.docker/.n8n
  sudo chown -R $(whoami) ~/.docker
  echo "数据复制完成，权限已设置"
}
```

> **提示：** 上面的脚本会先尝试不使用 sudo 复制文件，只有在遇到权限问题时才会要求您输入密码，这样可以减少输入密码的次数。

> **重要数据验证：** 为确保您的工作流数据正确复制，请验证数据库文件大小：
```bash
ls -la ~/Desktop/n8n-transfer/container-data/.n8n/database.sqlite
ls -la ~/.docker/.n8n/database.sqlite
```
> 确保两个文件大小相同。如果文件缺失或大小不同，请重复上述步骤。

### 6. 启动新容器（带持久化存储）
```bash
# 启动容器
docker run -d --name n8n-custom \
  -p 5678:5678 \
  -v ~/.docker:/data \
  --restart=unless-stopped \
  n8n-docker-custom-n8n:latest
```

> **注意：** 如果使用 Mac M1/M2/M3 (ARM) 处理器，可能收到平台不匹配警告 (`platform linux/amd64 does not match the detected host platform`)。这不会影响功能，Docker 会自动处理这种架构差异。

这个命令中的 `-v ~/.docker:/data` 参数将宿主机目录挂载到容器的 `/data` 路径，实现数据持久化。

## 迁移流程概要

为确保成功迁移，请按照以下顺序执行操作：

1. 安装并完全启动 Docker Desktop
2. 设置 Docker 环境变量（如出现连接问题）
3. 创建目录并导入 Docker 镜像
4. 验证数据复制是否成功（特别是数据库大小）
5. 启动容器
6. 验证能否通过浏览器访问 n8n

## 第三阶段：验证

### 1. 检查容器是否正常运行
```bash
docker ps
```

### 2. 查看容器日志
```bash
docker logs -f n8n-custom
```
等待直到看到 "Editor is now accessible" 消息

### 3. 访问 n8n 界面
在浏览器中打开 http://localhost:5678

### 4. 验证 Pandoc 和 Python 功能
```bash
# 检查 Pandoc 版本 (注意：docker 命令不需要 sudo)
docker exec n8n-custom pandoc --version

# 检查 Python 及其包
docker exec n8n-custom python -c "import sys; import pandas; import openpyxl; print(f'Python {sys.version}\\nPandas {pandas.__version__}\\nOpenPyxl {openpyxl.__version__}')"
```

## Mac 系统特有注意事项

### Apple Silicon (M1/M2) 芯片兼容性
如果您使用的是搭载 Apple Silicon 芯片的 Mac：

1. 确保 Docker Desktop 已启用 Rosetta 2 兼容模式（如果镜像是为 x86_64 架构构建的）
2. 或者重新构建兼容 ARM64 架构的 Docker 镜像

### 文件路径差异
- Mac 系统使用 Unix 风格的路径（如 `/Users/<USER>/`），而非 Windows 的 `C:\Users\<USER>\`
- Mac 上的隐藏文件夹以点开头（如 `.docker`），可以在 Finder 中按 `Command + Shift + .` 显示/隐藏

### 权限管理
如果遇到权限问题，请执行以下命令：
```bash
# 赋予目录适当的权限
sudo chown -R $(whoami) ~/.docker
sudo chmod -R 777 ~/.docker/.n8n

# 如果使用不同位置，请相应调整命令
```

### Docker Desktop 设置
1. 打开 Docker Desktop
2. 点击 ⚙️ Settings (偏好设置)
3. 进入 Resources → File Sharing
4. 确保包含 `~/.docker` 路径（或您选择的其他路径）

## 迁移优势

1. **数据持久化**：新设置将 n8n 数据存储在宿主机上，即使容器被删除也不会丢失数据
2. **文件访问**：通过挂载，n8n 的 "Execute Command" 节点可以使用 `cd data` 命令访问宿主机上的数据目录
3. **完整迁移**：保留了所有已安装的依赖项（Pandoc、Python 及其包）
4. **无需重建**：避免了重新配置环境的复杂过程

## 常见问题

### Docker 连接问题
如果遇到 "Failed to initialize: protocol not available" 错误，尝试以下解决方案：

1. 确保 Docker Desktop 应用程序已完全启动
2. 设置 Docker 环境变量：
   ```bash
   export DOCKER_HOST=unix:///var/run/docker.sock
   ```
3. 重新打开终端或添加到 `~/.zshrc` 永久生效

### 数据迁移问题
如果启动容器后发现工作流数据丢失：

1. 检查数据库文件大小是否一致：
   ```bash
   ls -la ~/Desktop/n8n-transfer/container-data/.n8n/database.sqlite
   ls -la ~/.docker/.n8n/database.sqlite
   ```
2. 如果不一致，请停止并删除容器，然后重新复制数据（使用 sudo）：
   ```bash
   docker stop n8n-custom && docker rm n8n-custom
   sudo rm -rf ~/.docker/.n8n
   sudo mkdir -p ~/.docker/.n8n
   sudo cp -R ~/Desktop/n8n-transfer/container-data/.n8n/* ~/.docker/.n8n/
   sudo chmod -R 777 ~/.docker/.n8n
   ```
3. 重新启动容器

## 详细故障排除指南

### Mac 系统权限问题的完整解决方案

如果你在迁移过程中遇到了“`Permission denied`”错误，或者在访问 n8n 界面时只看到空白页面，请按照以下完整步骤操作（这将需要输入一次管理员密码）：

#### 1. 停止并移除所有现有的 n8n 容器

```bash
# 停止并删除现有的 n8n 容器
docker stop n8n-custom 2>/dev/null
docker rm n8n-custom 2>/dev/null
```

#### 2. 彻底清理并正确设置数据目录的权限

```bash
# 彻底清理数据目录
sudo rm -rf ~/.docker/.n8n
sudo mkdir -p ~/.docker/.n8n

# 复制数据
sudo cp -R ~/Desktop/n8n-transfer/container-data/.n8n/* ~/.docker/.n8n/

# 设置充分的权限 - 这一步非常重要
sudo chmod -R 777 ~/.docker/.n8n
sudo chown -R $(whoami) ~/.docker
```

#### 3. 启动新容器并检查日志

```bash
# 启动容器
docker run -d --name n8n-custom -p 5678:5678 -v ~/.docker:/data --restart=unless-stopped n8n-docker-custom-n8n:latest

# 等待几秒并查看日志
sleep 5 && docker logs n8n-custom
```

#### 4. 验证访问

在浏览器中访问 http://localhost:5678

如果页面仍然为空，请尝试：
1. 清除浏览器缓存
2. 使用隐私模式打开
3. 等待 30 秒后刷新页面

#### 5. 查看容器日志获取更多信息

```bash
docker logs -f n8n-custom
```

当看到 "n8n ready on 0.0.0.0, port 5678" 消息时，表示 n8n 已成功启动。

### 端口冲突
如果 5678 端口被占用，可以更改端口映射：
```bash
docker run -d --name n8n-custom -p 5680:5678 -v ~/.docker:/data --restart=unless-stopped n8n-docker-custom-n8n:latest
```
然后通过 http://localhost:5680 访问。

### 数据安全
定期备份 `~/.docker` 目录以防数据丢失：
```bash
# 创建备份
tar -czf ~/n8n-data-backup-$(date +%Y%m%d).tar.gz ~/.docker
```

### SELinux/权限问题
如果您在容器中遇到权限错误，可能需要添加 `:Z` 到卷挂载：
```bash
docker run -d --name n8n-custom -p 5678:5678 -v ~/.docker:/data:Z --restart=unless-stopped n8n-docker-custom-n8n:latest
```
